import { <PERSON>, FileText, <PERSON><PERSON><PERSON>, Pin, Plus, Printer, RefreshCw, Search, Sheet, Trash } from 'lucide-react';
import { Icon } from 'next/dist/lib/metadata/types/metadata-types';
import React, { useState } from 'react';
import { sampleExcel, triggerFileInput, importFile, validation } from '@/components/arito/arito-handle-excel';
import ExcelUploadPopup from '@/components/arito/arito-excel-upload-popup';
import ExcelImportPopup from '@/components/arito/arito-excel-upload-popup';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onViewClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  className?: string;
  data: any[];
}

function onUpdate(data: any[]) {
  console.log(data);
}

export function ActionBar({
  className,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onViewClick,
  isEditDisabled = true,
  isViewDisabled = true,
  data
}: Props) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  const handleUpload = (file: File) => {
    // Handle the file upload logic here
    console.log(file);
    onUpdate([]);
    handleClosePopup();
  };

  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Bảng tính khấu hao TSCĐ</h1>}>
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} variant='primary' />
      <AritoActionButton title='In ấn' icon={Printer} onClick={() => {}} disabled={isEditDisabled} />
      <AritoActionButton
        title='Refresh'
        variant='destructive'
        icon={RefreshCw}
        onClick={() => {
          window.location.reload();
        }}
        disabled={isEditDisabled}
      />
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={() => {}} disabled={isViewDisabled} />
      <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={() => {}} disabled={isViewDisabled} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
}
