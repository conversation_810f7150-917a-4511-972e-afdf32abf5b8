import { UseSearchFieldStatesReturn } from '@/features/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-theo-khach-hang/hooks';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { khachHangSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { KhachHang } from '@/types/schemas';

interface DetailsTabProps {
  searchFieldStates: UseSearchFieldStatesReturn;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <div className='flex-1'>
            <div className='flex items-center'>
              <Label className='w-40'>Mã khách hàng:</Label>
              <div>
                <SearchField<KhachHang>
                  type='text'
                  columnDisplay='customer_code'
                  displayRelatedField='customer_name'
                  searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                  searchColumns={khachHangSearchColumns}
                  value={searchFieldStates.customer?.customer_code || ''}
                  relatedFieldValue={searchFieldStates.customer?.customer_name || ''}
                />
              </div>
            </div>
            <div className='flex gap-1'>
              <FormField
                name='mau_bc'
                label='Mẫu báo cáo'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: 'TC', label: 'Mẫu tiền chuẩn' },
                  { value: 'NT', label: 'Mẫu ngoại tệ' }
                ]}
                className='w-[400px]'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
