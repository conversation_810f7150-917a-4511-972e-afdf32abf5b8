import { useState, useCallback } from 'react';
import { BangKeChiTietTraTienChoHoaDonSearchFormValues } from '@/types/schemas/bang-ke-chi-tiet-tra-tien-cho-cac-hoa-don.type';
import { useBangKeChiTietTraTienChoHoaDon } from './useBangKeChiTietTraTienChoHoaDon';
import { useTableData } from './useTableData';

export interface UseReportDataReturn {
  // Data and loading states
  data: any[];
  isLoading: boolean;
  error: Error | null;

  // Table data
  tables: any[];

  // Search and dialog states
  searchParams: BangKeChiTietTraTienChoHoaDonSearchFormValues | null;
  showTable: boolean;
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;

  // Event handlers
  handleInitialSearch: (data: BangKeChiTietTraTienChoHoaDonSearchFormValues) => Promise<void>;
  handleInitialSearchClose: () => void;
  handleSearchClick: () => void;
  handleRefreshClick: () => void;
  handleRowClick: (params: any) => void;

  // Print template handlers
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (template: any) => void;

  // Export and other handlers
  handleExportDataClick: () => void;
  handleFixedColumnsClick: () => void;
}

export function useReportData(): UseReportDataReturn {
  const [searchParams, setSearchParams] = useState<BangKeChiTietTraTienChoHoaDonSearchFormValues | null>(null);
  const [showTable, setShowTable] = useState(false);
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);

  // Initialize with empty search params to avoid undefined
  const { data, isLoading, error, fetchData } = useBangKeChiTietTraTienChoHoaDon(searchParams || {});
  const { tables, handleRowClick } = useTableData(data, searchParams);

  const handleInitialSearch = useCallback(
    async (searchData: BangKeChiTietTraTienChoHoaDonSearchFormValues) => {
      setSearchParams(searchData);
      setInitialSearchDialogOpen(false);
      setShowTable(true); // Show table immediately
      try {
        await fetchData(searchData);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    },
    [fetchData]
  );

  const handleInitialSearchClose = useCallback(() => {
    setInitialSearchDialogOpen(false);
  }, []);

  const handleSearchClick = useCallback(() => {
    setInitialSearchDialogOpen(true);
  }, []);

  const handleRefreshClick = useCallback(async () => {
    if (searchParams) {
      try {
        await fetchData(searchParams);
      } catch (error) {
        console.error('Error refreshing data:', error);
      }
    }
  }, [fetchData, searchParams]);

  const handleEditPrintTemplateClick = useCallback(() => {
    setEditPrintTemplateDialogOpen(true);
  }, []);

  const handleClosePrintTemplateDialog = useCallback(() => {
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleSavePrintTemplate = useCallback((template: any) => {
    console.log('Save print template:', template);
    setEditPrintTemplateDialogOpen(false);
    // TODO: Implement save template functionality
  }, []);

  const handleExportDataClick = useCallback(() => {
    console.log('Export data clicked');
    // TODO: Implement export functionality
  }, []);

  const handleFixedColumnsClick = useCallback(() => {
    console.log('Fixed columns clicked');
    // TODO: Implement fixed columns functionality
  }, []);

  return {
    // Data and loading states
    data,
    isLoading,
    error,

    // Table data
    tables,

    // Search and dialog states
    searchParams,
    showTable,
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,

    // Event handlers
    handleInitialSearch,
    handleInitialSearchClose,
    handleSearchClick,
    handleRefreshClick,
    handleRowClick,

    // Print template handlers
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,

    // Export and other handlers
    handleExportDataClick,
    handleFixedColumnsClick
  };
}
