import { useState, useCallback } from 'react';
import {
  BangTinhPhanBoCCDCItem,
  BangTinhPhanBoCCDCResponse,
  BangTinhPhanBoCCDCSearchFormValues,
  UseBangTinhPhanBoCCDCReturn
} from '@/types/schemas';
import api from '@/lib/api';

/**
 * Generate mock data for CCDC allocation calculation
 */
const generateMockData = (): BangTinhPhanBoCCDCItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_cc: 'CC001',
      ten_cc: '<PERSON><PERSON><PERSON> tính xách tay Dell Latitude 5520',
      ngay_kh1: '2024-01-01',
      so_ky_kh: 36,
      ma_bp: 'BP001',
      ten_bp: 'Phòng Kế toán',
      ma_lcc: 'LCC001',
      ten_lcc: 'Thiết bị văn phòng',
      ly_do: '<PERSON><PERSON> bổ khấu hao theo kỳ',
      nguyen_gia: 25000000,
      gt_da_kh: 8333333,
      gt_cl: 16666667,
      gt_kh_ky: 694444,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV001',
      ma_phi: 'PH001',
      ma_unit: 'CN',
      so_luong: 1,
      dvt: 'Cái',
      ngay_kh_kt: '2027-01-01',
      gt_cl_dk: 17361111,
      gt_da_kh_dk: 7638889,
      gt_da_kh_lk: 8333333,
      gt_cl_ck: 16666667
    },
    {
      id: '2',
      stt: 2,
      ma_cc: 'CC002',
      ten_cc: 'Máy in laser HP LaserJet Pro M404dn',
      ngay_kh1: '2024-02-01',
      so_ky_kh: 24,
      ma_bp: 'BP002',
      ten_bp: 'Phòng Hành chính',
      ma_lcc: 'LCC001',
      ten_lcc: 'Thiết bị văn phòng',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 8000000,
      gt_da_kh: 2333333,
      gt_cl: 5666667,
      gt_kh_ky: 333333,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV002',
      ma_phi: 'PH002',
      ma_unit: 'CN',
      so_luong: 1,
      dvt: 'Cái',
      ngay_kh_kt: '2026-02-01',
      gt_cl_dk: 6000000,
      gt_da_kh_dk: 2000000,
      gt_da_kh_lk: 2333333,
      gt_cl_ck: 5666667
    },
    {
      id: '3',
      stt: 3,
      ma_cc: 'CC003',
      ten_cc: 'Bàn làm việc gỗ công nghiệp 1m6',
      ngay_kh1: '2024-01-15',
      so_ky_kh: 60,
      ma_bp: 'BP001',
      ten_bp: 'Phòng Kế toán',
      ma_lcc: 'LCC002',
      ten_lcc: 'Nội thất văn phòng',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 3200000,
      gt_da_kh: 853333,
      gt_cl: 2346667,
      gt_kh_ky: 53333,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV001',
      ma_phi: 'PH001',
      ma_unit: 'CN',
      so_luong: 2,
      dvt: 'Cái',
      ngay_kh_kt: '2029-01-15',
      gt_cl_dk: 2400000,
      gt_da_kh_dk: 800000,
      gt_da_kh_lk: 853333,
      gt_cl_ck: 2346667
    },
    {
      id: '4',
      stt: 4,
      ma_cc: 'CC004',
      ten_cc: 'Điều hòa Daikin 2HP inverter',
      ngay_kh1: '2024-03-01',
      so_ky_kh: 48,
      ma_bp: 'BP003',
      ten_bp: 'Phòng Kinh doanh',
      ma_lcc: 'LCC003',
      ten_lcc: 'Thiết bị điện lạnh',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 15000000,
      gt_da_kh: 3750000,
      gt_cl: 11250000,
      gt_kh_ky: 312500,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV003',
      ma_phi: 'PH003',
      ma_unit: 'CN',
      so_luong: 1,
      dvt: 'Cái',
      ngay_kh_kt: '2028-03-01',
      gt_cl_dk: 11562500,
      gt_da_kh_dk: 3437500,
      gt_da_kh_lk: 3750000,
      gt_cl_ck: 11250000
    },
    {
      id: '5',
      stt: 5,
      ma_cc: 'CC005',
      ten_cc: 'Tủ tài liệu sắt 4 ngăn',
      ngay_kh1: '2024-01-10',
      so_ky_kh: 72,
      ma_bp: 'BP002',
      ten_bp: 'Phòng Hành chính',
      ma_lcc: 'LCC002',
      ten_lcc: 'Nội thất văn phòng',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 2500000,
      gt_da_kh: 625000,
      gt_cl: 1875000,
      gt_kh_ky: 34722,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV002',
      ma_phi: 'PH002',
      ma_unit: 'CN',
      so_luong: 3,
      dvt: 'Cái',
      ngay_kh_kt: '2030-01-10',
      gt_cl_dk: 1909722,
      gt_da_kh_dk: 590278,
      gt_da_kh_lk: 625000,
      gt_cl_ck: 1875000
    },
    {
      id: '6',
      stt: 6,
      ma_cc: 'CC006',
      ten_cc: 'Máy photocopy Canon iR2625i',
      ngay_kh1: '2024-02-15',
      so_ky_kh: 30,
      ma_bp: 'BP004',
      ten_bp: 'Phòng Nhân sự',
      ma_lcc: 'LCC001',
      ten_lcc: 'Thiết bị văn phòng',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 35000000,
      gt_da_kh: 9333333,
      gt_cl: 25666667,
      gt_kh_ky: 1166667,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV004',
      ma_phi: 'PH004',
      ma_unit: 'CN',
      so_luong: 1,
      dvt: 'Cái',
      ngay_kh_kt: '2026-08-15',
      gt_cl_dk: 26833334,
      gt_da_kh_dk: 8166666,
      gt_da_kh_lk: 9333333,
      gt_cl_ck: 25666667
    },
    {
      id: '7',
      stt: 7,
      ma_cc: 'CC007',
      ten_cc: 'Ghế xoay văn phòng có tựa lưng',
      ngay_kh1: '2024-01-20',
      so_ky_kh: 36,
      ma_bp: 'BP001',
      ten_bp: 'Phòng Kế toán',
      ma_lcc: 'LCC002',
      ten_lcc: 'Nội thất văn phòng',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 1800000,
      gt_da_kh: 600000,
      gt_cl: 1200000,
      gt_kh_ky: 50000,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV001',
      ma_phi: 'PH001',
      ma_unit: 'CN',
      so_luong: 5,
      dvt: 'Cái',
      ngay_kh_kt: '2027-01-20',
      gt_cl_dk: 1250000,
      gt_da_kh_dk: 550000,
      gt_da_kh_lk: 600000,
      gt_cl_ck: 1200000
    },
    {
      id: '8',
      stt: 8,
      ma_cc: 'CC008',
      ten_cc: 'Máy chiếu Epson EB-X41',
      ngay_kh1: '2024-03-10',
      so_ky_kh: 24,
      ma_bp: 'BP005',
      ten_bp: 'Phòng Đào tạo',
      ma_lcc: 'LCC004',
      ten_lcc: 'Thiết bị trình chiếu',
      ly_do: 'Phân bổ khấu hao theo kỳ',
      nguyen_gia: 12000000,
      gt_da_kh: 3000000,
      gt_cl: 9000000,
      gt_kh_ky: 500000,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_vv: 'VV005',
      ma_phi: 'PH005',
      ma_unit: 'CN',
      so_luong: 1,
      dvt: 'Cái',
      ngay_kh_kt: '2026-03-10',
      gt_cl_dk: 9500000,
      gt_da_kh_dk: 2500000,
      gt_da_kh_lk: 3000000,
      gt_cl_ck: 9000000
    }
  ];
};

/**
 * Hook for managing CCDC allocation calculation data
 */
export function useBangTinhPhanBoCCDC(searchParams: BangTinhPhanBoCCDCSearchFormValues): UseBangTinhPhanBoCCDCReturn {
  const [data, setData] = useState<BangTinhPhanBoCCDCItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangTinhPhanBoCCDCSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangTinhPhanBoCCDCResponse>('/cong-cu/bang-tinh-phan-bo-ccdc/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
