'use client';

import { FormField } from '@/components/custom/arito/form/form-field';

import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { Label } from '@/components/ui/label';

interface OtherTabProps {
  formMode: 'add' | 'edit' | 'view';
}

export const OtherTab = ({ formMode }: OtherTabProps) => {
  return (
    // <div className="p-4 flex justify-center text-sm font-semibold">
    //   <div className="w-full max-w-4xl space-y-4">
    //     <div className="flex flex-col lg:flex-row gap-4 items-start">
    //       <FormField
    //         className="grid grid-cols-[150px,1fr] items-center"
    //         disabled={formMode === "view"}
    //         type="select"
    //         label="Mẫu lọc báo cáo"
    //         name="mau_bao_cao_dl"
    //         options={[{ label: "Người dùng tự lọc", value: 0 }]}
    //       />
    //       <div className="h-8 w-8 mt-2 lg:mt-0">
    //         <RadixHoverDropdown
    //           buttonClassName="!h-8 !w-8"
    //           iconNumber={624}
    //           items={[
    //             {
    //               value: "create_new",
    //               label: "Lưu mẫu mới",
    //               icon: 7,
    //             },
    //             {
    //               value: "edit",
    //               label: "Lưu đè vào mẫu đang chọn",
    //               icon: 75,
    //             },
    //             {
    //               value: "delete",
    //               label: "Xóa mẫu đang chọn",
    //               icon: 8,
    //               onClick: () => console.log("Delete current filter template"),
    //             },
    //           ]}
    //         />
    //       </div>
    //     </div>
    //     <div className="flex flex-col lg:flex-row gap-4 items-start">
    //       <FormField
    //         className="grid grid-cols-[150px,1fr] items-center"
    //         disabled={formMode === "view"}
    //         type="select"
    //         label="Mẫu phân tích DL"
    //         name="mau_bao_cao_dl"
    //         options={[{ label: "Không phân tích", value: 0 }]}
    //       />
    //       <div className="h-8 w-8 mt-2 lg:mt-0">
    //         <RadixHoverDropdown
    //           buttonClassName="!h-8 !w-8"
    //           iconNumber={790}
    //           items={[
    //             {
    //               value: "create_new",
    //               label: "Tạo mẫu phân tích mới",
    //               icon: 7,
    //             },
    //             {
    //               value: "edit",
    //               label: "Sửa mẫu đang chọn",
    //               icon: 75,
    //             },
    //             {
    //               value: "delete",
    //               label: "Xóa mẫu đang chọn",
    //               icon: 8,
    //               onClick: () => console.log("Delete current filter template"),
    //             },
    //           ]}
    //         />
    //       </div>
    //     </div>
    //   </div>
    // </div>

    <div className='min-w-[40vw] p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-4 md:space-y-6'>
          <div className='flex flex-col gap-2 lg:flex-row lg:items-start'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='mauLocBaoCao' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Mẫu lọc báo cáo
              </Label>
              <FormField
                className='w-full min-w-[160px] sm:min-w-[200px] lg:min-w-[250px]'
                id='mauLocBaoCao'
                disabled={formMode === 'view'}
                type='select'
                name='mau_bc'
                options={[{ label: 'Người dùng tự lọc', value: 0 }]}
                defaultValue={0}
              />
            </div>
            <div className='mt-2 h-8 w-8 self-end ring-0 hover:ring-0 sm:mt-0 sm:self-center lg:self-center'>
              <RadixHoverDropdown
                buttonClassName='!h-8 !w-8'
                iconNumber={624}
                items={[
                  {
                    value: 'create_new',
                    label: 'Lưu mẫu mới',
                    icon: 7
                  },
                  {
                    value: 'edit',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
          <div className='flex flex-col gap-2 lg:flex-row lg:items-start'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='mauPhanTichDL' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Mẫu phân tích DL
              </Label>
              <FormField
                className='w-full min-w-[160px] sm:min-w-[200px] lg:min-w-[250px]'
                id='mauPhanTichDL'
                disabled={formMode === 'view'}
                type='select'
                name='data_analysis_struct'
                options={[{ label: 'Không phân tích', value: 0 }]}
                defaultValue={0}
              />
            </div>
            <div className='mt-2 h-8 w-8 self-end ring-0 hover:ring-0 sm:mt-0 sm:self-center lg:self-center'>
              <RadixHoverDropdown
                buttonClassName='!h-8 !w-8'
                iconNumber={873}
                items={[
                  {
                    value: 'create_new',
                    label: 'Tao mẫu phân tích mới',
                    icon: 7
                  },
                  {
                    value: 'edit',
                    label: 'Sửa mẫu đang chọn',
                    icon: 75
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
