import { useFormContext } from 'react-hook-form';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode }) => {
  const { setValue } = useFormContext();
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <label className='min-w-40 text-left text-sm'>Tài khoản</label>
          <SearchField
            name='account'
            searchColumns={accountSearchColumns}
            className='w-full'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            columnDisplay='code'
            displayRelatedField='name'
            onValueChange={value => {
              setValue('account', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('account', row.code);
              }
            }}
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bậc tk</Label>
          <div className='w-[130px]'>
            <FormField type='number' label='' name='account_level' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              label=''
              name='account_type'
              disabled={formMode === 'view'}
              options={[
                { label: 'Tất cả', value: '1' },
                { label: 'Tài khoản chi tiết', value: '2' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bù trừ số dư</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              label=''
              name='deduct_balance'
              disabled={formMode === 'view'}
              options={[
                { label: 'Không', value: '1' },
                { label: 'Có - Ngoài trừ tài khoản công nợ', value: '2' },
                { label: 'Có - Tất cả tài khoản', value: '3' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Người lập</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='creator' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              label=''
              name='report_template'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn', value: '1' },
                { label: 'Mẫu ngoại tệ', value: '2' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
