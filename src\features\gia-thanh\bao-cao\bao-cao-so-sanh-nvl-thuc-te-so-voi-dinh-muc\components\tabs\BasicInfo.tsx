import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Từ kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='fromMonth' type='number' className='w-20' />
            <FormField name='fromYear' type='number' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đến kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='toMonth' type='number' className='w-20' />
            <FormField name='toYear' type='number' className='w-24' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
