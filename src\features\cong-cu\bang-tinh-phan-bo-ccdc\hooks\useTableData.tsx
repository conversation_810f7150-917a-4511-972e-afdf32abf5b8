import React, { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BangTinhPhanBoCCDCItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BangTinhPhanBoCCDCItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const totals = data.reduce(
      (acc, item) => ({
        nguyen_gia: acc.nguyen_gia + (item.nguyen_gia || 0),
        gt_da_kh_dk: acc.gt_da_kh_dk + (item.gt_da_kh_dk || 0),
        gt_cl_dk: acc.gt_cl_dk + (item.gt_cl_dk || 0),
        gt_kh_ky: acc.gt_kh_ky + (item.gt_kh_ky || 0),
        gt_da_kh_lk: acc.gt_da_kh_lk + (item.gt_da_kh_lk || 0),
        gt_cl_ck: acc.gt_cl_ck + (item.gt_cl_ck || 0)
      }),
      {
        nguyen_gia: 0,
        gt_da_kh_dk: 0,
        gt_cl_dk: 0,
        gt_kh_ky: 0,
        gt_da_kh_lk: 0,
        gt_cl_ck: 0
      }
    );

    const totalRow = {
      id: 'total',
      stt: '',
      ma_cc: '',
      ten_cc: 'Tổng cộng',
      ma_bp: '',
      dvt: '',
      so_luong: '',
      ten_lcc: '',
      ngay_kh1: '',
      so_ky_kh: '',
      ngay_kh_kt: '',
      nguyen_gia: totals.nguyen_gia,
      gt_da_kh_dk: totals.gt_da_kh_dk,
      gt_cl_dk: totals.gt_cl_dk,
      gt_kh_ky: totals.gt_kh_ky,
      gt_da_kh_lk: totals.gt_da_kh_lk,
      gt_cl_ck: totals.gt_cl_ck,
      tk_cc: '',
      tk_kh: '',
      tk_cp: '',
      ma_vv: '',
      ma_phi: ''
    };

    const tableRows = [totalRow, ...data];

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'stt',
            headerName: 'STT',
            width: 80,
            renderCell: (params: any) => {
              if (params.row.id === 'total') {
                return '';
              }
              return params.rowIndex;
            }
          },
          {
            field: 'ma_cc',
            headerName: 'Mã công cụ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ma_cc || '';
            }
          },
          {
            field: 'ten_cc',
            headerName: 'Tên công cụ',
            width: 250,
            renderCell: (params: any) => {
              const value = params.row.ten_cc || '';
              if (params.row.id === 'total') {
                return <span className='font-bold'>{value}</span>;
              }
              return value;
            }
          },
          {
            field: 'ma_bp',
            headerName: 'Mã bộ phận',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ma_bp || '';
            }
          },
          {
            field: 'dvt',
            headerName: 'ĐVT',
            width: 80,
            renderCell: (params: any) => {
              return params.row.dvt || '';
            }
          },
          {
            field: 'so_luong',
            headerName: 'Số lượng',
            width: 100,
            renderCell: (params: any) => {
              return params.row.so_luong || 0;
            }
          },
          {
            field: 'ten_lcc',
            headerName: 'Tên loại công cụ',
            width: 180,
            renderCell: (params: any) => {
              return params.row.ten_lcc || '';
            }
          },
          {
            field: 'ngay_kh1',
            headerName: 'Ngày tính p/b',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ngay_kh1 || '';
            }
          },
          {
            field: 'so_ky_kh',
            headerName: 'Số kỳ phân bổ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.so_ky_kh || 0;
            }
          },
          {
            field: 'ngay_kh_kt',
            headerName: 'Ngày k/thúc pb',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ngay_kh_kt || '';
            }
          },
          {
            field: 'nguyen_gia',
            headerName: 'Nguyên giá',
            width: 130,
            renderCell: (params: any) => {
              const value = params.row.nguyen_gia || 0;
              const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
              if (params.row.id === 'total') {
                return <span className='font-bold'>{formattedValue}</span>;
              }
              return formattedValue;
            }
          },
          {
            field: 'gt_da_kh_dk',
            headerName: 'Gt đã pb đầu kỳ',
            width: 140,
            renderCell: (params: any) => {
              const value = params.row.gt_da_kh_dk || 0;
              const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
              if (params.row.id === 'total') {
                return <span className='font-bold'>{formattedValue}</span>;
              }
              return formattedValue;
            }
          },
          {
            field: 'gt_cl_dk',
            headerName: 'Gt còn lại đầu kỳ',
            width: 140,
            renderCell: (params: any) => {
              const value = params.row.gt_cl_dk || 0;
              const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
              if (params.row.id === 'total') {
                return <span className='font-bold'>{formattedValue}</span>;
              }
              return formattedValue;
            }
          },
          {
            field: 'gt_kh_ky',
            headerName: 'Gt pb trong kỳ',
            width: 130,
            renderCell: (params: any) => {
              const value = params.row.gt_kh_ky || 0;
              const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
              if (params.row.id === 'total') {
                return <span className='font-bold'>{formattedValue}</span>;
              }
              return formattedValue;
            }
          },
          {
            field: 'gt_da_kh_lk',
            headerName: 'Gt pb lũy kế',
            width: 130,
            renderCell: (params: any) => {
              const value = params.row.gt_da_kh_lk || 0;
              const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
              if (params.row.id === 'total') {
                return <span className='font-bold'>{formattedValue}</span>;
              }
              return formattedValue;
            }
          },
          {
            field: 'gt_cl_ck',
            headerName: 'Gt còn lại cuối kỳ',
            width: 140,
            renderCell: (params: any) => {
              const value = params.row.gt_cl_ck || 0;
              const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
              if (params.row.id === '0') {
                return <span className='font-medium'>{formattedValue}</span>;
              }
              return formattedValue;
            }
          },
          {
            field: 'tk_cc',
            headerName: 'Tk công cụ',
            width: 100,
            renderCell: (params: any) => {
              return params.row.tk_cc || '';
            }
          },
          {
            field: 'tk_kh',
            headerName: 'Tk phân bổ',
            width: 100,
            renderCell: (params: any) => {
              return params.row.tk_kh || '';
            }
          },
          {
            field: 'tk_cp',
            headerName: 'Tk chi phí',
            width: 100,
            renderCell: (params: any) => {
              return params.row.tk_cp || '';
            }
          },
          {
            field: 'ma_vv',
            headerName: 'Mã vụ việc',
            width: 100,
            renderCell: (params: any) => {
              return params.row.ma_vv || '';
            }
          },
          {
            field: 'ma_phi',
            headerName: 'Mã phí',
            width: 100,
            renderCell: (params: any) => {
              return params.row.ma_phi || '';
            }
          }
        ],
        rows: tableRows
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
