'use client';

import React, { createContext, ReactNode, RefCallback, useEffect, useRef, useState } from 'react';
import { Box, IconButton, Modal, Tab, Tabs, Typography } from '@mui/material';
import { DefaultValues, FormProvider, useForm } from 'react-hook-form';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import { yupResolver } from '@hookform/resolvers/yup';
import CloseIcon from '@mui/icons-material/Close';
import AritoIcon from '@/components/custom/arito/icon';

export const AritoPopupFormContext = createContext<{
  control: any;
  errors: any;
  isViewMode: boolean;
}>({
  control: {},
  errors: {},
  isViewMode: false
});

export type TabItem = {
  id: string;
  label: string;
  component: ReactNode;
};

export interface AritoPopupFormProps<TFormData extends Record<string, any>> {
  open: boolean;
  onClose: () => void;
  mode?: 'view' | 'edit' | 'add';
  title: string;
  tabs?: ReactNode | TabItem[];
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  fullWidth?: boolean;
  bottomBar?: ReactNode;
  initialData?: TFormData;
  onSubmit?: (data: TFormData) => void | Promise<void>;
  schema?: any;
  headerFields?: ReactNode;
  titleIcon?: ReactNode;
  onDelete?: () => void;
  onCopy?: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
}

export const AritoPopupForm = <TFormData extends Record<string, any>>({
  open,
  onClose,
  mode = 'edit',
  title,
  tabs,
  maxWidth = 'md',
  fullWidth = true,
  bottomBar,
  initialData,
  onSubmit,
  schema,
  headerFields,
  titleIcon,
  onDelete,
  onCopy,
  onAdd,
  onEdit
}: AritoPopupFormProps<TFormData>) => {
  const [activeTab, setActiveTab] = useState<string>(Array.isArray(tabs) ? tabs[0]?.id || '' : '');
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [tabsHeight, setTabsHeight] = useState<number>(0);
  const tabRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Check if tabs is an array of TabItem
  const tabsHasTabs = Array.isArray(tabs) && tabs.length > 0 && 'id' in tabs[0];

  // Update tab height when tabs change or when the component mounts
  useEffect(() => {
    if (tabsHasTabs && open) {
      // Allow DOM to render first
      const timeoutId = setTimeout(() => {
        let maxHeight = 0;

        // Find the tallest tab
        Object.values(tabRefs.current).forEach(ref => {
          if (ref && ref.scrollHeight > maxHeight) {
            maxHeight = ref.scrollHeight;
          }
        });

        // Set the height to the tallest tab + 50px
        setTabsHeight(maxHeight > 0 ? maxHeight + 50 : 0);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [tabs, open, tabsHasTabs]);

  const methods = useForm<TFormData>({
    mode: 'onSubmit',
    defaultValues: (initialData || {}) as DefaultValues<TFormData>,
    resolver: schema ? (yupResolver(schema) as any) : undefined
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset
  } = methods;

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      reset(initialData);
    } else {
      reset({} as TFormData);
    }
  }, [initialData, reset]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    // When switching tabs, we want to validate the current tab's fields
    // but allow the user to switch tabs even if there are errors
    const currentTabFields = getTabFields(activeTab);

    if (currentTabFields.length > 0) {
      // Trigger validation for the current tab's fields
      const validationResult = currentTabFields.every(field => methods.trigger(field as any));

      // Log validation issues for debugging but still allow tab change
      if (!validationResult) {
        console.info('There are validation errors in the current tab, but allowing tab change');
      }
    }

    setActiveTab(newValue);
  };

  // Helper function to determine which fields belong to which tab
  // This is a simple implementation - adjust based on your form structure
  const getTabFields = (tabId: string): string[] => {
    // This should be customized based on your form structure
    // For now, we'll return an empty array as a placeholder
    return [];
  };

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const onFormSubmit = async (data: TFormData) => {
    try {
      if (onSubmit) {
        // Call the onSubmit callback and await if it returns a Promise
        await onSubmit(data);

        // If we get here, the form was submitted successfully
        console.info('Form submitted successfully');
      }
    } catch (error) {
      // Handle any errors that occurred during form submission
      console.error('Error submitting form:', error);

      // You could set a form-level error state here if needed
    }
  };

  // Create properly typed ref callback
  const setTabRef: RefCallback<HTMLDivElement> = (element: HTMLDivElement | null) => {
    if (element) {
      const tabId = element.getAttribute('data-tab-id');
      if (tabId) {
        tabRefs.current[tabId] = element;
      }
    }
  };

  return (
    <Modal open={open} onClose={(event, reason) => reason !== 'backdropClick' && onClose()}>
      <Box
        sx={{
          position: 'absolute',
          top: isFullScreen ? 0 : '50%',
          left: isFullScreen ? 0 : '50%',
          transform: isFullScreen ? 'none' : 'translate(-50%, -50%)',
          width: isFullScreen
            ? '100%'
            : maxWidth === 'xs'
              ? 400
              : maxWidth === 'sm'
                ? 600
                : maxWidth === 'md'
                  ? 800
                  : maxWidth === 'lg'
                    ? 1000
                    : maxWidth === 'xl'
                      ? 1200
                      : '90%',
          height: isFullScreen ? '100%' : 'auto',
          maxHeight: isFullScreen ? '100%' : '90vh',
          minHeight: isFullScreen ? '100%' : '200px',
          bgcolor: 'background.paper',
          boxShadow: 24,
          borderRadius: isFullScreen ? 0 : '4px',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          '@media (max-width: 600px)': {
            width: isFullScreen ? '100%' : '95%',
            height: isFullScreen ? '100%' : 'auto',
            maxHeight: isFullScreen ? '100%' : '95vh',
            minHeight: isFullScreen ? '100%' : '200px',
            top: isFullScreen ? 0 : '50%'
          }
        }}
      >
        <FormProvider {...methods}>
          <AritoPopupFormContext.Provider value={{ control, errors, isViewMode: mode === 'view' }}>
            <Box
              sx={{
                padding: '8px 16px',
                display: 'flex',
                backgroundColor: '#f2f7fc',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: '1px solid #e0e0e0',
                '@media (max-width: 600px)': {
                  padding: '8px 12px'
                }
              }}
            >
              <Typography
                variant='subtitle1'
                sx={{
                  fontWeight: 'bold',
                  '@media (max-width: 600px)': {
                    fontSize: '0.9rem'
                  }
                }}
              >
                <div className='flex items-center gap-2'>
                  {titleIcon}
                  <span className='font-medium text-[#787879]'>{title}</span>
                </div>
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton
                  size='small'
                  onClick={toggleFullScreen}
                  title={isFullScreen ? 'Thoát toàn màn hình' : 'Toàn màn hình'}
                >
                  {isFullScreen ? <FullscreenExitIcon fontSize='small' /> : <FullscreenIcon fontSize='small' />}
                </IconButton>
                <IconButton size='small' onClick={onClose} title='Đóng'>
                  <CloseIcon fontSize='small' />
                </IconButton>
              </Box>
            </Box>
            <Box
              sx={{
                padding: '0px',
                flexGrow: 1,
                backgroundColor: '#f9fcfd',
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column',
                height: 'auto',
                maxHeight: 'calc(80vh - 40px - 60px)',
                '@media (max-width: 600px)': {
                  padding: '12px 8px',
                  height: 'auto',
                  maxHeight: 'calc(85vh - 40px - 60px)'
                }
              }}
            >
              {headerFields && (
                <Box
                  sx={{
                    mb: -1,
                    backgroundColor: '#f9fcfd',
                    position: 'sticky',
                    top: 0,
                    zIndex: 2,
                    padding: '12px 16px 0px 16px',
                    maxHeight: 'none',
                    height: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                  }}
                >
                  {headerFields}
                </Box>
              )}

              {tabs && (
                <div className='flex flex-grow flex-col overflow-hidden border-none'>
                  {tabsHasTabs ? (
                    <>
                      {/* Main Tabs Navigation - MUI Tabs */}
                      <Box
                        sx={{
                          borderBottom: 1,
                          borderColor: 'divider',
                          height: 'auto',
                          minHeight: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          backgroundColor: '#f9fcfd',
                          textTransform: 'none',
                          position: 'sticky',
                          top: headerFields ? 'auto' : 0,
                          zIndex: 1,
                          width: '100%',
                          '& .MuiTabs-root': {
                            minHeight: '40px',
                            width: '100%'
                          },
                          '& .MuiTab-root': {
                            minHeight: '40px',
                            padding: '2px 12px',
                            fontWeight: 'bold',
                            fontSize: '11px',
                            minWidth: 'auto',
                            textTransform: 'none',
                            '&.Mui-selected': {
                              color: '#0b87c9'
                            }
                          },
                          '& .MuiTabs-indicator': {
                            backgroundColor: '#0b87c9',
                            height: '2px'
                          },
                          '@media (max-width: 600px)': {
                            height: 'auto',
                            minHeight: '36px',
                            '& .MuiTabs-root': {
                              minHeight: '36px'
                            },
                            '& .MuiTab-root': {
                              minHeight: '36px',
                              padding: '2px 8px',
                              minWidth: 'auto',
                              fontSize: '10px'
                            }
                          }
                        }}
                      >
                        <Tabs
                          value={activeTab}
                          onChange={handleTabChange}
                          variant='standard'
                          textColor='primary'
                          indicatorColor='primary'
                          sx={{
                            width: '100%'
                          }}
                        >
                          {(tabs as TabItem[]).map(tab => (
                            <Tab
                              key={tab.id}
                              value={tab.id}
                              label={tab.label}
                              sx={{
                                textTransform: 'none',
                                fontSize: '11px',
                                fontWeight: 'bold',
                                minHeight: '40px',
                                padding: '2px 12px',
                                '&.Mui-selected': {
                                  color: '#0b87c9'
                                },
                                '@media (max-width: 600px)': {
                                  minHeight: '36px',
                                  padding: '2px 8px',
                                  fontSize: '10px'
                                }
                              }}
                            />
                          ))}
                        </Tabs>
                      </Box>

                      {/* Tab Content */}
                      <Box
                        sx={{
                          overflow: 'auto',
                          display: 'flex',
                          flexDirection: 'column',
                          height: tabsHeight > 0 ? `${tabsHeight}px` : 'auto',
                          minHeight: '120px' // Fallback min-height
                        }}
                      >
                        <div className='w-full'>
                          {(tabs as TabItem[]).map(tab => (
                            <div
                              key={tab.id}
                              ref={setTabRef}
                              data-tab-id={tab.id}
                              className={
                                activeTab === tab.id
                                  ? 'block w-full transition-all duration-150'
                                  : 'hidden w-full transition-all duration-150'
                              }
                            >
                              {tab.component}
                            </div>
                          ))}
                        </div>
                      </Box>
                    </>
                  ) : (
                    // Regular non-tabbed content
                    <Box
                      sx={{
                        overflow: 'auto',
                        height: 'auto',
                        minHeight: '120px'
                      }}
                    >
                      {tabs as ReactNode}
                    </Box>
                  )}
                </div>
              )}
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'flex-end',
                padding: '8px 16px',
                borderTop: '1px solid #e0e0e0',
                backgroundColor: '#f9fcfd',
                gap: 1,
                '@media (max-width: 600px)': {
                  padding: '8px 12px'
                }
              }}
            >
              {bottomBar ? (
                // If bottomBar is provided, render it directly
                bottomBar
              ) : (
                // Otherwise, show the default save and cancel buttons
                <>
                  {mode !== 'view' && (
                    <button
                      className='flex items-center justify-center gap-2 bg-teal-600 px-4 py-2 text-white'
                      onClick={handleSubmit(onFormSubmit)}
                      type='button'
                    >
                      <AritoIcon icon={884} marginX='4px' />
                      <p className='flex items-center justify-center text-sm font-bold'>Đồng ý</p>
                    </button>
                  )}
                  {mode == 'view' && (
                    <>
                      <button
                        className='flex items-center justify-center gap-2 bg-teal-600 px-4 py-2 text-white'
                        onClick={onAdd}
                        type='button'
                        title='Thêm'
                      >
                        <AritoIcon icon={571} marginX='4px' />
                        <p className='flex items-center justify-center text-sm font-bold'>Thêm</p>
                      </button>
                      <button
                        className='flex items-center justify-center gap-2 border border-teal-600 px-4 py-2 text-teal-600'
                        onClick={onEdit}
                        type='button'
                        title='Sửa'
                      >
                        <AritoIcon icon={9} marginX='4px' />
                        <p className='flex items-center justify-center text-sm font-bold'>Sửa</p>
                      </button>
                      <button
                        className='flex items-center justify-center gap-2 border border-teal-600 px-4 py-2 text-teal-600'
                        onClick={onDelete}
                        type='button'
                        title='Xoá'
                      >
                        <AritoIcon icon={8} marginX='4px' />
                        <p className='flex items-center justify-center text-sm font-bold'>Xoá</p>
                      </button>
                      <button
                        className='flex items-center justify-center gap-2 border border-teal-600 px-4 py-2 text-teal-600'
                        onClick={onCopy}
                        type='button'
                        title='Sao chép'
                      >
                        <AritoIcon icon={11} marginX='4px' />
                        <p className='flex items-center justify-center text-sm font-bold'>Sao chép</p>
                      </button>
                    </>
                  )}
                  <button
                    onClick={onClose}
                    className='flex items-center justify-center gap-2 border border-teal-600 px-4 py-2 text-teal-600'
                    type='button'
                    title='Huỷ'
                  >
                    <AritoIcon icon={885} marginX='4px' />
                    <p className='flex items-center justify-center text-sm font-bold'>Huỷ</p>
                  </button>
                </>
              )}
            </Box>
          </AritoPopupFormContext.Provider>
        </FormProvider>
      </Box>
    </Modal>
  );
};

export default AritoPopupForm;
