import { useState, useCallback, useEffect } from 'react';
import { BusinessReportItem, BusinessReportResponse, SearchFormValues, UseBusinessReportReturn } from '../schema';
import api from '@/lib/api';

const generateMockData = (): BusinessReportItem[] => {
  // Regular data rows for business operation report
  const mockData: BusinessReportItem[] = [
    {
      id: '1',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 1,
      stt_in: '01',
      ma_so: '01',
      chi_tieu: 'Doanh thu bán hàng và cung cấp dịch vụ',
      thuyet_minh: '1',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: ***********,
      ky_truoc: ***********,
      tk: '511',
      tk_du: '',
      tai_khoan: '511',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '2',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 2,
      stt_in: '02',
      ma_so: '02',
      chi_tieu: 'Các khoản giảm trừ doanh thu',
      thuyet_minh: '2',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 500000000,
      ky_truoc: 400000000,
      tk: '521',
      tk_du: '',
      tai_khoan: '521',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '3',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 3,
      stt_in: '10',
      ma_so: '10',
      chi_tieu: 'Doanh thu thuần về bán hàng và cung cấp dịch vụ',
      thuyet_minh: '3',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 14500000000,
      ky_truoc: 11600000000,
      tk: '',
      tk_du: '',
      tai_khoan: '',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: true
    },
    {
      id: '4',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 4,
      stt_in: '11',
      ma_so: '11',
      chi_tieu: 'Giá vốn hàng bán',
      thuyet_minh: '4',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 8500000000,
      ky_truoc: 7200000000,
      tk: '632',
      tk_du: '',
      tai_khoan: '632',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '5',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 5,
      stt_in: '20',
      ma_so: '20',
      chi_tieu: 'Lợi nhuận gộp về bán hàng và cung cấp dịch vụ',
      thuyet_minh: '5',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 6000000000,
      ky_truoc: 4400000000,
      tk: '',
      tk_du: '',
      tai_khoan: '',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: true
    },
    {
      id: '6',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 6,
      stt_in: '21',
      ma_so: '21',
      chi_tieu: 'Doanh thu hoạt động tài chính',
      thuyet_minh: '6',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 200000000,
      ky_truoc: 150000000,
      tk: '515',
      tk_du: '',
      tai_khoan: '515',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '7',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 7,
      stt_in: '22',
      ma_so: '22',
      chi_tieu: 'Chi phí tài chính',
      thuyet_minh: '7',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 100000000,
      ky_truoc: 80000000,
      tk: '635',
      tk_du: '',
      tai_khoan: '635',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '8',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 8,
      stt_in: '23',
      ma_so: '23',
      chi_tieu: 'Phần lãi lỗ trong công ty liên doanh, liên kết',
      thuyet_minh: '8',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 50000000,
      ky_truoc: 30000000,
      tk: '515',
      tk_du: '',
      tai_khoan: '515',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '9',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 9,
      stt_in: '24',
      ma_so: '24',
      chi_tieu: 'Chi phí bán hàng',
      thuyet_minh: '9',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 800000000,
      ky_truoc: 650000000,
      tk: '641',
      tk_du: '',
      tai_khoan: '641',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '10',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 10,
      stt_in: '25',
      ma_so: '25',
      chi_tieu: 'Chi phí quản lý doanh nghiệp',
      thuyet_minh: '10',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 1200000000,
      ky_truoc: 1000000000,
      tk: '642',
      tk_du: '',
      tai_khoan: '642',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '11',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 11,
      stt_in: '30',
      ma_so: '30',
      chi_tieu: 'Lợi nhuận thuần từ hoạt động kinh doanh',
      thuyet_minh: '11',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 4150000000,
      ky_truoc: 2850000000,
      tk: '',
      tk_du: '',
      tai_khoan: '',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: true
    },
    {
      id: '12',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 12,
      stt_in: '31',
      ma_so: '31',
      chi_tieu: 'Thu nhập khác',
      thuyet_minh: '12',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 100000000,
      ky_truoc: 75000000,
      tk: '711',
      tk_du: '',
      tai_khoan: '711',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '13',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 13,
      stt_in: '32',
      ma_so: '32',
      chi_tieu: 'Chi phí khác',
      thuyet_minh: '13',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 50000000,
      ky_truoc: 40000000,
      tk: '811',
      tk_du: '',
      tai_khoan: '811',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '14',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 14,
      stt_in: '40',
      ma_so: '40',
      chi_tieu: 'Lợi nhuận khác',
      thuyet_minh: '14',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 50000000,
      ky_truoc: 35000000,
      tk: '',
      tk_du: '',
      tai_khoan: '',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: true
    },
    {
      id: '15',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 15,
      stt_in: '50',
      ma_so: '50',
      chi_tieu: 'Tổng lợi nhuận kế toán trước thuế',
      thuyet_minh: '15',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 4200000000,
      ky_truoc: 2885000000,
      tk: '',
      tk_du: '',
      tai_khoan: '',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: true
    },
    {
      id: '16',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 16,
      stt_in: '51',
      ma_so: '51',
      chi_tieu: 'Chi phí thuế thu nhập doanh nghiệp',
      thuyet_minh: '16',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 840000000,
      ky_truoc: 577000000,
      tk: '821',
      tk_du: '',
      tai_khoan: '821',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: false
    },
    {
      id: '17',
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 17,
      stt_in: '60',
      ma_so: '60',
      chi_tieu: 'Lợi nhuận sau thuế thu nhập doanh nghiệp',
      thuyet_minh: '17',
      xchi_tieu: '',
      xchi_tieu2: '',
      ky_nay: 3360000000,
      ky_truoc: 2308000000,
      tk: '',
      tk_du: '',
      tai_khoan: '',
      dau_cuoi: '',
      drilldown_controller: '',
      isSummary: true
    }
  ];

  return mockData;
};

/**
 * Custom hook for managing Business Report data
 *
 * This hook provides functionality to fetch business operation report data
 * with mock support for testing and development purposes.
 */
export function useBusinessReportData(searchParams: SearchFormValues): UseBusinessReportReturn {
  const [data, setData] = useState<BusinessReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BusinessReportResponse>(
        '/tong-hop/bao-cao-tai-chinh/bao-cao-ket-qua-hoat-dong-kinh-doanh/',
        {
          mock: true,
          mockData: mockData,
          params: searchParams
        }
      );

      // Handle both direct array and wrapped response
      const results = Array.isArray(response.data) ? response.data : response.data.results;
      setData(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
