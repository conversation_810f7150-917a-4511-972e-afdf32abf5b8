import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { FormField } from '@/components/custom/arito/form/form-field';
import { TabItem } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface SearchFieldProps {
  name: string;
  label: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  withSearch?: boolean;
  searchEndpoint?: string;
  searchColumns: ExtendedGridColDef[];
  defaultSearchColumn?: string;
  actionButtons?: ('add' | 'edit')[];
  headerFields?: React.ReactNode;
  tabs?: TabItem[];
}

const SearchField: React.FC<SearchFieldProps> = ({
  name,
  label,
  labelClassName = 'w-40 min-w-40',
  className = 'flex items-center',
  inputClassName = 'w-[250px]',
  disabled = false,
  formMode = 'add',
  withSearch = true,
  searchEndpoint = '/',
  searchColumns,
  defaultSearchColumn = 'name',
  actionButtons,
  headerFields,
  tabs
}) => {
  return (
    <div className={className}>
      <Label className={labelClassName}>{label}</Label>
      <div className={inputClassName}>
        <FormField
          name={name}
          type='text'
          label=''
          withSearch={withSearch}
          searchColumns={searchColumns}
          searchEndpoint={searchEndpoint}
          defaultSearchColumn={defaultSearchColumn}
          disabled={disabled || formMode === 'view'}
          actionButtons={actionButtons}
          headerFields={headerFields}
          tabs={tabs}
        />
      </div>
    </div>
  );
};

export default SearchField;
