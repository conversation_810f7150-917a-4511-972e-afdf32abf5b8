import { groupColumns, regionColumns, customerSearchColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Group, KhachHang, KhuVuc } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailsTabProps {
  searchFieldStates: {
    customer: KhachHang | null;
    setCustomer: (customer: KhachHang | null) => void;
    customerGroup1: Group | null;
    setCustomerGroup1: (group: Group | null) => void;
    customerGroup2: Group | null;
    setCustomerGroup2: (group: Group | null) => void;
    customerGroup3: Group | null;
    setCustomerGroup3: (group: Group | null) => void;
    region: KhuVuc | null;
    setRegion: (region: KhuVuc | null) => void;
  };
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <div className='w-40'>
            <SearchField<KhachHang>
              displayRelatedField={`customer_code`}
              columnDisplay={`customer_code`}
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={customerSearchColumns}
              value={searchFieldStates.customer?.customer_code || ''}
              relatedFieldValue={searchFieldStates.customer?.customer_code || ''}
              onRowSelection={searchFieldStates.setCustomer}
            />
          </div>
        </div>

        <div className='flex items-center pt-1'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <div className='w-40'>
                <SearchField<Group>
                  type='text'
                  displayRelatedField={`ten_phan_nhom`}
                  columnDisplay={`ten_phan_nhom`}
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH1`}
                  searchColumns={groupColumns}
                  value={searchFieldStates.customerGroup1?.ten_phan_nhom || ''}
                  relatedFieldValue={searchFieldStates.customerGroup1?.ten_phan_nhom || ''}
                  onRowSelection={searchFieldStates.setCustomerGroup1}
                />
              </div>
              <div className='w-40'>
                <SearchField<Group>
                  type='text'
                  displayRelatedField={`ten_phan_nhom`}
                  columnDisplay={`ten_phan_nhom`}
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH2`}
                  searchColumns={groupColumns}
                  value={searchFieldStates.customerGroup2?.ten_phan_nhom || ''}
                  relatedFieldValue={searchFieldStates.customerGroup2?.ten_phan_nhom || ''}
                  onRowSelection={searchFieldStates.setCustomerGroup2}
                />
              </div>
              <div className='w-40'>
                <SearchField<Group>
                  type='text'
                  displayRelatedField={`ten_phan_nhom`}
                  columnDisplay={`ten_phan_nhom`}
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH3`}
                  searchColumns={groupColumns}
                  value={searchFieldStates.customerGroup3?.ten_phan_nhom || ''}
                  relatedFieldValue={searchFieldStates.customerGroup3?.ten_phan_nhom || ''}
                  onRowSelection={searchFieldStates.setCustomerGroup3}
                />
              </div>
            </div>
          </div>
        </div>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Khu vực:</Label>
            <div className='w-40'>
              <SearchField<KhuVuc>
                type='text'
                displayRelatedField={`rgname`}
                columnDisplay={`rgname`}
                searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
                searchColumns={regionColumns}
                value={searchFieldStates.region?.rgname || ''}
                relatedFieldValue={searchFieldStates.region?.rgname || ''}
                onRowSelection={searchFieldStates.setRegion}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <FormField
            name='tat_toan'
            label='Tất toán'
            labelClassName='w-40'
            type='select'
            options={[
              { value: '0', label: 'Chưa tất toán' },
              { value: '1', label: 'Đã tất toán sử dụng tính năng này' }
            ]}
            className='w-[400px]'
          />
        </div>
        <div className='flex items-center'>
          <FormField
            name='mau_bao_cao'
            label='Mẫu báo cáo'
            labelClassName='w-40'
            type='select'
            options={[
              { value: '0', label: 'Mẫu tiền chuẩn' },
              { value: '1', label: 'Mẫu ngoại tệ' }
            ]}
            className='w-[400px]'
          />
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
