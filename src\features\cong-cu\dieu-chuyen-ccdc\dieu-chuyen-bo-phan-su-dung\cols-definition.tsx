import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { Checkbox } from '@/components/ui/checkbox';

export const CCDCTransfersColumns: GridColDef[] = [
  { field: 'ma_cong_cu', headerName: 'Mã công cụ', width: 150 },
  { field: 'ten_cong_cu', headerName: 'Tên công cụ', width: 300 },
  { field: 'ky', headerName: 'Kỳ', width: 100 },
  { field: 'nam', headerName: 'Năm', width: 100 },
  { field: 'ma_bo_phan', headerName: 'Mã bộ phận', width: 100 },
  { field: 'tk_cong_cu', headerName: 'Tk công cụ', width: 100 },
  { field: 'tk_phan_bo', headerName: 'Tk phân bổ', width: 100 },
  { field: 'tk_chi_phi', headerName: 'Tk chi phí', width: 100 }
];

export const ConstraintColumns: GridColDef[] = [
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 100 },
  {
    field: 'bat_buoc_nhap',
    headerName: 'Bắt buộc nhập',
    width: 120,
    renderCell: params => <Checkbox checked={params.value} />
  }
];

export const toolSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã công cụ', flex: 1 },
  { field: 'name', headerName: 'Tên công cụ', flex: 1 }
];

export const sectionSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'name', headerName: 'Tên bộ phận', flex: 1 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account', headerName: 'Tên tài khoản', flex: 1 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 1 },
  {
    field: 'ledger_account',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'detail_account',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'level', headerName: 'Bậc tk', flex: 1 }
];
