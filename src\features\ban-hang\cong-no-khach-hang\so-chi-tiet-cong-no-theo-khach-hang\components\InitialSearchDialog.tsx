import React from 'react';

import { AritoHeaderTabs, BottomBar, AritoForm, AritoDialog, AritoIcon } from '@/components/custom/arito';
import { searchSchema, initialValues, SearchFormValues } from '../schema';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: SearchFormValues) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();

  const handleSubmit = (data: SearchFormValues) => {
    const combinedData = {
      ...data,
      tk: searchFieldStates.account?.code || ''
    };
    onSearch(combinedData);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Sổ chi tiết công nợ theo khách hàng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo
              searchFieldStates={{ account: searchFieldStates.account, setAccount: searchFieldStates.setAccount }}
            />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative flex w-full justify-end gap-2'
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
