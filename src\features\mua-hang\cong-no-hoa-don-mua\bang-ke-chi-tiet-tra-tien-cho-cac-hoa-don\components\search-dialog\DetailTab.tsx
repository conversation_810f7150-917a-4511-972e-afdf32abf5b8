import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { khachHangSearchColumns, regionColumns, QUERY_KEYS } from '@/constants';
import { FormField } from '@/components/custom/arito/form/form-field';
import { DoiTuong, KhuVuc, NhomKhachHang } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  formMode: FormMode;
  nhaCungCap: DoiTuong | null;
  setNhaCungCap: (nhaCungCap: DoiTuong) => void;
  nhomKH1: NhomKhachHang | null;
  nhomKH2: NhomKhachHang | null;
  nhomKH3: NhomKhachHang | null;
  setNhomKH1: (nhaCungCap: NhomKhachHang) => void;
  setNhomKH2: (nhaCungCap: NhomKhachHang) => void;
  setNhomKH3: (nhaCungCap: NhomKhachHang) => void;
  khuVuc: KhuVuc | null;
  setKhuVuc: (khuVuc: KhuVuc) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({
  formMode,
  nhaCungCap,
  setNhaCungCap,
  nhomKH1,
  nhomKH2,
  nhomKH3,
  setNhomKH1,
  setNhomKH2,
  setNhomKH3,
  khuVuc,
  setKhuVuc
}) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhà cung cấp</Label>
          <SearchField<DoiTuong>
            type='text'
            name='ma_kh'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            dialogTitle='Danh mục đối tượng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            classNameRelatedField='w-full'
            onRowSelection={setNhaCungCap}
            value={nhaCungCap?.customer_code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm NCC</Label>
          <SearchField<NhomKhachHang>
            type='text'
            name='nh_kh1'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
            dialogTitle='Danh mục nhóm khách hàng 1'
            columnDisplay='ma_nh'
            classNameRelatedField='w-full'
            onRowSelection={setNhomKH1}
            value={nhomKH1?.ma_nh || ''}
          />
          <SearchField<NhomKhachHang>
            type='text'
            name='nh_kh2'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
            dialogTitle='Danh mục nhóm khách hàng 2'
            columnDisplay='ma_nh'
            classNameRelatedField='w-full'
            onRowSelection={setNhomKH2}
            value={nhomKH2?.ma_nh || ''}
            className='ml-2'
          />
          <SearchField<NhomKhachHang>
            type='text'
            name='nh_kh3'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
            dialogTitle='Danh mục nhóm khách hàng 3'
            columnDisplay='ma_nh'
            classNameRelatedField='w-full'
            onRowSelection={setNhomKH3}
            value={nhomKH3?.ma_nh || ''}
            className='ml-2'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực</Label>
          <SearchField<KhuVuc>
            type='text'
            name='rg_code'
            searchColumns={regionColumns}
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
            dialogTitle='Danh mục khu vực'
            columnDisplay='rg_code'
            displayRelatedField='rgname'
            classNameRelatedField='w-full'
            onRowSelection={setKhuVuc}
            value={khuVuc?.rg_code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số dư</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='tt_yn'
              disabled={formMode === 'view'}
              options={[
                { label: '0. Tất cả', value: '0' },
                { label: '1. Chỉ có hóa đơn số dư lớn hơn 0', value: '1' },
                { label: '2. Chỉ những hóa đơn đã tất toán', value: '2' }
              ]}
              defaultValue={'0'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chi tiết thu tiền</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='ct_thu'
              disabled={formMode === 'view'}
              options={[
                { label: '0. Không', value: '0' },
                { label: '1. Có', value: '1' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='mau_bc'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn', value: '20' },
                { label: 'Mẫu ngoại tệ', value: '30' }
              ]}
              defaultValue={'20'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
