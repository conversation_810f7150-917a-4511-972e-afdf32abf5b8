import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import { AritoMenuActionButton } from './arito-menu-action-custom';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';
interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
  onShowSidebar?: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onShowSidebar
}: ActionBarProps) => (
  <AritoActionBar
    titleComponent={<h1 className='text-xl font-bold'>Danh mục nhà cung cấp</h1>}
    className='flex w-full items-center justify-between bg-white p-4'
  >
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
    <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => {}} />
    <AritoActionButton title='Cố định cột' icon={FileSpreadsheet} onClick={() => {}} />
    <AritoMenuButton
      items={[
        {
          title: 'Cập nhật trạng thái doanh nghiệp',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={18} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Tải mẫu Excel',
          icon: <AritoIcon icon={28} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: <AritoIcon icon={29} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Phân nhóm nhiều khách hàng',
          icon: <AritoIcon icon={774} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Ẩn/hiện cây phân nhóm',
          icon: <AritoIcon icon={538} />,
          onClick: onShowSidebar,
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);
