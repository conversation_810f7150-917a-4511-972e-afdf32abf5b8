'use client';

import { useState } from 'react';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { ActionBar, InitialSearchDialog } from './components';
import { useReportData } from './hooks';

export default function BaoCaoSoSanhNVLThucTeSoVoiDinhMuc() {
  const [searchParams, setSearchParams] = useState(null);
  const {
    initialSearchDialogOpen,
    showTable,
    isLoading,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,

    tables,
    handleRowClick,

    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  } = useReportData();

  const handleSearchSubmit = (data: any) => {
    setSearchParams(data);
    handleInitialSearch(data);
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchSubmit}
      />

      {isLoading && <LoadingOverlay />}
      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
