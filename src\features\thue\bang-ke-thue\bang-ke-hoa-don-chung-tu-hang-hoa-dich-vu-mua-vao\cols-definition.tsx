import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

const isSummaryRow = (params: any): boolean => {
  // Check if the row has the isSummary property set to true
  if (params && params.row && params.row.isSummary === true) {
    return true;
  }

  // Fallback to the old method of checking ID
  const id = params.id || params;
  return typeof id === 'string' && id.toString().startsWith('summary');
};

const createBoldCellRenderer = () => {
  const BoldCellRenderer = (params: GridRenderCellParams) => {
    if (!params) return null;

    if (isSummaryRow(params)) {
      return <div className='font-bold'>{params.value}</div>;
    }

    return params.value;
  };

  BoldCellRenderer.displayName = 'BoldCellRenderer';
  return BoldCellRenderer;
};

const formatDateToDDMMYYYY = (date: Date | string | null | undefined): string => {
  if (!date || date === null || date === undefined) {
    return '//';
  }

  let dateObj: Date;

  // Handle string dates (ISO format like "2023-01-09T17:00:00.000Z")
  if (typeof date === 'string') {
    dateObj = new Date(date);
  } else if (date instanceof Date) {
    dateObj = date;
  } else {
    return '//';
  }

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    return '//';
  }

  const day = dateObj.getDate().toString().padStart(2, '0');
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const year = dateObj.getFullYear();
  return `${day}/${month}/${year}`;
};

const createDateCellRenderer = () => {
  const DateCellRenderer = (params: GridRenderCellParams) => {
    if (!params) return null;

    const formattedDate = formatDateToDDMMYYYY(params.value);

    if (isSummaryRow(params)) {
      return <div className='font-bold'>{formattedDate}</div>;
    }

    return formattedDate;
  };

  DateCellRenderer.displayName = 'DateCellRenderer';
  return DateCellRenderer;
};

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'so_hoa_don',
    headerName: 'Số hóa đơn',
    width: 120,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'ngay_hoa_don',
    headerName: 'Ngày hđ',
    width: 150,
    type: 'date',
    valueFormatter: (params: any) => {
      if (!params || params.value === null || params.value === undefined) {
        return '//';
      }
      return formatDateToDDMMYYYY(params.value);
    },
    renderCell: createDateCellRenderer()
  },
  {
    field: 'so_chung_tu',
    headerName: 'Số chứng từ',
    width: 120,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'ngay_chung_tu',
    headerName: 'Ngày c.từ',
    width: 100,
    type: 'date',
    valueFormatter: (params: any) => {
      if (!params || params.value === null || params.value === undefined) {
        return '//';
      }
      return formatDateToDDMMYYYY(params.value);
    },
    renderCell: createDateCellRenderer()
  },
  {
    field: 'ky_hieu',
    headerName: 'Ký hiệu',
    width: 100,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'ten_nha_cung_cap',
    headerName: 'Tên khách hàng',
    width: 250,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'mat_hang',
    headerName: 'Tên vật tư thuế',
    width: 200,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'tien_hang',
    headerName: 'Tiền chưa thuế',
    width: 120,
    type: 'number',
    valueFormatter: (params: any) => {
      if (!params || params.value === null || params.value === '') {
        return '';
      }
      return new Intl.NumberFormat('vi-VN').format(params.value as number);
    },
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'thue_suat',
    headerName: 'Thuế suất',
    width: 100,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'tien_thue',
    headerName: 'Tiền thuế',
    width: 120,
    type: 'number',
    valueFormatter: (params: any) => {
      if (!params || params.value === null || params.value === '') {
        return '';
      }
      return new Intl.NumberFormat('vi-VN').format(params.value as number);
    },
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'tong_tien',
    headerName: 'Tổng tiền',
    width: 120,
    type: 'number',
    valueFormatter: (params: any) => {
      if (!params || params.value === null || params.value === '') {
        return '';
      }
      return new Intl.NumberFormat('vi-VN').format(params.value as number);
    },
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 200,
    renderCell: createBoldCellRenderer()
  },
  {
    field: 'ma_chung_tu',
    headerName: 'Mã chứng từ',
    width: 120,
    renderCell: createBoldCellRenderer()
  }
];
