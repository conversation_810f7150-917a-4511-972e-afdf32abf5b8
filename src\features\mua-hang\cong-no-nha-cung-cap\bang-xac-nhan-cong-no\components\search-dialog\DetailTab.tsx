import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { khachHangSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { DoiTuong } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  formMode: FormMode;
  nhaCungCap: DoiTuong | null;
  setNhaCungCap: (nhaCungCap: DoiTuong) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode, nhaCungCap, setNhaCungCap }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Hoá đơn từ/đến</Label>
          <FormField type='date' name='ngay_hd1' disabled={formMode === 'view'} />
          <FormField type='date' name='ngay_hd2' disabled={formMode === 'view'} className='ml-2' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhà cung cấp</Label>
          <SearchField<DoiTuong>
            type='text'
            name='ma_kh'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            dialogTitle='Danh mục đối tượng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            classNameRelatedField='w-full'
            onRowSelection={setNhaCungCap}
            value={nhaCungCap?.customer_code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tính số dư</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='so_du'
              disabled={formMode === 'view'}
              options={[
                { label: 'Có', value: '1' },
                { label: 'Không', value: '0' }
              ]}
              defaultValue={'0'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Xem chi tiết tài khoản</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='ct_yn'
              disabled={formMode === 'view'}
              options={[
                { label: 'Có - Xem chi tiết tài khoản đối ứng', value: '1' },
                { label: 'Không - Xem tổng chứng từ', value: '0' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='mau_bc'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn', value: '20' },
                { label: 'Mẫu ngoại tệ', value: '30' }
              ]}
              defaultValue={'20'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
