import { useState, useCallback } from 'react';
import {
  TatToanHoaDonItem,
  TatToanHoaDonResponse,
  TatToanHoaDonSearchFormValues,
  UseTatToanHoaDonReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): TatToanHoaDonItem[] => {
  return [
    {
      id: '1',
      ma_don_vi: 'CN',
      ma_chung_tu: 'HDB001',
      so_chung_tu: 'HDB2024001',
      ngay_chung_tu: '2024-01-15',
      ma_khach_hang: 'KH001',
      ten_khach_hang: 'Công ty TNHH ABC Technology',
      dien_giai: '<PERSON><PERSON> hàng phần mềm quản lý',
      tai_khoan: '131',
      tong_tien: 50000000,
      da_thanh_toan: 30000000,
      con_lai: 20000000,
      tien_tat_toan: 20000000,
      ngay_tat_toan: '2024-02-15',
      ly_do_tat_toan: '<PERSON><PERSON> to<PERSON> đủ theo hợp đồng'
    },
    {
      id: '2',
      ma_don_vi: 'BN',
      ma_chung_tu: 'HDB002',
      so_chung_tu: 'HDB2024002',
      ngay_chung_tu: '2024-01-16',
      ma_khach_hang: 'KH002',
      ten_khach_hang: 'Cửa hàng XYZ Trading',
      dien_giai: 'Bán thiết bị văn phòng',
      tai_khoan: '131',
      tong_tien: 25000000,
      da_thanh_toan: 25000000,
      con_lai: 0,
      tien_tat_toan: 25000000,
      ngay_tat_toan: '2024-01-20',
      ly_do_tat_toan: 'Thanh toán ngay khi giao hàng'
    },
    {
      id: '3',
      ma_don_vi: 'DN',
      ma_chung_tu: 'HDB003',
      so_chung_tu: 'HDB2024003',
      ngay_chung_tu: '2024-01-17',
      ma_khach_hang: 'KH003',
      ten_khach_hang: 'Đại lý 123 Distribution',
      dien_giai: 'Bán hàng điện tử tiêu dùng',
      tai_khoan: '131',
      tong_tien: 75000000,
      da_thanh_toan: 45000000,
      con_lai: 30000000,
      tien_tat_toan: 30000000,
      ngay_tat_toan: '2024-03-17',
      ly_do_tat_toan: 'Tất toán theo thỏa thuận gia hạn'
    },
    {
      id: '4',
      ma_don_vi: 'CN',
      ma_chung_tu: 'HDB004',
      so_chung_tu: 'HDB2024004',
      ngay_chung_tu: '2024-01-18',
      ma_khach_hang: 'KH004',
      ten_khach_hang: 'Khách lẻ Nguyễn Văn A',
      dien_giai: 'Bán dịch vụ tư vấn IT',
      tai_khoan: '511',
      tong_tien: 15000000,
      da_thanh_toan: 15000000,
      con_lai: 0,
      tien_tat_toan: 15000000,
      ngay_tat_toan: '2024-01-18',
      ly_do_tat_toan: 'Thanh toán ngay'
    },
    {
      id: '5',
      ma_don_vi: 'BN',
      ma_chung_tu: 'HDB005',
      so_chung_tu: 'HDB2024005',
      ngay_chung_tu: '2024-01-19',
      ma_khach_hang: 'KH005',
      ten_khach_hang: 'Công ty CP DEF Solutions',
      dien_giai: 'Bán giải pháp phần mềm ERP',
      tai_khoan: '131',
      tong_tien: 120000000,
      da_thanh_toan: 60000000,
      con_lai: 60000000,
      tien_tat_toan: 60000000,
      ngay_tat_toan: '2024-04-19',
      ly_do_tat_toan: 'Tất toán theo kế hoạch thanh toán'
    },
    {
      id: '6',
      ma_don_vi: 'DN',
      ma_chung_tu: 'HDB006',
      so_chung_tu: 'HDB2024006',
      ngay_chung_tu: '2024-01-20',
      ma_khach_hang: 'KH006',
      ten_khach_hang: 'Siêu thị GHI Mart',
      dien_giai: 'Bán hàng tiêu dùng nhanh',
      tai_khoan: '131',
      tong_tien: 35000000,
      da_thanh_toan: 20000000,
      con_lai: 15000000,
      tien_tat_toan: 15000000,
      ngay_tat_toan: '2024-02-20',
      ly_do_tat_toan: 'Tất toán theo hạn thanh toán'
    },
    {
      id: '7',
      ma_don_vi: 'CN',
      ma_chung_tu: 'HDB007',
      so_chung_tu: 'HDB2024007',
      ngay_chung_tu: '2024-01-21',
      ma_khach_hang: 'KH007',
      ten_khach_hang: 'Nhà phân phối JKL',
      dien_giai: 'Bán thiết bị công nghiệp',
      tai_khoan: '131',
      tong_tien: 90000000,
      da_thanh_toan: 45000000,
      con_lai: 45000000,
      tien_tat_toan: 45000000,
      ngay_tat_toan: '2024-05-21',
      ly_do_tat_toan: 'Tất toán theo hợp đồng dài hạn'
    },
    {
      id: '8',
      ma_don_vi: 'BN',
      ma_chung_tu: 'HDB008',
      so_chung_tu: 'HDB2024008',
      ngay_chung_tu: '2024-01-22',
      ma_khach_hang: 'KH008',
      ten_khach_hang: 'Cửa hàng MNO Electronics',
      dien_giai: 'Bán linh kiện điện tử',
      tai_khoan: '131',
      tong_tien: 18000000,
      da_thanh_toan: 18000000,
      con_lai: 0,
      tien_tat_toan: 18000000,
      ngay_tat_toan: '2024-01-25',
      ly_do_tat_toan: 'Thanh toán trong 3 ngày'
    },
    {
      id: '9',
      ma_don_vi: 'DN',
      ma_chung_tu: 'HDB009',
      so_chung_tu: 'HDB2024009',
      ngay_chung_tu: '2024-01-23',
      ma_khach_hang: 'KH009',
      ten_khach_hang: 'Công ty TNHH PQR Services',
      dien_giai: 'Bán dịch vụ bảo trì hệ thống',
      tai_khoan: '511',
      tong_tien: 40000000,
      da_thanh_toan: 24000000,
      con_lai: 16000000,
      tien_tat_toan: 16000000,
      ngay_tat_toan: '2024-03-23',
      ly_do_tat_toan: 'Tất toán theo tiến độ dự án'
    },
    {
      id: '10',
      ma_don_vi: 'CN',
      ma_chung_tu: 'HDB010',
      so_chung_tu: 'HDB2024010',
      ngay_chung_tu: '2024-01-24',
      ma_khach_hang: 'KH010',
      ten_khach_hang: 'Khách lẻ Trần Thị B',
      dien_giai: 'Bán sản phẩm phần mềm cá nhân',
      tai_khoan: '131',
      tong_tien: 8000000,
      da_thanh_toan: 8000000,
      con_lai: 0,
      tien_tat_toan: 8000000,
      ngay_tat_toan: '2024-01-24',
      ly_do_tat_toan: 'Thanh toán ngay khi mua'
    }
  ];
};

/**
 * Custom hook for managing TatToanHoaDon (Invoice Settlement) data
 *
 * This hook provides functionality to fetch invoice settlement data
 * with mock support for testing and development purposes.
 */
export function useTatToanHoaDon(searchParams: TatToanHoaDonSearchFormValues): UseTatToanHoaDonReturn {
  const [data, setData] = useState<TatToanHoaDonItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: TatToanHoaDonSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<TatToanHoaDonResponse>('/ban-hang/tat-toan-hoa-don/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
