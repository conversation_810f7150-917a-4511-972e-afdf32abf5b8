import { GridColDef } from '@mui/x-data-grid';

export const collectionDetailReportColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  {
    field: 'unit',
    headerName: 'Đơn vị',
    width: 100
  },
  {
    field: 'invoiceDate',
    headerName: 'Ngày hóa đơn',
    width: 120
  },
  {
    field: 'invoiceNumber',
    headerName: 'Số hóa đơn',
    width: 120
  },
  {
    field: 'customerCode',
    headerName: 'Mã khách hàng',
    width: 120
  },
  {
    field: 'customerName',
    headerName: 'Tên khách hàng',
    width: 200,
    cellClassName: params => {
      return params.row.isSummary ? 'font-bold' : '';
    }
  },
  {
    field: 'description',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'totalAmount',
    headerName: 'Tổng tiền',
    width: 120,
    cellClassName: params => {
      return params.row.isSummary ? 'font-bold' : '';
    }
  },
  {
    field: 'paidAmount',
    headerName: 'Đã thu',
    width: 120,
    cellClassName: params => {
      return params.row.isSummary ? 'font-bold' : '';
    }
  },
  {
    field: 'remainingAmount',
    headerName: 'Còn lại',
    width: 120,
    cellClassName: params => {
      return params.row.isSummary ? 'font-bold' : '';
    }
  },
  {
    field: 'paymentDate',
    headerName: 'Ngày thu',
    width: 120
  },
  {
    field: 'paymentMethod',
    headerName: 'Phương thức',
    width: 120
  },
  {
    field: 'paymentAmount',
    headerName: 'Số tiền thu',
    width: 120,
    cellClassName: params => {
      return params.row.isSummary ? 'font-bold' : '';
    }
  },
  {
    field: 'paymentDescription',
    headerName: 'Diễn giải thu',
    width: 150
  },
  {
    field: 'accountCode',
    headerName: 'Mã tài khoản',
    width: 120
  },
  {
    field: 'accountName',
    headerName: 'Tên tài khoản',
    width: 150
  },
  {
    field: 'employeeName',
    headerName: 'Nhân viên',
    width: 150
  },
  {
    field: 'department',
    headerName: 'Bộ phận',
    width: 120
  },
  {
    field: 'project',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'contract',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'paymentPhase',
    headerName: 'Đợt thanh toán',
    width: 120
  },
  {
    field: 'agreement',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'currency',
    headerName: 'Loại tiền',
    width: 100
  },
  {
    field: 'exchangeRate',
    headerName: 'Tỷ giá',
    width: 100
  },
  {
    field: 'foreignAmount',
    headerName: 'Tiền ngoại tệ',
    width: 120,
    cellClassName: params => {
      return params.row.isSummary ? 'font-bold' : '';
    }
  },
  {
    field: 'documentCode',
    headerName: 'Mã c/từ',
    width: 120
  }
];

// Customer columns
export const customerColumns = [
  {
    field: 'customerCode',
    headerName: 'Mã đối tượng',
    width: 120
  },
  {
    field: 'customerName',
    headerName: 'Tên đối tượng',
    width: 200
  },
  {
    field: 'receivable',
    headerName: 'Công nợ p/thu',
    width: 120
  },
  {
    field: 'payable',
    headerName: 'Công nợ p/trả',
    width: 120
  },
  {
    field: 'taxCode',
    headerName: 'Mã số thuế',
    width: 120
  },
  {
    field: 'email',
    headerName: 'Email',
    width: 150
  },
  {
    field: 'phone',
    headerName: 'Số điện thoại',
    width: 120
  }
];

// Customer Group columns
export const customerGroupColumns = [
  {
    field: 'customerGroupCode',
    headerName: 'Mã nhóm khách hàng',
    width: 150
  },
  {
    field: 'customerGroupName',
    headerName: 'Tên nhóm khách hàng',
    width: 250
  }
];

// Region columns
export const regionColumns = [
  {
    field: 'regionCode',
    headerName: 'Mã khu vực',
    width: 150
  },
  {
    field: 'regionName',
    headerName: 'Tên khu vực',
    width: 250
  }
];

// Account columns
export const accountColumns = [
  {
    field: 'accountCode',
    headerName: 'Mã tài khoản',
    width: 120
  },
  {
    field: 'accountName',
    headerName: 'Tên tài khoản',
    width: 250
  },
  {
    field: 'accountType',
    headerName: 'Loại tài khoản',
    width: 150
  }
];

// Employee columns
export const employeeColumns = [
  {
    field: 'employeeCode',
    headerName: 'Mã nhân viên',
    width: 150
  },
  {
    field: 'employeeName',
    headerName: 'Tên nhân viên',
    width: 250
  }
];
