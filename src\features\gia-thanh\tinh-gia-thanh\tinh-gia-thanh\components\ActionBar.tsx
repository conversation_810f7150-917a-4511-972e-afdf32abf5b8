import { Pen<PERSON>l, Trash, Copy, FileSearch, Check, FilePlus2 } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onCalculateClick?: () => void;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onMoveUpClick?: () => void;
  onMoveDownClick?: () => void;
  onSaveOrderClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onCalculateClick,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onMoveUpClick,
  onMoveDownClick,
  onSaveOrderClick,
  onSearchClick,
  onRefreshClick
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Tính giá thành các bước</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <div className='mr-2 size-2 rounded-full bg-red-500' />
          Đơn vị [0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ] kỳ{' '}
          {new Date().getMonth() + 1} năm {new Date().getFullYear()}
        </span>
      </div>
    }
  >
    {onCalculateClick && (
      <AritoActionButton title='Tính các bước' icon={Check} onClick={onCalculateClick} disabled={isViewDisabled} />
    )}
    {onAddClick && <AritoActionButton title='Thêm' icon={FilePlus2} onClick={onAddClick} disabled={isViewDisabled} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />}
    {onCopyClick && <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chuyển lên',
          icon: <AritoIcon icon={3} />,
          onClick: onMoveUpClick,
          group: 0
        },
        {
          title: 'Chuyển xuống',
          icon: <AritoIcon icon={30} />,
          onClick: onMoveDownClick,
          group: 0
        },
        {
          title: 'Lưu thứ tự',
          icon: <AritoIcon icon={294} />,
          onClick: onSaveOrderClick,
          group: 0
        },
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: onSearchClick,
          group: 1
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
