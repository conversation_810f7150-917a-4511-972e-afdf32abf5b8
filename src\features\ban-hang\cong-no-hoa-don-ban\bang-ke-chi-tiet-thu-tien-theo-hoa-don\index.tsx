'use client';

import { EditPrintTemplateDialog } from './components/edit-print-template';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import ActionBar from './components/ActionBar';

export default function DebtBalanceReportPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const { tables, handleRowClick, refreshData } = useTableData(searchParams || {});

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers(refreshData);

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            searchParams={searchParams}
            className='border-b border-gray-200'
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} />
          </div>
        </>
      )}
    </div>
  );
}
