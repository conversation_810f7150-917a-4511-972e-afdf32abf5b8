import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import type { BangCanDoiPhatSinhCongNoItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BangCanDoiPhatSinhCongNoItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    // Tính tổng từ dữ liệu thực
    const calculateTotals = (items: BangCanDoiPhatSinhCongNoItem[]) => {
      return items.reduce(
        (totals, item) => {
          return {
            du_no_dau: totals.du_no_dau + (item.du_no_dau || 0),
            du_co_dau: totals.du_co_dau + (item.du_co_dau || 0),
            ps_no: totals.ps_no + (item.ps_no || 0),
            ps_co: totals.ps_co + (item.ps_co || 0),
            du_no_cuoi: totals.du_no_cuoi + (item.du_no_cuoi || 0),
            du_co_cuoi: totals.du_co_cuoi + (item.du_co_cuoi || 0)
          };
        },
        {
          du_no_dau: 0,
          du_co_dau: 0,
          ps_no: 0,
          ps_co: 0,
          du_no_cuoi: 0,
          du_co_cuoi: 0
        }
      );
    };

    const totals = calculateTotals(data);

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'tai_khoan',
            headerName: 'Tài khoản',
            width: 100,
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'ma_khach_hang',
            headerName: 'Mã NCC',
            width: 100,
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'ten_khach_hang',
            headerName: 'Tên nhà cung cấp',
            width: 250,
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-bold' : '')
          },
          {
            field: 'du_no_dau',
            headerName: 'Dư nợ đầu',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.du_no_dau > 0 ? params.row.du_no_dau.toLocaleString('vi-VN') : '';
            }
          },
          {
            field: 'du_co_dau',
            headerName: 'Dư có đầu',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.du_co_dau > 0 ? params.row.du_co_dau.toLocaleString('vi-VN') : '';
            }
          },
          {
            field: 'ps_no',
            headerName: 'Ps nợ',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.ps_no > 0 ? params.row.ps_no.toLocaleString('vi-VN') : '';
            }
          },
          {
            field: 'ps_co',
            headerName: 'Ps có',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.ps_co > 0 ? params.row.ps_co.toLocaleString('vi-VN') : '';
            }
          },
          {
            field: 'du_no_cuoi',
            headerName: 'Dư nợ cuối',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.du_no_cuoi > 0 ? params.row.du_no_cuoi.toLocaleString('vi-VN') : '';
            }
          },
          {
            field: 'du_co_cuoi',
            headerName: 'Dư có cuối',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.du_co_cuoi > 0 ? params.row.du_co_cuoi.toLocaleString('vi-VN') : '';
            }
          }
        ],
        rows: [
          {
            id: 'total-row',
            tai_khoan: '',
            ma_khach_hang: '',
            ten_khach_hang: 'Tổng cộng',
            du_no_dau: totals.du_no_dau,
            du_co_dau: totals.du_co_dau,
            ps_no: totals.ps_no,
            ps_co: totals.ps_co,
            du_no_cuoi: totals.du_no_cuoi,
            du_co_cuoi: totals.du_co_cuoi,
            isTotal: true // Đánh dấu đây là dòng tổng
          },
          ...data
        ]
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {};

  return {
    tables,
    handleRowClick
  };
}
