import { <PERSON>, Pin, Printer, Refresh<PERSON><PERSON>, FileText } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onPrintClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onPrintClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onEditPrintTemplateClick
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Báo cáo kết quả hoạt động kinh doanh</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <div className='mr-2 size-2 rounded-full bg-red-500' />
          Từ ngày {new Date().toLocaleDateString()} đến ngày {new Date().toLocaleDateString()}
        </span>
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}

    <AritoMenuButton
      title='In ấn'
      icon={Printer}
      items={[
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 0
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 0
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh (song ngữ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ (song ngữ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 2
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 2
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh (song ngữ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 3
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ (song ngữ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 3
        }
      ]}
    />

    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onExportClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
