import { z } from 'zod';

export const SearchFormSchema = z.object({
  // BasicInfoTab fields
  ngay_ct1: z.date().optional(),
  ngay_ct2: z.date().optional(),
  ngay_tt: z.date().optional(),
  tk: z.string().optional(),

  // DetailTab fields
  ma_kh: z.string().optional(),
  nh_kh1: z.string().optional(),
  nh_kh2: z.string().optional(),
  nh_kh3: z.string().optional(),
  rg_code: z.string().optional(),
  tt_yn: z.string().optional(),
  ct_thu: z.string().optional(),
  mau_bc: z.string().optional(),

  // OtherTab fields
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  // BasicInfoTab initial values
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  ngay_tt: new Date(),
  tk: '',

  // DetailTab initial values
  ma_kh: '',
  nh_kh1: '',
  nh_kh2: '',
  nh_kh3: '',
  rg_code: '',
  tt_yn: '1',
  ct_thu: '1',
  mau_bc: '20',

  // OtherTab initial values
  so_ct1: '',
  so_ct2: '',
  report_filtering: '0', // Default to "Người dùng tự lọc"
  data_analysis_struct: '0' // Default to "Không phân tích"
};
