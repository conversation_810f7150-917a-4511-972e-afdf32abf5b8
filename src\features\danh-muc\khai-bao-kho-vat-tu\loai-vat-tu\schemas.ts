import { z } from 'zod';

/**
 * Schema for material type form data
 */
export const materialTypeSchema = z.object({
  ma_loai_vat_tu: z.string().min(1, 'Mã loại vật tư không được để trống'),
  ten_loai_vat_tu: z.string().min(1, 'Tên loại vật tư không được để trống'),
  ten_khac: z.string().nullable().optional(),
  tk_kho: z.string().nullable().optional(),
  tk_doanh_thu: z.string().nullable().optional(),
  tk_gia_von: z.string().nullable().optional(),
  trang_thai: z.number().min(0).max(1)
});

/**
 * Type for material type form data
 */
export type MaterialTypeFormattedData = z.infer<typeof materialTypeSchema>;
