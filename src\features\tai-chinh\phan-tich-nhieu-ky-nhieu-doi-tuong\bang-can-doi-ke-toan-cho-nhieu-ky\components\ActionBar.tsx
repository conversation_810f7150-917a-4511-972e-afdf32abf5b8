import { FileText, Lock, Printer, RefreshCw, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
export const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportClick,
  onEditPrintTemplateClick
}) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Bảng cân đối kế toán cho nhiều kỳ</h1>}>
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}
    {onPrintClick && (
      <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} disabled={isViewDisabled} />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Lock} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onExportClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
