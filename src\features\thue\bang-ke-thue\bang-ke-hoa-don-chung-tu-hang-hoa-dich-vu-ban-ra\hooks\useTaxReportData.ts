import { useState, useCallback, useEffect } from 'react';
import { TaxReportItem, TaxReportResponse, SearchFormValues, UseTaxReportReturn } from '../schema';
import api from '@/lib/api';

const generateMockData = (): TaxReportItem[] => {
  // Regular data rows
  const mockData = Array.from({ length: 10 }, (_, index) => ({
    id: (index + 1).toString(),
    stt: index + 1,
    ngay_ct: new Date(2023, 0, index + 1), // January 1-10, 2023
    so_ct: `HD${1000 + index}`,
    so_ct2: `CT${2000 + index}`,
    ngay_lct: new Date(2023, 0, index + 5), // January 5-14, 2023
    ma_ct: `AA/23E`,
    ma_so_thue: `${1000000000 + index * 111111}`,
    ten_kh: `Khách hàng ${index + 1}`,
    ten_vt_thue: `Hàng hóa dịch vụ ${index + 1}`,
    t_tien2: 10000000 + index * 1000000,
    thue_suat: '10%',
    t_thue: (10000000 + index * 1000000) * 0.1,
    ghi_chu: `Ghi chú cho hóa đơn ${index + 1}`,
    ma_nt: 'VND',
    ten_unit: `Đơn vị ${index + 1}`,
    ten_unit2: `Đơn vị 2 ${index + 1}`,
    ma_ct0: `MCT0${index + 1}`,
    ma_thue: `MT${index + 1}`,
    nhom_thue: `NT${index + 1}`,
    ten_thue: `Thuế ${index + 1}`,
    ten_thue2: `Thuế 2 ${index + 1}`,
    ten_nh: `Ngân hàng ${index + 1}`,
    ten_nh2: `Ngân hàng 2 ${index + 1}`,
    ma_mau_ct: `MMC${index + 1}`,
    stt2: index + 1,
    ten_kh2: `Khách hàng 2 ${index + 1}`,
    xten_kh: `Tên mở rộng KH ${index + 1}`,
    xten_kh2: `Tên mở rộng KH 2 ${index + 1}`,
    xid: `XID${index + 1}`,
    line: index + 1,
    sysorder: index + 1,
    sysprint: 1,
    systotal: 0,
    excel: 0
  }));

  // Calculate totals for summary rows
  const totalTTien2 = mockData.reduce((sum, item) => sum + (item.t_tien2 as number), 0);
  const totalTThue = mockData.reduce((sum, item) => sum + (item.t_thue as number), 0);

  // Summary rows for "ban ra" (sales) - different categories than "mua vao" (purchases)
  const summaryRows: TaxReportItem[] = [
    {
      id: 'summary-1',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng cộng',
      ten_vt_thue: '',
      t_tien2: totalTTien2,
      thue_suat: '',
      t_thue: totalTThue,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-8',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-2',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '1. Hàng hóa, dịch vụ không chịu thuế GTGT',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.7,
      thue_suat: '',
      t_thue: totalTThue * 0.7,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-9',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-3',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.7,
      thue_suat: '',
      t_thue: totalTThue * 0.7,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-4',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '2. Hàng hóa, dịch vụ chịu thuế suất thuế GTGT 0%',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.3,
      thue_suat: '',
      t_thue: totalTThue * 0.3,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-10',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-5',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.3,
      thue_suat: '',
      t_thue: totalTThue * 0.3,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-6',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '3. Hàng hoá, dịch vụ chịu thuế suất thuế GTGT 5%',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.2,
      thue_suat: '',
      t_thue: totalTThue * 0.2,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-11',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-12',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.2,
      thue_suat: '',
      t_thue: totalTThue * 0.2,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-13',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '4. Hàng hoá, dịch vụ chịu thuế suất thuế GTGT 10%',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.3,
      thue_suat: '',
      t_thue: totalTThue * 0.3,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-14',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-15',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.3,
      thue_suat: '',
      t_thue: totalTThue * 0.3,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-16',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '5. Hàng hóa, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.15,
      thue_suat: '',
      t_thue: totalTThue * 0.15,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-17',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-18',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.15,
      thue_suat: '',
      t_thue: totalTThue * 0.15,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-19',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '4. Hàng hoá, dịch vụ chịu thuế suất thuế GTGT 8%',
      ten_vt_thue: '',
      t_tien2: totalTTien2 * 0.25,
      thue_suat: '',
      t_thue: totalTThue * 0.25,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-20',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: '',
      ten_vt_thue: '',
      t_tien2: '',
      thue_suat: '',
      t_thue: '',
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    },
    {
      id: 'summary-7',
      isSummary: true,
      stt: '',
      ngay_ct: null,
      so_ct: '//',
      so_ct2: '//',
      ngay_lct: null,
      ma_ct: '//',
      ma_so_thue: '',
      ten_kh: 'Tổng cộng',
      ten_vt_thue: '',
      t_tien2: totalTTien2,
      thue_suat: '',
      t_thue: totalTThue,
      ghi_chu: '',
      ma_nt: '',
      ten_unit: '',
      ten_unit2: '',
      ma_ct0: '',
      ma_thue: '',
      nhom_thue: '',
      ten_thue: '',
      ten_thue2: '',
      ten_nh: '',
      ten_nh2: '',
      ma_mau_ct: '',
      stt2: '',
      ten_kh2: '',
      xten_kh: '',
      xten_kh2: '',
      xid: '',
      line: '',
      sysorder: '',
      sysprint: '',
      systotal: '',
      excel: ''
    }
  ];

  // Combine regular data with summary rows
  return [...mockData, ...summaryRows];
};

/**
 * Custom hook for managing Tax Report data
 *
 * This hook provides functionality to fetch tax report data
 * with mock support for testing and development purposes.
 */
export function useTaxReportData(searchParams: SearchFormValues): UseTaxReportReturn {
  const [data, setData] = useState<TaxReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<TaxReportResponse>(
        '/thue/bang-ke-thue/bang-ke-hoa-don-chung-tu-hang-hoa-dich-vu-ban-ra/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
