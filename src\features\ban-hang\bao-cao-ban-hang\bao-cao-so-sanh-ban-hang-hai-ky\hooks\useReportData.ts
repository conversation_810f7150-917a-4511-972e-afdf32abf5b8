import { useEffect } from 'react';
import { useBaoCaoSoSanhBanHangHaiKy } from './useBaoCaoSoSanhBanHangHaiKy';
import { useActionHandlers } from './useActionHandlers';
import { useDialogState } from './useDialogState';
import { useTableData } from './useTableData';

export function useReportData() {
  const dialogState = useDialogState();
  const reportDataHook = useBaoCaoSoSanhBanHangHaiKy();
  const tableData = useTableData(reportDataHook.data);
  const actionHandlers = useActionHandlers();

  // Initialize table data when the component mounts
  useEffect(() => {
    // Load initial data with default search parameters
    reportDataHook.fetchData({
      fromDate: '',
      toDate: '',
      previousFromDate: '',
      previousToDate: '',
      reportBy: 'customer',
      groupBy: 'product'
    });
  }, []);

  return {
    initialSearchDialogOpen: dialogState.initialSearchDialogOpen,
    editPrintTemplateDialogOpen: dialogState.editPrintTemplateDialogOpen,
    showTable: dialogState.showTable,

    handleInitialSearchClose: dialogState.handleInitialSearchClose,
    handleInitialSearch: dialogState.handleInitialSearch,
    handleSearchClick: dialogState.handleSearchClick,
    handleEditPrintTemplateClick: dialogState.handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog: dialogState.handleClosePrintTemplateDialog,
    handleSavePrintTemplate: dialogState.handleSavePrintTemplate,

    tables: tableData.tables,
    handleRowClick: tableData.handleRowClick,

    // Report data methods
    reportData: reportDataHook.data,
    isLoading: reportDataHook.isLoading,
    error: reportDataHook.error,
    fetchReportData: reportDataHook.fetchData,
    refreshReportData: reportDataHook.refreshData,

    handleRefreshClick: actionHandlers.handleRefreshClick,
    handleFixedColumnsClick: actionHandlers.handleFixedColumnsClick,
    handleExportDataClick: actionHandlers.handleExportDataClick
  };
}
