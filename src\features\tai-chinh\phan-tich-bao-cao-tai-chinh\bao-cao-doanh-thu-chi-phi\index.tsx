'use client';

import React from 'react';
import EditPrintTemplateDialog from './components/editPrintTemplateDialog';
import AritoDataTables from '@/components/custom/arito/data-tables';
import SearchDialog from './components/searchDialog';
import { ActionBar } from './components/ActionBar';
import { useReportData } from './hooks';

export default function BaoCaoDoanhThuChiPhi() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,

    tables,
    handleRowClick,
    isLoading,
    error,
    selectedRowId,

    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  } = useReportData();

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <SearchDialog open={initialSearchDialogOpen} onClose={handleInitialSearchClose} onSearch={handleInitialSearch} />

      <EditPrintTemplateDialog open={editPrintTemplateDialogOpen} onClose={handleClosePrintTemplateDialog} />

      {showTable && (
        <>
          <ActionBar
            onSearch={handleSearchClick}
            onRefresh={handleRefreshClick}
            onExportData={handleExportDataClick}
            onFixedColumns={handleFixedColumnsClick}
            onEditPrintTemplate={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} selectedRowId={selectedRowId || undefined} />
          </div>
        </>
      )}
    </div>
  );
}
