import { SearchFormValues } from '../schema';

export interface BusinessReportItem {
  id: string;
  ma_so: string;
  chi_tieu: string;
  thuyet_minh: string;
  tong_tien: number;
  binh_quoi: number;
  nguyen_kiem: number;
  le_van_sy: number;
  dinh_bo_linh: number;
  duong_so_8: number;
  phan_van_tri: number;
  ngo_tat_to: number;
  nguyen_van_qua: number;
  van_kiep: number;
  tan_quy: number;
  dong_den: number;
}

export interface BusinessReportResponse {
  results: BusinessReportItem[];
  total: number;
}

export interface UseBusinessReportReturn {
  data: BusinessReportItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
