'use client';

import React, { useState } from 'react';
import { useRowSelection, useDialogState, useBusinessReportData } from './hooks';
import { ActionBar, SearchDialog, EditPrintTemplateDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { initialSearchValues, SearchFormValues } from './schema';
import { IncomeStatementColumns } from './cols-definition';

export default function BaoCaoKetQuaHoatDongKinhDoanh() {
  const [searchParams, setSearchParams] = useState<SearchFormValues>(initialSearchValues);
  const { data, isLoading, error, refreshData } = useBusinessReportData(searchParams);

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showData,
    showSearchDialog,
    showEditPrintTemplateDialog,
    showSaveTemplateDialog,

    openSearchDialog,
    closeSearchDialog,
    openEditPrintTemplateDialog,
    closeEditPrintTemplateDialog,
    openSaveTemplateDialog,
    closeSaveTemplateDialog,

    handleSearchButtonClick,
    handleSearchCompleted,
    handleEditPrintTemplateButtonClick,
    handleSaveTemplateButtonClick,
    handleRefreshButtonClick,
    handleFixedColumnsButtonClick,
    handleExportButtonClick
  } = useDialogState(clearSelection);

  const handleSearch = (newSearchParams: SearchFormValues) => {
    setSearchParams(newSearchParams);
    handleSearchCompleted(); // This will close dialog and show data
  };

  const handleRefresh = async () => {
    await refreshData();
    handleRefreshButtonClick();
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: IncomeStatementColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          openSearchDialog={showSearchDialog}
          onCloseSearchDialog={closeSearchDialog}
          onSearch={handleSearch}
          openSaveTemplateDialog={showSaveTemplateDialog}
          onOpenSaveTemplateDialog={openSaveTemplateDialog}
          onCloseSaveTemplateDialog={closeSaveTemplateDialog}
          onSaveTemplate={handleSaveTemplateButtonClick}
        />
      )}

      <EditPrintTemplateDialog
        open={showEditPrintTemplateDialog}
        onClose={closeEditPrintTemplateDialog}
        onSave={handleEditPrintTemplateButtonClick}
      />

      {showData && (
        <>
          <ActionBar
            searchParams={searchParams}
            onSearchClick={handleSearchButtonClick}
            onRefreshClick={handleRefresh}
            onFixedColumnsClick={handleFixedColumnsButtonClick}
            onExportClick={handleExportButtonClick}
            onEditPrintTemplateClick={handleEditPrintTemplateButtonClick}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
