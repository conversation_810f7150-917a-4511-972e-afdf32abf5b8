import { GridColDef } from '@mui/x-data-grid';

export const fullyDepreciatedAssetsColumns: GridColDef[] = [
  {
    field: 'stt',
    headerName: 'STT',
    width: 70
  },
  {
    field: 'ma_tai_san',
    headerName: 'Mã tài sản',
    width: 120
  },
  {
    field: 'ten_tai_san',
    headerName: 'Tên tài sản',
    width: 200
  },
  {
    field: 'nguyen_gia',
    headerName: 'Nguyên giá',
    width: 120,
    type: 'number'
  },
  {
    field: 'gt_khau_hao',
    headerName: 'Giá trị đã khấu hao',
    width: 150,
    type: 'number'
  },
  {
    field: 'con_lai',
    headerName: 'Còn lại',
    width: 150,
    type: 'number'
  },
  {
    field: 'kh_trong_ky',
    headerName: 'KH trong kỳ',
    width: 150,
    type: 'number'
  },
  {
    field: 'ngay_mua',
    headerName: 'Ngày mua',
    width: 120,
    type: 'date',
    valueGetter: (params: any) => {
      const value = params.value;
      if (value instanceof Date) return value;
      if (typeof value === 'string') return new Date(value);
      return null;
    }
  },
  {
    field: 'ngay_tinh_kh',
    headerName: 'Ngày tính kh',
    width: 120,
    type: 'date',
    valueGetter: (params: any) => {
      const value = params.value;
      if (value instanceof Date) return value;
      if (typeof value === 'string') return new Date(value);
      return null;
    }
  },
  {
    field: 'so_ky_khau_hao',
    headerName: 'Số kỳ khấu hao',
    width: 150,
    type: 'number'
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày ct',
    width: 120,
    type: 'date',
    valueGetter: (params: any) => {
      const value = params.value;
      if (value instanceof Date) return value;
      if (typeof value === 'string') return new Date(value);
      return null;
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số ct',
    width: 120
  },
  {
    field: 'tk_tai_san',
    headerName: 'Tk tài sản',
    width: 120
  },
  {
    field: 'tk_khau_hao',
    headerName: 'Tk khấu hao',
    width: 120
  },
  {
    field: 'tk_chi_phi',
    headerName: 'Tk chi phí',
    width: 120
  },
  {
    field: 'ma_bo_phan',
    headerName: 'Mã bộ phận',
    width: 120
  },
  {
    field: 'nhom_tai_san_1',
    headerName: 'Nhóm tài sản 1',
    width: 150
  },
  {
    field: 'nhom_tai_san_2',
    headerName: 'Nhóm tài sản 2',
    width: 150
  },
  {
    field: 'nhom_tai_san_3',
    headerName: 'Nhóm tài sản 3',
    width: 150
  }
];

export const assetTypeSearchColumns = [
  { field: 'loai_tai_san', headerName: 'Loại tài sản', width: 150 },
  { field: 'ten_loai_tai_san', headerName: 'Tên loại tài sản', width: 250 }
];

export const departmentSearchColumns = [
  { field: 'ma_bo_phan', headerName: 'Mã bộ phận', width: 150 },
  { field: 'ten_bo_phan', headerName: 'Tên bộ phận', width: 250 }
];

export const assetGroupSearchColumns = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_nhom', headerName: 'Tên nhóm', width: 250 }
];

// Định nghĩa các cột phân tích
export const columnAnalysisItems = [
  { label: 'Stt', type: 'string' },
  { label: 'Ngày c/từ', type: 'string' },
  { label: 'Số c/từ', type: 'array' },
  { label: 'Ngày hóa đơn', type: 'string' },
  { label: 'Số hóa đơn', type: 'array' },
  { label: 'Mã khách hàng', type: 'array' },
  { label: 'Tên khách hàng', type: 'array' },
  { label: 'Mã NVBH', type: 'array' },
  { label: 'Tên NVBH', type: 'array' },
  { label: 'Tổng tiền HĐ nt', type: 'number' },
  { label: 'Đã thu nt', type: 'number' },
  { label: 'Phải thu nt', type: 'number' },
  { label: 'Tổng tiền HĐ', type: 'number' },
  { label: 'Đã thu', type: 'number' },
  { label: 'Phải thu', type: 'number' },
  { label: 'Trong hạn', type: 'number' },
  { label: 'Quá hạn %s1 ngày', type: 'number' },
  { label: 'Quá hạn %s2 ngày', type: 'number' },
  { label: 'Quá hạn %s3 ngày', type: 'number' },
  { label: 'Quá hạn %s4 ngày', type: 'number' },
  { label: 'Quá hạn trên %s5 ngày', type: 'number' },
  { label: 'Ngày đến hạn', type: 'string' },
  { label: 'Hạn tt', type: 'string' },
  { label: 'Số ngày đến hạn', type: 'number' },
  { label: 'Diễn giải', type: 'string' },
  { label: 'ID', type: 'string' },
  { label: 'Mã chứng từ', type: 'array' },
  { label: 'Đơn vị', type: 'array' }
];

export const printTemplateColumns: GridColDef[] = [
  { field: 'column', headerName: 'Cột', width: 150, editable: true },
  { field: 'columnName', headerName: 'Tên cột', width: 150, editable: true },
  {
    field: 'englishName',
    headerName: 'Tên tiếng Anh',
    width: 150,
    editable: true
  },
  {
    field: 'width',
    headerName: 'Độ rộng',
    width: 100,
    editable: true,
    type: 'number'
  },
  {
    field: 'format',
    headerName: 'Định dạng',
    width: 120,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Text', 'Number', 'Currency', 'Date', 'Percentage']
  },
  {
    field: 'alignment',
    headerName: 'Căn chỉnh',
    width: 100,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Left', 'Center', 'Right']
  },
  {
    field: 'bold',
    headerName: 'In đậm',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'italic',
    headerName: 'Nghiêng',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'total',
    headerName: 'Tổng',
    width: 80,
    editable: true,
    type: 'boolean'
  }
];

export const defaultTableData = [
  {
    id: 1,
    columnName: 'Mã khách hàng',
    englishName: 'Customer Code',
    width: 120,
    format: 'Text',
    alignment: 'Left',
    bold: false,
    italic: false,
    total: false
  },
  {
    id: 2,
    columnName: 'Tên khách hàng',
    englishName: 'Customer Name',
    width: 200,
    format: 'Text',
    alignment: 'Left',
    bold: false,
    italic: false,
    total: false
  },
  {
    id: 3,
    columnName: 'Số lượng',
    englishName: 'Quantity',
    width: 100,
    format: 'Number',
    alignment: 'Right',
    bold: false,
    italic: false,
    total: true
  },
  {
    id: 4,
    columnName: 'Đơn giá',
    englishName: 'Price',
    width: 120,
    format: 'Currency',
    alignment: 'Right',
    bold: false,
    italic: false,
    total: false
  },
  {
    id: 5,
    columnName: 'Thành tiền',
    englishName: 'Amount',
    width: 120,
    format: 'Currency',
    alignment: 'Right',
    bold: false,
    italic: false,
    total: true
  }
];
