import { GridColDef } from '@mui/x-data-grid';
import React, { useState } from 'react';
import { AritoInputTable } from '@/components/custom/arito/input-table';

export const EditPrintTemplatePrintColumns: React.FC = () => {
  const columns: GridColDef[] = [
    { field: 'columnName', headerName: 'Tên cột', width: 150, editable: true },
    {
      field: 'englishName',
      headerName: 'Tên tiếng Anh',
      width: 150,
      editable: true
    },
    {
      field: 'width',
      headerName: 'Độ rộng',
      width: 100,
      editable: true,
      type: 'number'
    },
    {
      field: 'format',
      headerName: 'Định dạng',
      width: 120,
      editable: true,
      type: 'singleSelect',
      valueOptions: ['Text', 'Number', 'Currency', 'Date', 'Percentage']
    },
    {
      field: 'alignment',
      headerName: 'Căn chỉnh',
      width: 100,
      editable: true,
      type: 'singleSelect',
      valueOptions: ['Left', 'Center', 'Right']
    },
    {
      field: 'bold',
      headerName: 'In đậm',
      width: 80,
      editable: true,
      type: 'boolean'
    },
    {
      field: 'italic',
      headerName: 'Nghiêng',
      width: 80,
      editable: true,
      type: 'boolean'
    },
    {
      field: 'total',
      headerName: 'Tổng',
      width: 80,
      editable: true,
      type: 'boolean'
    }
  ];

  // Initial data for the table - updated for purchase orders
  const [tableData, setTableData] = useState([
    {
      id: 1,
      columnName: 'Mã nhà cung cấp',
      englishName: 'Supplier Code',
      width: 120,
      format: 'Text',
      alignment: 'Left',
      bold: false,
      italic: false,
      total: false
    },
    {
      id: 2,
      columnName: 'Tên nhà cung cấp',
      englishName: 'Supplier Name',
      width: 200,
      format: 'Text',
      alignment: 'Left',
      bold: false,
      italic: false,
      total: false
    },
    {
      id: 3,
      columnName: 'Số lượng',
      englishName: 'Quantity',
      width: 100,
      format: 'Number',
      alignment: 'Right',
      bold: false,
      italic: false,
      total: true
    },
    {
      id: 4,
      columnName: 'Đơn giá',
      englishName: 'Price',
      width: 120,
      format: 'Currency',
      alignment: 'Right',
      bold: false,
      italic: false,
      total: false
    },
    {
      id: 5,
      columnName: 'Thành tiền',
      englishName: 'Amount',
      width: 120,
      format: 'Currency',
      alignment: 'Right',
      bold: false,
      italic: false,
      total: true
    }
  ]);

  const handleTableChange = (newData: any[]) => {
    setTableData(newData);
  };

  return (
    <div className='space-y-4'>
      <div className='h-[400px]'>
        <AritoInputTable
          value={tableData}
          columns={columns}
          onChange={handleTableChange}
          mode='edit'
          tableActionButtons={['add', 'delete', 'copy', 'moveUp', 'moveDown', 'pin', 'export', 'paste']}
        />
      </div>
    </div>
  );
};

export default EditPrintTemplatePrintColumns;
