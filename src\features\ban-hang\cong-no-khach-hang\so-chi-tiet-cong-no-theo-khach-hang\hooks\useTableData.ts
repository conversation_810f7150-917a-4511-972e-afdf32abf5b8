import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { SoChiTietCongNoTheoKhachHangItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: SoChiTietCongNoTheoKhachHangItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_unit',
            headerName: 'Đơn vị',
            width: 150,
            renderCell: (params: any) => {
              return params.row.ma_unit || '';
            }
          },
          {
            field: 'ngay_ct',
            headerName: 'Ngày c/từ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ngay_ct || '';
            }
          },
          {
            field: 'so_ct',
            headerName: 'Số c/từ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.so_ct || '';
            }
          },
          {
            field: 'ngay_ct0',
            headerName: 'Ng<PERSON>y hóa đơn',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ngay_ct0 || '';
            }
          },
          {
            field: 'so_ct0',
            headerName: 'Số hóa đơn',
            width: 120,
            renderCell: (params: any) => {
              return params.row.so_ct0 || '';
            }
          },
          {
            field: 'tk',
            headerName: 'Tài khoản',
            width: 100,
            renderCell: (params: any) => {
              return params.row.tk || '';
            }
          },
          {
            field: 'tk_du',
            headerName: 'Tk đối ứng',
            width: 120,
            renderCell: (params: any) => {
              return params.row.tk_du || '';
            }
          },
          {
            field: 'dien_giai',
            headerName: 'Diễn giải',
            width: 200,
            renderCell: (params: any) => {
              return params.row.dien_giai || '';
            }
          },
          {
            field: 'ps_no',
            headerName: 'Ps nợ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ps_no ? params.row.ps_no.toLocaleString('vi-VN') : '0';
            }
          },
          {
            field: 'ps_co',
            headerName: 'Ps có',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ps_co ? params.row.ps_co.toLocaleString('vi-VN') : '0';
            }
          },
          {
            field: 'ma_ct',
            headerName: 'Mã c/từ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ma_ct || '';
            }
          }
        ],
        rows: data.map(item => ({
          id: item.id,
          ma_unit: item.ma_unit,
          ngay_ct: item.ngay_ct,
          so_ct: item.so_ct,
          ngay_ct0: item.ngay_ct0,
          so_ct0: item.so_ct0,
          tk: item.tk,
          tk_du: item.tk_du,
          dien_giai: item.dien_giai,
          ps_no: item.ps_no,
          ps_co: item.ps_co,
          ma_ct: item.ma_ct
        }))
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,

    handleRowClick
  };
}
