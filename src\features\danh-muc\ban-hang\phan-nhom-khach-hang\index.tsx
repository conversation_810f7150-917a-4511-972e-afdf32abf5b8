'use client';

import Split from 'react-split';
import { customerGroupColumns } from '@/features/danh-muc/ban-hang/phan-nhom-khach-hang/cols-definition';
import { AritoSplitPane } from '@/components/custom/arito/split-pane';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, ConfirmDialog, FormDialog } from './components';
import SidebarButton from '@/components/arito/sidebar-button';
import AritoIcon from '@/components/custom/arito/icon';
import { useData, useFormState } from './hooks';
import { GroupType } from '@/types/schemas';
import { useGroup } from '@/hooks/queries';

export const groupTypes = [
  {
    label: 'Nhóm khách hàng 1 (KH1)',
    value: GroupType.CUSTOMER1,
    shortCode: 'KH1'
  },
  {
    label: 'Nhóm khách hàng 2 (KH2)',
    value: GroupType.CUSTOMER2,
    shortCode: 'KH2'
  },
  {
    label: 'Nhóm khách hàng 3 (KH3)',
    value: GroupType.CUSTOMER3,
    shortCode: 'KH3'
  }
];

const DEFAULT_GROUP = groupTypes[0].value;

export default function CustomerGroupPage() {
  const {
    groups: groupsKH1,
    isLoading: isLoadingKH1,
    refreshGroups: refreshGroupsKH1,
    fetchGroupsByType: fetchGroupsByTypeKH1,
    addGroup: addGroupKH1,
    updateGroup: updateGroupKH1,
    deleteGroup: deleteGroupKH1
  } = useGroup(GroupType.CUSTOMER1);

  const {
    groups: groupsKH2,
    isLoading: isLoadingKH2,
    refreshGroups: refreshGroupsKH2,
    fetchGroupsByType: fetchGroupsByTypeKH2,
    addGroup: addGroupKH2,
    updateGroup: updateGroupKH2,
    deleteGroup: deleteGroupKH2
  } = useGroup(GroupType.CUSTOMER2);

  const {
    groups: groupsKH3,
    isLoading: isLoadingKH3,
    refreshGroups: refreshGroupsKH3,
    fetchGroupsByType: fetchGroupsByTypeKH3,
    addGroup: addGroupKH3,
    updateGroup: updateGroupKH3,
    deleteGroup: deleteGroupKH3
  } = useGroup(GroupType.CUSTOMER3);

  const getGroupFunctions = (groupType: GroupType) => {
    switch (groupType) {
      case GroupType.CUSTOMER1:
        return {
          groups: groupsKH1,
          refreshGroups: refreshGroupsKH1,
          fetchGroupsByType: fetchGroupsByTypeKH1,
          addGroup: addGroupKH1,
          updateGroup: updateGroupKH1,
          deleteGroup: deleteGroupKH1,
          isLoading: isLoadingKH1
        };
      case GroupType.CUSTOMER2:
        return {
          groups: groupsKH2,
          refreshGroups: refreshGroupsKH2,
          fetchGroupsByType: fetchGroupsByTypeKH2,
          addGroup: addGroupKH2,
          updateGroup: updateGroupKH2,
          deleteGroup: deleteGroupKH2,
          isLoading: isLoadingKH2
        };
      case GroupType.CUSTOMER3:
        return {
          groups: groupsKH3,
          refreshGroups: refreshGroupsKH3,
          fetchGroupsByType: fetchGroupsByTypeKH3,
          addGroup: addGroupKH3,
          updateGroup: updateGroupKH3,
          deleteGroup: deleteGroupKH3,
          isLoading: isLoadingKH3
        };
      default:
        return {
          groups: groupsKH1,
          refreshGroups: refreshGroupsKH1,
          fetchGroupsByType: fetchGroupsByTypeKH1,
          addGroup: addGroupKH1,
          updateGroup: updateGroupKH1,
          deleteGroup: deleteGroupKH1,
          isLoading: isLoadingKH1
        };
    }
  };

  const { activeGroup, handleFilter } = useData(DEFAULT_GROUP);

  const { isLoading, addGroup, updateGroup, deleteGroup, groups, refreshGroups } = getGroupFunctions(activeGroup);

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState(activeGroup);

  const tables = [
    {
      name: '',
      rows: groups,
      columns: customerGroupColumns,
      key: `${activeGroup}-${Date.now()}`
    }
  ];

  const handleFormSubmit = async (data: any) => {
    try {
      const formData = {
        ...data,
        loai_nhom: activeGroup
      };

      if (formMode === 'add') {
        await addGroup(formData);
        await refreshGroups();
      } else if (formMode === 'edit' && selectedObj) {
        await updateGroup(selectedObj.uuid, formData);
        await refreshGroups();
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className='flex h-screen w-screen flex-row overflow-hidden bg-white'>
      <AritoSplitPane split='vertical' minSize={200} defaultSize={300}>
        <div className='h-full overflow-y-auto border-r'>
          <div className='p-4'>
            <h2 className='mb-4 flex items-center gap-1 text-lg font-semibold'>
              <AritoIcon icon={539} /> Danh mục phân nhóm
            </h2>
            <hr className='mb-4' />
            <ul>
              {groupTypes.map(group => (
                <li key={group.value} className='mb-2 text-sm'>
                  <SidebarButton isActive={activeGroup === group.value} onClick={() => handleFilter(group.value)}>
                    {group.label}
                  </SidebarButton>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className='h-full overflow-y-auto'>
          <div className='flex h-full flex-col'>
            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onViewClick={() => selectedObj && handleViewClick()}
              isViewDisabled={!selectedObj}
              activeGroup={activeGroup}
              onRefresh={() => refreshGroups()}
            />

            <div className='flex-1 overflow-hidden'>
              <Split
                className='flex flex-1 flex-col overflow-hidden'
                direction='vertical'
                sizes={[50, 50]}
                minSize={200}
                gutterSize={8}
                gutterAlign='center'
                snapOffset={30}
                dragInterval={1}
                cursor='row-resize'
              >
                <div className='w-full overflow-hidden'>
                  {isLoading && (
                    <div className='flex h-64 items-center justify-center'>
                      <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500' />
                    </div>
                  )}

                  {!isLoading && (
                    <AritoDataTables
                      key={activeGroup}
                      tables={tables}
                      onRowClick={handleRowClick}
                      selectedRowId={selectedRowIndex || undefined}
                      getRowId={row => row.uuid}
                    />
                  )}
                </div>
              </Split>
            </div>
          </div>
        </div>
      </AritoSplitPane>

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          onAddButtonClick={() => {
            handleCloseForm();

            clearSelection();

            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              const objToEdit = { ...selectedObj };

              handleCloseForm();

              clearSelection();

              setTimeout(() => {
                handleRowClick({ id: objToEdit.uuid, row: objToEdit });

                handleEditClick();
              }, 100);
            }
          }}
          onDeleteButtonClick={() => {
            if (selectedObj) {
              const objToDelete = { ...selectedObj };

              handleCloseForm();

              clearSelection();

              setTimeout(() => {
                handleRowClick({ id: objToDelete.uuid, row: objToDelete });

                handleDeleteClick();
              }, 100);
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              const objToCopy = { ...selectedObj };

              handleCloseForm();

              clearSelection();

              setTimeout(() => {
                handleRowClick({ id: objToCopy.uuid, row: objToCopy });

                handleCopyClick();
              }, 100);
            }
          }}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          activeGroup={activeGroup}
          groups={groups}
          isCopyMode={isCopyMode}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          selectedObj={selectedObj}
          onClose={handleCloseDelete}
          deleteKhuVuc={deleteGroup}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
