import { useState, useCallback } from 'react';
import {
  TheTaiSanCoDinhItem,
  TheTaiSanCoDinhResponse,
  TheTaiSanCoDinhSearchFormValues,
  UseTheTaiSanCoDinhReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): TheTaiSanCoDinhItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_ts: 'TS001',
      ten_ts: '<PERSON><PERSON>y tính xách tay Dell Inspiron 15',
      ngay_tang: '2024-01-15',
      ngay_kh: '2024-02-01',
      so_ky_kh: 36,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP001',
      so_ct: 'CT2024001'
    },
    {
      id: '2',
      stt: 2,
      ma_ts: 'TS002',
      ten_ts: '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> 24 inch',
      ngay_tang: '2024-01-16',
      ngay_kh: '2024-02-01',
      so_ky_kh: 24,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP002',
      so_ct: 'CT2024002'
    },
    {
      id: '3',
      stt: 3,
      ma_ts: 'TS003',
      ten_ts: 'Bàn phím cơ Logitech MX Keys',
      ngay_tang: '2024-01-17',
      ngay_kh: '2024-02-01',
      so_ky_kh: 18,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP001',
      so_ct: 'CT2024003'
    },
    {
      id: '4',
      stt: 4,
      ma_ts: 'TS004',
      ten_ts: 'Chuột không dây Microsoft',
      ngay_tang: '2024-01-18',
      ngay_kh: '2024-02-01',
      so_ky_kh: 12,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP003',
      so_ct: 'CT2024004'
    },
    {
      id: '5',
      stt: 5,
      ma_ts: 'TS005',
      ten_ts: 'Tai nghe Sony WH-1000XM4',
      ngay_tang: '2024-01-19',
      ngay_kh: '2024-02-01',
      so_ky_kh: 30,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP002',
      so_ct: 'CT2024005'
    },
    {
      id: '6',
      stt: 6,
      ma_ts: 'TS006',
      ten_ts: 'Webcam Logitech C920',
      ngay_tang: '2024-01-20',
      ngay_kh: '2024-02-01',
      so_ky_kh: 24,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP001',
      so_ct: 'CT2024006'
    },
    {
      id: '7',
      stt: 7,
      ma_ts: 'TS007',
      ten_ts: 'Ổ cứng SSD Samsung 1TB',
      ngay_tang: '2024-01-21',
      ngay_kh: '2024-02-01',
      so_ky_kh: 48,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP004',
      so_ct: 'CT2024007'
    },
    {
      id: '8',
      stt: 8,
      ma_ts: 'TS008',
      ten_ts: 'RAM DDR4 16GB Corsair',
      ngay_tang: '2024-01-22',
      ngay_kh: '2024-02-01',
      so_ky_kh: 36,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP004',
      so_ct: 'CT2024008'
    },
    {
      id: '9',
      stt: 9,
      ma_ts: 'TS009',
      ten_ts: 'Card đồ họa NVIDIA RTX 4060',
      ngay_tang: '2024-01-23',
      ngay_kh: '2024-02-01',
      so_ky_kh: 60,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP005',
      so_ct: 'CT2024009'
    },
    {
      id: '10',
      stt: 10,
      ma_ts: 'TS010',
      ten_ts: 'Mainboard ASUS ROG Strix',
      ngay_tang: '2024-01-24',
      ngay_kh: '2024-02-01',
      so_ky_kh: 42,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP001',
      so_ct: 'CT2024010'
    }
  ];
};

/**
 * Custom hook for managing TheTaiSanCoDinh (Fixed Asset Card) data
 *
 * This hook provides functionality to fetch fixed asset card data
 * with mock support for testing and development purposes.
 */
export function useTheTaiSanCoDinhData(searchParams: TheTaiSanCoDinhSearchFormValues): UseTheTaiSanCoDinhReturn {
  const [data, setData] = useState<TheTaiSanCoDinhItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: TheTaiSanCoDinhSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<TheTaiSanCoDinhResponse>('/tai-san/kiem-ke-tscd/the-tscd/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
