import { useState } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(): UseTableDataReturn {
  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: [
        { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
        { field: 'ten_vt', headerName: 'Tên vật tư', width: 200 },
        { field: 'dvt', headerName: 'Đvt', width: 80 },
        { field: 'tien_ban', headerName: 'Doanh thu', width: 150, type: 'number' },
        { field: 'tien_tl', headerName: 'Trả lại', width: 150, type: 'number' },
        { field: 'tien_cl', headerName: 'Dt còn lại', width: 150, type: 'number' }
      ],
      rows: []
    }
  ]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,

    handleRowClick
  };
}
