import { GridColDef } from '@mui/x-data-grid';

export const exportMainColumns = (): GridColDef[] => [
  { field: 'don_vi', headerName: 'Đơn vị', width: 120 },
  { field: 'ngay_ctu', headerName: '<PERSON><PERSON><PERSON> c.từ', width: 120 },
  { field: 'so_ct', headerName: 'Số c.từ', width: 120 },
  { field: 'ma_kh', headerName: 'Mã <PERSON>h<PERSON>ch hàng', width: 120 },
  {
    field: 'ten_kh',
    headerName: 'Tên Khách hàng',
    width: 200,
    flex: 1,
    cellClassName: params => {
      return params.row?.isSummary ? 'font-bold' : '';
    }
  },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 120 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 180 },
  { field: 'dvt', headerName: 'ĐVT', width: 80 },
  {
    field: 'so_luong',
    headerName: 'S.Lượng',
    width: 120,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  },
  {
    field: 'don_gia',
    headerName: 'Đơn giá',
    width: 150,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  },
  {
    field: 'thanh_tien',
    headerName: 'Thành tiền',
    width: 150,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  },
  {
    field: 'thue',
    headerName: 'Thuế',
    width: 120,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  },
  { field: 'tk_no', headerName: 'Tài khoản nợ', width: 120 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 120 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 120 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 120 },
  {
    field: 'phi',
    headerName: 'Phí',
    width: 120,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  },
  { field: 'sanh_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'c/p_khong_hop_le', headerName: 'C/P không hợp lệ', width: 120 },
  { field: 'ma_ctu', headerName: 'Mã c/từ', width: 120 }
];

export const exportPrintColColums: GridColDef[] = [
  { field: 'col', headerName: 'Cột', width: 150 },
  { field: 'colName', headerName: 'Tên cột', width: 150 },
  { field: 'colNameEng', headerName: 'Tên tiếng anh', width: 150 },
  { field: 'colWidth', headerName: 'Độ rộng', width: 150 },
  { field: 'colType', headerName: 'Định dạng', width: 150 },
  { field: 'colAlign', headerName: 'Căn chỉnh', width: 150 },
  { field: 'colBold', headerName: 'In đậm', width: 150 },
  { field: 'colItalic', headerName: 'In nghiêng', width: 150 },
  { field: 'colSum', headerName: 'Tổng', width: 150 }
];

export const exportDotThanhToanColumns: GridColDef[] = [
  { field: 'ma_dot', headerName: 'Mã đợt', width: 150 },
  { field: 'ten_dot_thanh_toan', headerName: 'Tên đợt thanh toán', width: 150 },
  { field: 'ngay_thanh_toan', headerName: 'Ngày thanh toán', width: 150 },
  { field: 'ty_le_%', headerName: 'Tỷ lệ (%)', width: 150 },
  { field: 'tien', headerName: 'Tiền', width: 150 }
];

export const exportSanPhamColColums: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 150 }
];

export const exportLenhSanXuatColColums: GridColDef[] = [
  { field: 'so_lenh', headerName: 'Số lệnh', width: 150 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 }
];
