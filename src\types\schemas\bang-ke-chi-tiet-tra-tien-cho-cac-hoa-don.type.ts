// TypeScript interfaces for BangKeChiTietTraTienChoHoaDon (Detailed Payment Report for Invoices)

export interface BangKeChiTietTraTienChoHoaDonItem {
  stt: number;
  id: string;
  id_tt: string;
  id_ct: string;
  loai_tt: string;
  unit_id: string;
  ma_ct: string;
  ngay_ct: string;
  so_ct: string;
  ngay_ct0: string;
  so_ct0: string;
  ma_kh: string;
  ten_kh: string;
  dien_giai: string;
  t_tt: number;
  tt: number;
  cl: number;
  tt_yn: string;
  ngay_tt: string;
  ma_tt: string;
  han_tt: string;
  ma_nt: string;
  ty_gia: number;
  t_tt_nt0: number;
  t_tt0: number;
  ma_unit: string;
}

export interface BangKeChiTietTraTienChoHoaDonResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangKeChiTietTraTienChoHoaDonItem[];
}

export interface BangKeChiTietTraTienChoHoaDonSearchFormValues {
  ngay_ct1?: Date;
  ngay_ct2?: Date;
  ngay_tt?: Date;
  tk?: string;
  ma_kh?: string;
  nh_kh1?: string;
  nh_kh2?: string;
  nh_kh3?: string;
  rg_code?: string;
  tt_yn?: string;
  ct_thu?: string;
  mau_bc?: string;
  so_ct1?: string;
  so_ct2?: string;
  report_filtering?: string;
  data_analysis_struct?: string;
  [key: string]: any;
}

export interface UseBangKeChiTietTraTienChoHoaDonReturn {
  data: BangKeChiTietTraTienChoHoaDonItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangKeChiTietTraTienChoHoaDonSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
