import { Box, Dialog, DialogContent, Divider, IconButton, Typography, useMediaQuery, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import React from 'react';
import AritoIcon from '@/components/custom/arito/icon';

interface AritoNotifyModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  confirmText?: string;
}

const AritoNotifyModal: React.FC<AritoNotifyModalProps> = ({
  open,
  onClose,
  onConfirm,
  title = 'Cảnh báo',
  message,
  confirmText = 'Đồng ý'
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='xs'
      fullWidth={isMobile}
      PaperProps={{
        style: {
          overflow: 'hidden',
          borderRadius: 0,
          minWidth: isMobile ? '100%' : '600px'
        }
      }}
    >
      <Box
        sx={{
          backgroundColor: '#eef7fb',
          padding: '4px 8px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #e0e0e0'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <AritoIcon icon={260} marginX='4px' />
          <Typography color='#444' variant='body2'>
            {title}
          </Typography>
        </Box>
        <IconButton sx={{ color: 'black' }} size='small' onClick={onClose}>
          <CloseIcon fontSize='small' />
        </IconButton>
      </Box>

      <DialogContent sx={{ padding: '0px' }}>
        <Typography fontSize='12px' variant='body2' sx={{ marginBottom: '8px', padding: '10px 20px' }}>
          {message}
        </Typography>

        <Divider />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '8px',
            padding: '6px'
          }}
        >
          <button
            onClick={onConfirm}
            style={{
              backgroundColor: '#53a3a3',
              color: 'white',
              border: 'none',
              padding: '6px 6px',
              paddingRight: '12px',
              cursor: 'default',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px',
              fontSize: '14px',
              width: '100px',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={e => (e.currentTarget.style.backgroundColor = '#438585')}
            onMouseOut={e => (e.currentTarget.style.backgroundColor = '#53a3a3')}
          >
            <AritoIcon icon={884} marginX='4px' />
            {confirmText}
          </button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AritoNotifyModal;
