import { useState, useCallback, useEffect } from 'react';
import { RevenueExpenseItem, RevenueExpenseResponse, SearchFormValues, UseRevenueExpenseReturn } from '../types';
import api from '@/lib/api';

/**
 * Generate mock data for revenue expense report
 * @param numberOfPeriods - Number of periods to generate data for
 * @returns Array of revenue expense items with dynamic period fields
 */
const generateMockData = (numberOfPeriods: number = 4): RevenueExpenseItem[] => {
  const baseData = [
    {
      id: 1,
      indicator: 'Tổng doanh thu',
      baseAmount: 15000000000,
      basePercent: 100
    },
    {
      id: 2,
      indicator: '<PERSON>anh thu bán hàng và cung cấp dịch vụ',
      baseAmount: 12000000000,
      basePercent: 80
    },
    {
      id: 3,
      indicator: '<PERSON><PERSON>h thu hoạt động tài chính',
      baseAmount: 2000000000,
      basePercent: 13.3
    },
    {
      id: 4,
      indicator: '<PERSON>hu nhập khác',
      baseAmount: 1000000000,
      basePercent: 6.7
    },
    {
      id: 5,
      indicator: 'Tổng chi phí',
      baseAmount: 12000000000,
      basePercent: 80,
      highlight: true
    },
    {
      id: 6,
      indicator: 'Chi phí giá vốn',
      baseAmount: 7000000000,
      basePercent: 46.7
    },
    {
      id: 7,
      indicator: 'Chi phí bán hàng',
      baseAmount: 2000000000,
      basePercent: 13.3
    },
    {
      id: 8,
      indicator: 'Chi phí quản lý doanh nghiệp',
      baseAmount: 1500000000,
      basePercent: 10
    },
    {
      id: 9,
      indicator: 'Chi phí tài chính',
      baseAmount: 1000000000,
      basePercent: 6.7
    },
    {
      id: 10,
      indicator: 'Chi phí khác',
      baseAmount: 500000000,
      basePercent: 3.3
    },
    {
      id: 11,
      indicator: 'Lợi nhuận',
      baseAmount: 3000000000,
      basePercent: 20
    }
  ];

  return baseData.map(item => {
    const result: any = {
      id: item.id,
      indicator: item.indicator,
      highlight: item.highlight
    };

    let totalAmount = 0;
    let totalPercent = 0;

    // Generate dynamic period data
    for (let i = 1; i <= numberOfPeriods; i++) {
      const amountVariation = item.baseAmount + (Math.random() * 0.2 - 0.1) * item.baseAmount; // ±10% variation
      const percentVariation = item.basePercent + (Math.random() * 0.2 - 0.1) * item.basePercent; // ±10% variation

      result[`period_${i}_amount`] = Math.round(amountVariation);
      result[`period_${i}_percent`] = Math.round(percentVariation * 10) / 10; // Round to 1 decimal place

      totalAmount += result[`period_${i}_amount`];
      totalPercent += result[`period_${i}_percent`];
    }

    result.total_amount = totalAmount;
    result.total_percent = Math.round((totalPercent / numberOfPeriods) * 10) / 10; // Average percent

    return result;
  });
};

/**
 * Custom hook for managing Revenue Expense Report data
 *
 * This hook provides functionality to fetch revenue expense report data
 * with mock support for testing and development purposes.
 */
export function useRevenueExpenseData(searchParams: SearchFormValues): UseRevenueExpenseReturn {
  const [data, setData] = useState<RevenueExpenseItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Ensure period is a number
      const period = Number(searchParams.period) || 4;

      // Use mock data directly for now
      const mockData = generateMockData(period);

      const response = await api.get<RevenueExpenseResponse>(
        '/tai-chinh/phan-tich-bao-cao-tai-chinh/bao-cao-doanh-thu-chi-phi/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
