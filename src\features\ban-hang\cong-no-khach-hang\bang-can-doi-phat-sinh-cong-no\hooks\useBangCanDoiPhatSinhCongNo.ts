import { useState, useCallback } from 'react';
import type {
  BangCanDoiPhatSinhCongNoItem,
  BangCanDoiPhatSinhCongNoResponse,
  BangCanDoiPhatSinhCongNoSearchFormValues,
  UseBangCanDoiPhatSinhCongNoReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BangCanDoiPhatSinhCongNoItem[] => {
  return [
    {
      uuid: '1',
      tai_khoan: '1121',
      ma_khach_hang: 'KH001',
      ten_khach_hang: 'Công ty TNHH ABC',
      du_no_dau: 10000000,
      du_co_dau: 0,
      ps_no: 5000000,
      ps_co: 2000000,
      du_no_cuoi: 13000000,
      du_co_cuoi: 0
    },
    {
      uuid: '2',
      tai_khoan: '131',
      ma_khach_hang: 'KH002',
      ten_khach_hang: '<PERSON><PERSON><PERSON> hàng <PERSON>',
      du_no_dau: 0,
      du_co_dau: 3000000,
      ps_no: 1000000,
      ps_co: 4000000,
      du_no_cuoi: 0,
      du_co_cuoi: 6000000
    },
    {
      uuid: '3',
      tai_khoan: '1121',
      ma_khach_hang: 'KH003',
      ten_khach_hang: 'Ông Nguyễn Văn A',
      du_no_dau: 500000,
      du_co_dau: 0,
      ps_no: 100000,
      ps_co: 0,
      du_no_cuoi: 600000,
      du_co_cuoi: 0
    },
    {
      uuid: '4',
      tai_khoan: '331',
      ma_khach_hang: 'KH004',
      ten_khach_hang: 'Bà Trần Thị B',
      du_no_dau: 0,
      du_co_dau: 1500000,
      ps_no: 500000,
      ps_co: 200000,
      du_no_cuoi: 0,
      du_co_cuoi: 1200000
    },
    {
      uuid: '5',
      tai_khoan: '131',
      ma_khach_hang: 'KH005',
      ten_khach_hang: 'Công ty Cổ phần XYZ',
      du_no_dau: 7000000,
      du_co_dau: 0,
      ps_no: 3000000,
      ps_co: 1000000,
      du_no_cuoi: 9000000,
      du_co_cuoi: 0
    },
    {
      uuid: '6',
      tai_khoan: '1121',
      ma_khach_hang: 'KH006',
      ten_khach_hang: 'Nhà hàng Hải Sản',
      du_no_dau: 0,
      du_co_dau: 2000000,
      ps_no: 0,
      ps_co: 500000,
      du_no_cuoi: 0,
      du_co_cuoi: 2500000
    },
    {
      uuid: '7',
      tai_khoan: '331',
      ma_khach_hang: 'KH007',
      ten_khach_hang: 'Doanh nghiệp Tư nhân D',
      du_no_dau: 0,
      du_co_dau: 800000,
      ps_no: 100000,
      ps_co: 0,
      du_no_cuoi: 0,
      du_co_cuoi: 700000
    },
    {
      uuid: '8',
      tai_khoan: '131',
      ma_khach_hang: 'KH008',
      ten_khach_hang: 'Tạp hóa Minh Tâm',
      du_no_dau: 2000000,
      du_co_dau: 0,
      ps_no: 500000,
      ps_co: 200000,
      du_no_cuoi: 2300000,
      du_co_cuoi: 0
    }
  ];
};
/**
 * Custom hook for managing BangCanDoiPhatSinhCongNo (Customer Debt Balance Report) data
 *
 * This hook provuuides functionality to fetch customer debt balance report data
 * with mock support for testing and development purposes.
 */
export function useBangCanDoiPhatSinhCongNo(
  searchParams: BangCanDoiPhatSinhCongNoSearchFormValues
): UseBangCanDoiPhatSinhCongNoReturn {
  const [data, setData] = useState<BangCanDoiPhatSinhCongNoItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangCanDoiPhatSinhCongNoSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangCanDoiPhatSinhCongNoResponse>(
        '/ban-hang/cong-no-khach-hang/bang-can-doi-phat-sinh-cong-no/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
