import { useState, useCallback } from 'react';
import {
  SoChiTietCongNoTheoKhachHangItem,
  SoChiTietCongNoTheoKhachHangResponse,
  SoChiTietCongNoTheoKhachHangSearchFormValues,
  UseSoChiTietCongNoTheoKhachHangReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): SoChiTietCongNoTheoKhachHangItem[] => {
  return [
    {
      id: '1',
      ma_unit: 'CN',
      ngay_ct: '2024-01-15',
      so_ct: 'BH001',
      ngay_ct0: '2024-01-15',
      so_ct0: 'HD001',
      tk: '131',
      tk_du: '511',
      dien_giai: 'B<PERSON> hàng cho Công ty ABC Technology',
      ps_no: 50000000,
      ps_co: 0,
      ma_ct: 'BH',
      xorder: 1,
      line: 1,
      unit_id: 'CN001',
      ma_bp: 'BP001',
      ma_vv: 'VV001'
    },
    {
      id: '2',
      ma_unit: 'CN',
      ngay_ct: '2024-01-16',
      so_ct: 'TT001',
      ngay_ct0: '2024-01-16',
      so_ct0: 'PT001',
      tk: '131',
      tk_du: '111',
      dien_giai: 'Thu tiền từ Công ty ABC Technology',
      ps_no: 0,
      ps_co: 30000000,
      ma_ct: 'TT',
      xorder: 2,
      line: 1,
      unit_id: 'CN001',
      ma_bp: 'BP001',
      ma_vv: 'VV001'
    },
    {
      id: '3',
      ma_unit: 'BN',
      ngay_ct: '2024-01-17',
      so_ct: 'BH002',
      ngay_ct0: '2024-01-17',
      so_ct0: 'HD002',
      tk: '131',
      tk_du: '511',
      dien_giai: 'Bán thiết bị văn phòng cho XYZ Trading',
      ps_no: 25000000,
      ps_co: 0,
      ma_ct: 'BH',
      xorder: 3,
      line: 1,
      unit_id: 'BN001',
      ma_bp: 'BP002',
      ma_vv: 'VV002'
    },
    {
      id: '4',
      ma_unit: 'BN',
      ngay_ct: '2024-01-18',
      so_ct: 'TT002',
      ngay_ct0: '2024-01-18',
      so_ct0: 'PT002',
      tk: '131',
      tk_du: '111',
      dien_giai: 'Thu tiền từ XYZ Trading',
      ps_no: 0,
      ps_co: 25000000,
      ma_ct: 'TT',
      xorder: 4,
      line: 1,
      unit_id: 'BN001',
      ma_bp: 'BP002',
      ma_vv: 'VV002'
    },
    {
      id: '5',
      ma_unit: 'DN',
      ngay_ct: '2024-01-19',
      so_ct: 'BH003',
      ngay_ct0: '2024-01-19',
      so_ct0: 'HD003',
      tk: '131',
      tk_du: '511',
      dien_giai: 'Bán hàng điện tử cho 123 Distribution',
      ps_no: 75000000,
      ps_co: 0,
      ma_ct: 'BH',
      xorder: 5,
      line: 1,
      unit_id: 'DN001',
      ma_bp: 'BP003',
      ma_vv: 'VV003'
    },
    {
      id: '6',
      ma_unit: 'DN',
      ngay_ct: '2024-01-20',
      so_ct: 'TT003',
      ngay_ct0: '2024-01-20',
      so_ct0: 'PT003',
      tk: '131',
      tk_du: '111',
      dien_giai: 'Thu tiền từ 123 Distribution',
      ps_no: 0,
      ps_co: 45000000,
      ma_ct: 'TT',
      xorder: 6,
      line: 1,
      unit_id: 'DN001',
      ma_bp: 'BP003',
      ma_vv: 'VV003'
    },
    {
      id: '7',
      ma_unit: 'CN',
      ngay_ct: '2024-01-21',
      so_ct: 'BH004',
      ngay_ct0: '2024-01-21',
      so_ct0: 'HD004',
      tk: '131',
      tk_du: '511',
      dien_giai: 'Bán dịch vụ tư vấn IT cho Nguyễn Văn A',
      ps_no: 15000000,
      ps_co: 0,
      ma_ct: 'BH',
      xorder: 7,
      line: 1,
      unit_id: 'CN001',
      ma_bp: 'BP001',
      ma_vv: 'VV001'
    },
    {
      id: '8',
      ma_unit: 'CN',
      ngay_ct: '2024-01-21',
      so_ct: 'TT004',
      ngay_ct0: '2024-01-21',
      so_ct0: 'PT004',
      tk: '131',
      tk_du: '111',
      dien_giai: 'Thu tiền từ Nguyễn Văn A',
      ps_no: 0,
      ps_co: 15000000,
      ma_ct: 'TT',
      xorder: 8,
      line: 1,
      unit_id: 'CN001',
      ma_bp: 'BP001',
      ma_vv: 'VV001'
    },
    {
      id: '9',
      ma_unit: 'HN',
      ngay_ct: '2024-01-22',
      so_ct: 'BH005',
      ngay_ct0: '2024-01-22',
      so_ct0: 'HD005',
      tk: '131',
      tk_du: '511',
      dien_giai: 'Bán phần mềm kế toán cho Công ty DEF',
      ps_no: 40000000,
      ps_co: 0,
      ma_ct: 'BH',
      xorder: 9,
      line: 1,
      unit_id: 'HN001',
      ma_bp: 'BP004',
      ma_vv: 'VV004'
    },
    {
      id: '10',
      ma_unit: 'HN',
      ngay_ct: '2024-01-25',
      so_ct: 'TT005',
      ngay_ct0: '2024-01-25',
      so_ct0: 'PT005',
      tk: '131',
      tk_du: '111',
      dien_giai: 'Diễn giải: thu tiền từ Công ty DEF',
      ps_no: 0,
      ps_co: 20000000,
      ma_ct: 'TT',
      xorder: 10,
      line: 1,
      unit_id: 'HN001',
      ma_bp: 'BP004',
      ma_vv: 'VV004'
    },
    // Summary rows
    {
      id: 'summary-1',
      ma_unit: '',
      ngay_ct: '',
      so_ct: '',
      ngay_ct0: '',
      so_ct0: '',
      tk: '',
      tk_du: '',
      dien_giai: 'Đầu kỳ',
      ps_no: 50000000, // Opening balance debit (row 1)
      ps_co: 0, // Opening balance credit
      ma_ct: '',
      xorder: 11,
      line: 1,
      unit_id: '',
      ma_bp: '',
      ma_vv: ''
    },
    {
      id: 'summary-2',
      ma_unit: '',
      ngay_ct: '',
      so_ct: '',
      ngay_ct0: '',
      so_ct0: '',
      tk: '',
      tk_du: '',
      dien_giai: 'Tổng phát sinh',
      ps_no: 120000000, // Total debit transactions (30M + 25M + 75M + 15M = 145M, excluding opening balance)
      ps_co: 110000000, // Total credit transactions (25M + 25M + 45M + 15M + 20M = 130M)
      ma_ct: '',
      xorder: 12,
      line: 1,
      unit_id: '',
      ma_bp: '',
      ma_vv: ''
    },
    {
      id: 'summary-3',
      ma_unit: '',
      ngay_ct: '',
      so_ct: '',
      ngay_ct0: '',
      so_ct0: '',
      tk: '',
      tk_du: '',
      dien_giai: 'Cuối kỳ',
      ps_no: 60000000, // Closing balance: Opening (50M) + Total Debit (120M) - Total Credit (110M) = 60M
      ps_co: 0, // Closing balance credit
      ma_ct: '',
      xorder: 13,
      line: 1,
      unit_id: '',
      ma_bp: '',
      ma_vv: ''
    }
  ];
};

/**
 * Custom hook for managing SoChiTietCongNoTheoKhachHang (Customer Debt Detail Report) data
 *
 * This hook provides functionality to fetch customer debt detail data
 * with mock support for testing and development purposes.
 */
export function useSoChiTietCongNoTheoKhachHang(
  searchParams: SoChiTietCongNoTheoKhachHangSearchFormValues
): UseSoChiTietCongNoTheoKhachHangReturn {
  const [data, setData] = useState<SoChiTietCongNoTheoKhachHangItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SoChiTietCongNoTheoKhachHangSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<SoChiTietCongNoTheoKhachHangResponse>(
        '/ban-hang/so-chi-tiet-cong-no-theo-khach-hang/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
