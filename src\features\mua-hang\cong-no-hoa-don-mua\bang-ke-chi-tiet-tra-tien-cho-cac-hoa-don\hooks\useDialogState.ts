import { useState, useCallback } from 'react';

export interface UseDialogStateReturn {
  isOpen: boolean;
  openDialog: () => void;
  closeDialog: () => void;
  toggleDialog: () => void;
}

/**
 * Custom hook for managing dialog state
 * Provides simple open/close functionality for dialogs
 */
export function useDialogState(initialState: boolean = false): UseDialogStateReturn {
  const [isOpen, setIsOpen] = useState<boolean>(initialState);

  const openDialog = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    setIsOpen(false);
  }, []);

  const toggleDialog = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  return {
    isOpen,
    openDialog,
    closeDialog,
    toggleDialog
  };
}
