import { useState } from 'react';

export interface SearchParams {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showTable: boolean;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchParams, fetchData?: (params: SearchParams) => Promise<void>) => Promise<void>;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  setShowTable: (show: boolean) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = async (values: SearchParams, fetchData?: (params: SearchParams) => Promise<void>) => {
    if (fetchData) {
      try {
        await fetchData(values);
      } catch (error) {
        console.error('❌ Error fetching data:', error);
      }
    }

    // Show the table and close the dialog
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    // This handler is kept for future implementation
    console.log('Edit print template clicked');
  };

  return {
    initialSearchDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    setShowTable
  };
}
