// TypeScript interfaces for BangKeCongNoTheoHoaDonMua (Purchase Invoice Debt Report)

export interface BangKeCongNoTheoHoaDonItem {
  stt: number;
  id: string;
  id_tt: string;
  id_ct: string;
  loai_tt: string;
  unit_id: string;
  ma_ct: string;
  ngay_ct: string;
  so_ct: string;
  ma_tt: string;
  han_tt: string;
  ma_nt: string;
  ty_gia: number;
  ma_kh: string;
  dien_giai: string;
  t_tt_nt0: number;
  t_tt0: number;
  t_tt: number;
  tt: number;
  cl: number;
  tt_yn: string;
  ngay_tt: string;
  trong_han: number;
  qua_han01: number;
  qua_han02: number;
  qua_han03: number;
  qua_han04: number;
  qua_han05: number;
  so_ct0: string;
  ngay_ct0: string;
  ngay_dh0: string;
  so_ngay: number;
  ten_kh: string;
  ma_unit: string;
}

export interface BangKeCongNoTheoHoaDonMuaResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangKeCongNoTheoHoaDonItem[];
}

export interface BangKeCongNoTheoHoaDonSearchFormValues {
  ngay_bc?: Date;
  ngay_tt?: Date;
  ngay_ct1?: Date;
  ngay_ct2?: Date;
  tk?: string;
  ma_kh?: string;
  nh_kh1?: string;
  nh_kh2?: string;
  nh_kh3?: string;
  rg_code?: string;
  ct_theo?: string;
  tt_yn?: string;
  so_ngay_tt?: number;
  so_ngay_cb?: number;
  mau_bc?: string;
  so_ct1?: string;
  so_ct2?: string;
  report_filtering?: string;
  data_analysis_struct?: string;
  [key: string]: any;
}

export interface UseBangKeCongNoTheoHoaDonReturn {
  data: BangKeCongNoTheoHoaDonItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangKeCongNoTheoHoaDonSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
