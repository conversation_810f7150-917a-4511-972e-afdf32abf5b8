import { useState, useEffect, useRef } from 'react';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

interface UseColumnResizeProps {
  searchColumns?: ExtendedGridColDef[];
}

interface UseColumnResizeReturn {
  columnWidths: { [key: string]: number };
  isResizing: boolean;
  cursorStyle: string;
  userSelectStyle: string;
  tableRef: React.RefObject<HTMLDivElement>;
  startResize: (e: React.MouseEvent, columnField: string) => void;
}

export const useColumnResize = ({ searchColumns }: { searchColumns?: ExtendedGridColDef[] }): UseColumnResizeReturn => {
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: number }>({});
  const [isResizing, setIsResizing] = useState(false);
  const [resizingColumn, setResizingColumn] = useState<string | null>(null);
  const [resizeStartX, setResizeStartX] = useState(0);
  const [resizeStartWidth, setResizeStartWidth] = useState(0);
  const [cursorStyle, setCursorStyle] = useState<string>('default');
  const [userSelectStyle, setUserSelectStyle] = useState<string>('auto');
  const tableRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (searchColumns) {
      const initialWidths: { [key: string]: number } = {};
      searchColumns.forEach(col => {
        initialWidths[col.field] = col.checkboxSelection ? 80 : col.width || 200;
      });
      setColumnWidths(initialWidths);
    }
  }, [searchColumns]);

  // Apply cursor and user-select styles when resizing
  useEffect(() => {
    if (isResizing) {
      setCursorStyle('col-resize');
      setUserSelectStyle('none');
    } else {
      setCursorStyle('default');
      setUserSelectStyle('auto');
    }
  }, [isResizing]);

  useEffect(() => {
    if (!isResizing || !resizingColumn) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (tableRef.current) {
        const column = searchColumns?.find(col => col.field === resizingColumn);
        const minWidth = column?.checkboxSelection ? 60 : 100;
        const newWidth = Math.max(minWidth, resizeStartWidth + (e.clientX - resizeStartX));

        requestAnimationFrame(() => {
          setColumnWidths(prev => ({
            ...prev,
            [resizingColumn]: newWidth
          }));
        });
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizingColumn(null);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, resizingColumn, resizeStartX, resizeStartWidth, searchColumns]);

  const startResize = (e: React.MouseEvent, columnField: string) => {
    e.preventDefault();
    e.stopPropagation();

    setIsResizing(true);
    setResizingColumn(columnField);
    setResizeStartX(e.clientX);
    setResizeStartWidth(columnWidths[columnField] || 200);
  };

  return {
    columnWidths,
    isResizing,
    cursorStyle,
    userSelectStyle,
    tableRef,
    startResize
  };
};
