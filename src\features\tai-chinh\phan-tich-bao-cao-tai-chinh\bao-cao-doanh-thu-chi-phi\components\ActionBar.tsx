import { Pin, RefreshCw, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { formatDateForDisplay } from '../utils/dateUtils';
import AritoIcon from '@/components/custom/arito/icon';
import { SearchFormValues } from '../types';

interface ActionBarProps {
  onSearch?: () => void;
  onRefresh?: () => void;
  onFixedColumns?: () => void;
  onExportData?: () => void;
  onEditPrintTemplate?: () => void;
  className?: string;
  searchParams?: SearchFormValues;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearch,
  onRefresh,
  onFixedColumns,
  onExportData,
  onEditPrintTemplate,
  className,
  searchParams
}) => {
  const formatDateRange = () => {
    if (!searchParams) return '';
    return `Từ ngày ${formatDateForDisplay(searchParams.from_date)}`;
  };

  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Báo cáo doanh thu chi phí</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>{formatDateRange()}</span>
            </p>
          </div>
        </div>
      }
    >
      {onSearch && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearch} variant='primary' />}

      {onRefresh && <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} variant='destructive' />}
      {onFixedColumns && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumns} variant='secondary' />
      )}
      {onExportData && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportData} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplate,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
