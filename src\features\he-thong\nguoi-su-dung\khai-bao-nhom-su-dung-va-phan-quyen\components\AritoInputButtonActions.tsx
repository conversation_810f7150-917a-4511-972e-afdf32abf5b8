import { Box, Button, IconButton, Tooltip, Typography } from '@mui/material';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import AritoIcon from '@/components/custom/arito/icon';

interface ModeViewProps {
  startItem?: number;
  endItem?: number;
  totalItems?: number;
  paginationModel?: { page: number; pageSize: number };
  totalPages?: number;
  PageNumbers?: React.ReactNode;
  handlePageChange?: (newPage: number) => void;
}

const buttonSx2 = {
  textTransform: 'none',
  padding: '0px 7px 0px 0px',
  border: '1px solid transparent',
  borderRadius: 0,
  cursor: 'default',
  '&:hover': {
    border: '1.3px solid rgb(171, 211, 239)',
    backgroundColor: 'rgba(25, 118, 210, 0.15)',
    color: '#007acc',
    borderRadius: 0
  },
  '&.Mui-disabled': {
    color: 'rgba(0, 0, 0, 0.26)'
  }
};

export function AritoInputButtonActions({
  startItem,
  endItem,
  totalItems,
  totalPages,
  paginationModel,
  handlePageChange,
  PageNumbers
}: ModeViewProps) {
  return (
    <div className='flex-none border-b border-gray-200 bg-white'>
      <Box>
        <div className='flex items-center justify-between pl-1'>
          <div className='flex items-center gap-0.5'>
            <Tooltip title='Cố định cột' arrow>
              <Button size='small' variant='text' sx={buttonSx2}>
                <AritoIcon icon={16} marginX='0.5rem' />
                Cố định cột
              </Button>
            </Tooltip>
          </div>
          <div className='flex items-center'>
            {/* <Typography component="div" sx={{ fontSize: "0.75rem" }}>
              <span style={{ fontWeight: 600 }}>
                {startItem} - {endItem}
              </span>
              {" trong "}
              <span style={{ fontWeight: 600 }}>{totalItems}</span>
            </Typography> */}

            {/* <div className="flex items-center ml-4">
              <IconButton
                size="small"
                onClick={() => handlePageChange(paginationModel.page - 1)}
                disabled={paginationModel.page === 0}
                sx={{
                  color: "text.secondary",
                  "&:hover": { color: "black" },
                }}
              >
                <NavigateBeforeIcon fontSize="small" />
              </IconButton>
              {PageNumbers}
              <IconButton
                size="small"
                onClick={() => handlePageChange(paginationModel.page + 1)}
                disabled={paginationModel.page >= totalPages - 1}
                sx={{
                  color: "text.secondary",
                  "&:hover": { color: "black" },
                }}
              >
                <NavigateNextIcon fontSize="small" />
              </IconButton>
            </div> */}
          </div>
        </div>
      </Box>
    </div>
  );
}
