import { GridColDef } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'ma_dtt',
    headerName: 'Mã đợt',
    width: 150
  },
  {
    field: 'ten_dtt',
    headerName: 'Tên đợt thanh toán',
    width: 250
  },
  {
    field: 'stt',
    headerName: 'Số thứ tự',
    width: 120
  }
];
