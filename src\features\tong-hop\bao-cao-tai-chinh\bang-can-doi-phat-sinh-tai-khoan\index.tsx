'use client';

import React from 'react';
import { ActionBar, SearchDialog, EditPrintTemplateDialog } from './components';
import { useRowSelection, useDialogState, useTrialBalanceData } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { TrialBalanceColumns } from './cols-definition';
import { initialSearchValues } from './schema';

export default function BangCanDoiPhatSinhTaiKhoan({ initialRows }: { initialRows: any[] }) {
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showData,
    showSearchDialog,
    showEditPrintDialog,
    searchParams,

    openSearchDialog,
    closeSearchDialog,
    openEditPrintDialog,
    closeEditPrintDialog,

    handleSearchButtonClick,
    handleViewBookButtonClick,
    handleRefreshButtonClick,
    handleFixedColumnsButtonClick,
    handleEditPrintButtonClick,
    handleExportButtonClick
  } = useDialogState(clearSelection);

  // Use trial balance data hook
  const { data: trialBalanceData, isLoading, error } = useTrialBalanceData(searchParams || initialSearchValues);

  const tables = [
    {
      name: '',
      rows: trialBalanceData,
      columns: TrialBalanceColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          openSearchDialog={showSearchDialog}
          onCloseSearchDialog={closeSearchDialog}
          onSearch={handleSearchButtonClick}
        />
      )}

      <EditPrintTemplateDialog
        open={showEditPrintDialog}
        onClose={closeEditPrintDialog}
        onSave={handleEditPrintButtonClick}
      />

      {showData && (
        <>
          <ActionBar
            onSearchClick={handleSearchButtonClick}
            onViewBookClick={handleViewBookButtonClick}
            onRefreshClick={handleRefreshButtonClick}
            onFixedColumnsClick={handleFixedColumnsButtonClick}
            onExportClick={handleExportButtonClick}
            onEditPrintClick={handleEditPrintButtonClick}
            searchParams={searchParams}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
