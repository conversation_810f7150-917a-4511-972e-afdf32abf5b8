import { GridColDef } from '@mui/x-data-grid';
import type { ExtendedGridColDef } from '@/components/custom/arito/search-table';

// Main table
export const getDataTableColumns = (): GridColDef[] => [
  { field: 'trang_thai', headerName: 'Trạng thái', width: 100 },
  { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
  { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ma_khach_hang', headerName: 'Mã khách hàng', width: 150 },
  { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'tai_khoan_no', headerName: 'Tk nợ', width: 100 },
  { field: 'tong_tien', headerName: 'Tổng tiền', width: 150 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 },
  { field: 'loai_phieu_thu', headerName: 'Loại phiếu thu', width: 150 }
];

// Chi Tiết tab
export const getDetailsTableColumns = (): GridColDef[] => [
  { field: 'ma_khach_hang', headerName: 'Mã khách hàng', width: 120 },
  { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
  { field: 'du_cong_no', headerName: 'Dư công nợ', width: 150 },
  { field: 'hoa_don', headerName: 'Hóa đơn', width: 120 },
  { field: 'ngay_hoa_don', headerName: 'Ngày hóa đơn', width: 120 },
  { field: 'tai_khoan_co', headerName: 'Tài khoản có', width: 120 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 },
  { field: 'ty_gia_hoa_don', headerName: 'Tỷ giá hđ', width: 100 },
  { field: 'tien_tren_hoa_don', headerName: 'Tiền trên hóa đơn', width: 150 },
  { field: 'da_phan_bo', headerName: 'Đã phân bổ', width: 120 },
  { field: 'con_lai', headerName: 'Còn lại', width: 120 },
  { field: 'tien_vnd', headerName: 'Tiền VND', width: 150 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 120 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 120 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 120 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 120 },
  { field: 'phi', headerName: 'Phí', width: 80 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'chi_phi_khong_hop_le', headerName: 'C/p không h/lệ', width: 150 }
];

/* Search Columns */

export const diaChiSearchColumns: ExtendedGridColDef[] = [
  { field: 'address', headerName: 'Địa chỉ' },
  { field: 'description', headerName: 'Diễn giải' }
];
// DocumentNumber search columns
const DocumentNumberSearchColumns = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', flex: 2 }
];

export const employeeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nv', headerName: 'Mã nhân viên' },
  { field: 'ten_nv', headerName: 'Tên nhân viên', flex: 2 }
];

export const transportMethodSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ptvc', headerName: 'Mã phương tiện' },
  { field: 'ten_ptvc', headerName: 'Tên phương tiện' }
];
export const paymentMethodSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_pttt', headerName: 'Mã phương thức' },
  { field: 'ten_pttt', headerName: 'Tên phương thức' }
];
export const deliveryMethodSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ptgh', headerName: 'Mã phương thức' },
  { field: 'ten_ptgh', headerName: 'Tên phương thức' }
];

export const customerSearchColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã khách hàng', flex: 1 },
  { field: 'customer_name', headerName: 'Tên khách hàng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 1 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'parent_account_code', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'is_parent_account', headerName: 'Tk sổ cái', flex: 1, checkboxSelection: true },
  { field: 'active', headerName: 'Tk chi tiết', flex: 1, checkboxSelection: true },
  { field: 'account_level', headerName: 'Bậc tk', flex: 1 }
];

export const materialSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 120 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'nhom_1', headerName: 'Nhóm 1', width: 120 }
];

export const warehouseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'don_vi', headerName: 'Đơn vị', width: 120 },
  { field: 'theo_doi_vi_tri', headerName: 'Theo dõi vị trí', width: 120 }
];

export const batchSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lo', headerName: 'Mã lô', width: 120 },
  { field: 'ten_lo', headerName: 'Tên lô', width: 280 }
];

export const locationSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 120 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', width: 280 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_don_vi', headerName: 'Mã đơn vị', width: 120 },
  { field: 'ten_don_vi', headerName: 'Tên đơn vị', width: 250 }
];
