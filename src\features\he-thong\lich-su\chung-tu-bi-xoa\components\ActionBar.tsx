import { Pin, Search, FileDown, Refresh<PERSON><PERSON>, Printer } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import { AritoMenuActionButton } from './arito-menu-action-custom';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';
interface ActionBarProps {
  printedSample: any[];
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
  onPrintEditClick: () => void;
}

export const ActionBar = ({
  printedSample,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintEditClick,
  onPrintClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Các chứng từ bị xóa</h1>}>
    <AritoActionButton
      title='Tìm kiếm'
      icon={Search}
      onClick={() => {
        onSearchClick();
      }}
    />
    {printedSample.length > 0 && (
      <AritoMenuActionButton
        title='In ấn'
        icon={Printer}
        items={printedSample.map(item => ({
          title: item.title,
          icon: <AritoIcon icon={247} />,
          onClick: () => {
            onPrintEditClick();
          },
          group: 0
        }))}
      />
    )}
    <AritoActionButton
      title='Refesh'
      icon={RefreshCw}
      onClick={() => {
        onRefreshClick();
      }}
    />
    <AritoActionButton title='Cố định cột' icon={Pin} onClick={() => {}} />
    <AritoActionButton title='Kết xuất dữ liệu' icon={FileDown} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={9} />,
          onClick: () => {
            onPrintClick();
          },
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
