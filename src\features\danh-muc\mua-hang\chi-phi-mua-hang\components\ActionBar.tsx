import { Eye, FileText, Pencil, Plus, Trash } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick?: () => void;
  onViewClick?: () => void;
  onRefreshClick?: () => void;
}

export function ActionBar({ onAddClick, onEditClick, onDeleteClick, onCopyClick, onViewClick, onRefreshClick }: Props) {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục chi phí</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
      <AritoActionButton title='Xoá' variant='destructive' icon={Trash} onClick={onDeleteClick} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
      <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
}
