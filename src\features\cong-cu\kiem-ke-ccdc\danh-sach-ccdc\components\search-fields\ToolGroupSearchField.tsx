import { FormField } from '@/components/custom/arito/form/form-field';
import { toolGroupSearchColumns } from '../../cols-definition';
import { Label } from '@/components/ui/label';

interface ToolGroupSearchFieldProps {
  formMode?: 'add' | 'edit' | 'view';
  name1?: string;
  name2?: string;
  name3?: string;
  label1?: string;
  label2?: string;
  label3?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
}

export const ToolGroupSearchField: React.FC<ToolGroupSearchFieldProps> = ({
  formMode = 'add',
  name1 = 'toolGroup1',
  name2 = 'toolGroup2',
  name3 = 'toolGroup3',
  label1 = 'Nhóm công cụ 1',
  label2 = 'Nhóm công cụ 2',
  label3 = 'Nhóm công cụ 3',
  labelClassName = 'w-40 min-w-40',
  className = 'flex items-center',
  inputClassName = 'w-full min-w-[300px]',
  disabled = false
}) => {
  return (
    <div className='space-y-2'>
      {/* Nhóm công cụ 1 */}
      <div className={className}>
        <Label className={labelClassName}>{label1}</Label>
        <FormField
          name={name1}
          type='text'
          className={inputClassName}
          label=''
          searchColumns={toolGroupSearchColumns}
          searchEndpoint='/api/tool-groups'
          defaultSearchColumn='name'
          disabled={disabled || formMode === 'view'}
        />
      </div>

      {/* Nhóm công cụ 2 */}
      <div className={className}>
        <Label className={labelClassName}>{label2}</Label>
        <FormField
          name={name2}
          type='text'
          className={inputClassName}
          label=''
          searchColumns={toolGroupSearchColumns}
          searchEndpoint='/api/tool-groups'
          defaultSearchColumn='name'
          disabled={disabled || formMode === 'view'}
        />
      </div>

      {/* Nhóm công cụ 3 */}
      <div className={className}>
        <Label className={labelClassName}>{label3}</Label>
        <FormField
          name={name3}
          type='text'
          className={inputClassName}
          label=''
          searchColumns={toolGroupSearchColumns}
          searchEndpoint='/api/tool-groups'
          defaultSearchColumn='name'
          disabled={disabled || formMode === 'view'}
        />
      </div>
    </div>
  );
};
