import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/types/schemas';

export interface UseSearchFieldStatesReturn {
  account: <PERSON><PERSON><PERSON><PERSON> | null;
  setAccount: (account: <PERSON><PERSON>ho<PERSON> | null) => void;
  customer: <PERSON>hachHang | null;
  setCustomer: (customer: <PERSON><PERSON><PERSON><PERSON><PERSON> | null) => void;
}

export function useSearchFieldStates(): UseSearchFieldStatesReturn {
  const [account, setAccount] = useState<TaiKhoan | null>(null);
  const [customer, setCustomer] = useState<KhachHang | null>(null);

  return {
    account,
    setAccount,
    customer,
    setCustomer
  };
}
