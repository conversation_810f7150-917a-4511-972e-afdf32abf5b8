import { GridRowParams } from '@mui/x-data-grid';
import React, { useState, useMemo } from 'react';
import { generatePeriodColumns, generateDynamicMockData } from '../utils/dateUtils';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { SearchFormValues } from '../types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  // Generate dynamic columns based on search parameters
  const dynamicColumns = useMemo(() => {
    // Use numberOfTtDays as number of periods, fallback to legacy so_ky
    const numberOfPeriods = Number(searchParams.numberOfTtDays) || searchParams.so_ky || 12;
    // Use reportDate as start date, fallback to legacy tu_ngay
    const startDate = searchParams.reportDate || searchParams.tu_ngay || '2024-01-01';
    // Use balance as period type, fallback to legacy loai_thoi_gian
    const periodType = (searchParams.balance || searchParams.loai_thoi_gian || 'month') as
      | 'day'
      | 'week'
      | 'month'
      | 'quarter'
      | 'halfYear'
      | 'year';

    const periodColumns = generatePeriodColumns(startDate, numberOfPeriods, periodType);

    return [
      {
        field: 'ma_so',
        headerName: 'Mã số',
        width: 80
      },
      {
        field: 'chi_tieu',
        headerName: 'Chỉ tiêu',
        width: 300
      },
      ...periodColumns,
      {
        field: 'tong_cong',
        headerName: 'Tổng cộng',
        width: 150,
        type: 'number' as const
      }
    ];
  }, [
    searchParams.numberOfTtDays,
    searchParams.so_ky,
    searchParams.reportDate,
    searchParams.tu_ngay,
    searchParams.balance,
    searchParams.loai_thoi_gian
  ]);

  // Generate dynamic mock data
  const dynamicData = useMemo(() => {
    const numberOfPeriods = Number(searchParams.numberOfTtDays) || searchParams.so_ky || 12;
    return generateDynamicMockData(numberOfPeriods);
  }, [searchParams.numberOfTtDays, searchParams.so_ky]);

  const handleRowClick = (params: GridRowParams) => {
    const report = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
  };

  const tables = [
    {
      name: 'Báo cáo kết quả hoạt động sản xuất kinh doanh cho nhiều kỳ',
      rows: dynamicData,
      columns: dynamicColumns
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading: false,
    error: null,
    refreshData: async () => {}
  };
}
