/**
 * Utility functions for date calculations in business reports
 */

export interface MonthColumn {
  field: string;
  headerName: string;
  width: number;
  type?: 'number' | 'string' | 'date' | 'dateTime' | 'boolean' | 'singleSelect' | 'actions';
}

/**
 * Generate period columns based on start date, period type and number of periods
 * @param startDate - Start date in format YYYY-MM-DD
 * @param numberOfPeriods - Number of periods to generate
 * @param periodType - Type of period (day, week, month, quarter, halfYear, year)
 * @returns Array of column definitions
 */
export function generatePeriodColumns(
  startDate: string,
  numberOfPeriods: number,
  periodType: 'day' | 'week' | 'month' | 'quarter' | 'halfYear' | 'year' = 'month'
): MonthColumn[] {
  if (!startDate || !numberOfPeriods) {
    // Default fallback columns
    return [
      { field: 'ky_1', headerName: 'Tháng 1/2024', width: 150, type: 'number' as const },
      { field: 'ky_2', headerName: 'Tháng 2/2024', width: 150, type: 'number' as const },
      { field: 'ky_3', headerName: 'Tháng 3/2024', width: 150, type: 'number' as const },
      { field: 'ky_4', headerName: 'Tháng 4/2024', width: 150, type: 'number' as const }
    ];
  }

  const columns: MonthColumn[] = [];
  const date = new Date(startDate);

  for (let i = 0; i < numberOfPeriods; i++) {
    let currentDate: Date;
    let headerName: string;

    switch (periodType) {
      case 'day':
        currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + i);
        headerName = `Ngày ${currentDate.getDate()}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
        break;

      case 'week':
        currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + i * 7);
        const weekStart = currentDate.getDate();
        const weekEnd = weekStart + 6;
        headerName = `Tuần ${weekStart}-${weekEnd}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
        break;

      case 'month':
        currentDate = new Date(date.getFullYear(), date.getMonth() + i, 1);
        headerName = `Tháng ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
        break;

      case 'quarter':
        const quarterIndex = Math.floor(date.getMonth() / 3) + i;
        const quarterYear = date.getFullYear() + Math.floor(quarterIndex / 4);
        const quarter = (quarterIndex % 4) + 1;
        headerName = `Quý ${quarter}/${quarterYear}`;
        break;

      case 'halfYear':
        const halfYearIndex = Math.floor(date.getMonth() / 6) + i;
        const halfYear = date.getFullYear() + Math.floor(halfYearIndex / 2);
        const half = (halfYearIndex % 2) + 1;
        headerName = `Nửa năm ${half}/${halfYear}`;
        break;

      case 'year':
        const year = date.getFullYear() + i;
        headerName = `Năm ${year}`;
        break;

      default:
        currentDate = new Date(date.getFullYear(), date.getMonth() + i, 1);
        headerName = `Tháng ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
    }

    columns.push({
      field: `ky_${i + 1}`,
      headerName,
      width: 150,
      type: 'number' as const
    });
  }

  return columns;
}

// Keep the old function for backward compatibility
export function generateMonthColumns(startDate: string, numberOfPeriods: number): MonthColumn[] {
  return generatePeriodColumns(startDate, numberOfPeriods, 'month');
}

/**
 * Generate mock data with dynamic period fields
 * @param numberOfPeriods - Number of periods
 * @returns Mock data with dynamic period fields
 */
export function generateDynamicMockData(numberOfPeriods: number) {
  const baseData = [
    {
      id: '1',
      ma_so: '01',
      chi_tieu: '1. Doanh thu bán hàng và cung cấp dịch vụ',
      baseAmount: 1000000000
    },
    {
      id: '2',
      ma_so: '01A',
      chi_tieu: '- Doanh thu bán hàng và cung cấp dịch vụ',
      baseAmount: 800000000
    },
    {
      id: '3',
      ma_so: '02',
      chi_tieu: '2. Các khoản giảm trừ doanh thu',
      baseAmount: 200000000
    },
    {
      id: '4',
      ma_so: '02A',
      chi_tieu: '+ Chiết khấu thương mại',
      baseAmount: 100000000
    },
    {
      id: '5',
      ma_so: '02B',
      chi_tieu: '+ Giảm giá',
      baseAmount: 50000000
    },
    {
      id: '6',
      ma_so: '02C',
      chi_tieu: '+ Hàng bán bị trả lại',
      baseAmount: 50000000
    },
    {
      id: '7',
      ma_so: '10',
      chi_tieu: '3. Doanh thu thuần về bán hàng và cung cấp dịch vụ',
      baseAmount: 800000000
    },
    {
      id: '8',
      ma_so: '11',
      chi_tieu: '4. Giá vốn hàng bán',
      baseAmount: 600000000
    },
    {
      id: '9',
      ma_so: '20',
      chi_tieu: '5. Lợi nhuận gộp về bán hàng và cung cấp dịch vụ',
      baseAmount: 200000000
    },
    {
      id: '10',
      ma_so: '21',
      chi_tieu: '6. Doanh thu hoạt động tài chính',
      baseAmount: 50000000
    }
  ];

  return baseData.map(item => {
    const result: any = {
      id: item.id,
      ma_so: item.ma_so,
      chi_tieu: item.chi_tieu
    };

    let total = 0;

    // Generate dynamic period data
    for (let i = 1; i <= numberOfPeriods; i++) {
      const periodValue = item.baseAmount + (Math.random() * 0.2 - 0.1) * item.baseAmount; // ±10% variation
      result[`ky_${i}`] = Math.round(periodValue);
      total += result[`ky_${i}`];
    }

    result.tong_cong = total;

    return result;
  });
}

/**
 * Format date for display in ActionBar
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Formatted date string
 */
export function formatDateForDisplay(dateString: string): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  } catch {
    return dateString;
  }
}
