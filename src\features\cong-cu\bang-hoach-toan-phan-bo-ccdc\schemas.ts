import { z } from 'zod';

export const revenueExpenseReportSchema = z.object({});

export type EditPrintTemplateTableType = object;

export const editPrintTemPlateSchema = z.object({});

export type EditPrintTemplateFormValues = z.infer<typeof editPrintTemPlateSchema>;

export const searchSchema = z.object({
  tu_ky: z.string().optional(),
  tu_nam: z.string().optional(),
  den_ky: z.string().optional(),
  den_nam: z.string().optional(),
  mau_bc: z.number().optional(),
  data_analysis_struct: z.number().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  tu_ky: `${new Date().getMonth() + 1}`,
  tu_nam: `${new Date().getFullYear()}`,
  den_ky: `${new Date().getMonth() + 1}`,
  den_nam: `${new Date().getFullYear()}`,
  mau_bc: 0,
  data_analysis_struct: 0
};
