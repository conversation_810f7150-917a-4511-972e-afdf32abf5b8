import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const supplierGroupSearchColumns: GridColDef[] = [
  {
    field: 'supplierGroupCode',
    headerName: 'Mã nhóm nhà cung cấp',
    width: 150
  },
  {
    field: 'supplierGroupName',
    headerName: 'Tên nhóm nhà cung cấp',
    width: 250
  }
];

export const productSearchColumns: GridColDef[] = [
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 250
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.dvt_data.dvt || '';
    }
  },
  {
    field: 'nhom_vt1',
    headerName: 'Nhóm 1',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      // return params.row.nhom_vt1_data.ma_nhom || '';
    }
  },
  {
    field: 'lo_yn',
    headerName: 'Theo dõi lô',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'qc_yn',
    headerName: 'Quy cách',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'hinh_anh',
    headerName: 'Hình ảnh',
    width: 120
  }
];

export const itemTypeSearchColumns = [
  { field: 'loai_vat_tu', headerName: 'Loại vật tư', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại vật tư', width: 280 }
];

export const productGroupSearchColumns: GridColDef[] = [
  {
    field: 'productGroupCode',
    headerName: 'Mã nhóm vật tư',
    width: 150
  },
  {
    field: 'productGroupName',
    headerName: 'Tên nhóm vật tư',
    width: 250
  }
];

export const warehouseSearchColumns: GridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 },
  {
    field: 'vi_tri_yn',
    headerName: 'Theo dõi vị trí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  }
];

export const supplierSearchColumns: GridColDef[] = [
  { field: 'supplier_code', headerName: 'Mã nhà cung cấp', width: 150 },
  { field: 'supplier_name', headerName: 'Tên nhà cung cấp', width: 200 },
  { field: 'CongNoPhaiThu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'CongNoPhaiTra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'phone', headerName: 'Số điện thoại', width: 150 }
];

export const accountSearchColumns: GridColDef[] = [
  {
    field: 'code',
    headerName: 'Mã tài khoản',
    width: 150
  },
  {
    field: 'name',
    headerName: 'Tên tài khoản',
    width: 200,
    flex: 1
  },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    width: 150,
    editable: false,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.parent_account_code_data?.code || '';
    }
  },
  {
    field: 'is_parent_account',
    headerName: 'TK sổ cái',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'TK chi tiết',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'bac_tk',
    headerName: 'Bậc TK',
    width: 100,
    editable: false,
    type: 'number'
  }
];

export const customerSearchColumns: GridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 200 },
  { field: 'CongNoPhaiThu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'CongNoPhaiTra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 }
];

export const ngoaiTeSearchColumns: GridColDef[] = [
  { field: 'ma_nt', headerName: 'Mã ngoại tệ', width: 100 },
  { field: 'ten_nt', headerName: 'Tên ngoại tệ', width: 200 }
];

export const boPhanSearchColumns: GridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 150 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', width: 250 }
];

export const vuViecSearchColumns: GridColDef[] = [
  { field: 'ma_vv', headerName: 'Mã vụ việc', width: 150 },
  { field: 'ten_vv', headerName: 'Tên vụ việc', width: 250 }
];

export const sanPhamSearchColumns: GridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã sản phẩm', width: 100 },
  { field: 'ten_vt', headerName: 'Tên sản phẩm', width: 200 }
];

export const hopDongSearchColumns: GridColDef[] = [
  { field: 'ma_hop_dong', headerName: 'Mã hợp đồng', width: 100 },
  { field: 'ten_hop_dong', headerName: 'Tên hợp đồng', width: 200 }
];

export const kheUocSearchColumns: GridColDef[] = [
  { field: 'ma_ku', headerName: 'Mã khế ước', width: 100 },
  { field: 'ten_ku', headerName: 'Tên khế ước', width: 200 }
];

export const phiSearchColumns: GridColDef[] = [
  { field: 'ma_phi', headerName: 'Mã phí', width: 100 },
  { field: 'ten_phi', headerName: 'Tên phí', width: 200 }
];

export const lenhSanXuatSearchColumns: GridColDef[] = [
  { field: 'so_lsx', headerName: 'Số lệnh', width: 100 },
  { field: 'description', headerName: 'Diễn giải', width: 200 }
];

export const invalidCostSearchColumns: GridColDef[] = [
  { field: 'ma_chi_phi', headerName: 'Mã chi phí', width: 100 },
  { field: 'ten_chi_phi', headerName: 'Tên chi phí', width: 200 }
];
