import React, { useState } from 'react';

import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    onSearch(data);
  };

  const handleDirectSubmit = () => {
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Thẻ tài sản cố định'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
              onClick={handleDirectSubmit}
            >
              <AritoIcon icon={884} className='mr-2' />
              Đồng ý
            </Button>
            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} className='mr-2' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
