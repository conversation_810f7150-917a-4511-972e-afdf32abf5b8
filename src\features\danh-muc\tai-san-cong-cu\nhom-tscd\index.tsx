'use client';

import React from 'react';
import { AritoSplitPane } from '@/components/custom/arito/split-pane';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, DeleteDialog } from './components';
import SidebarButton from '@/components/arito/sidebar-button';
import { AssetGroup } from '@/types/schemas/nhom-tscd.type';
import { getDataTableColumns } from './cols-definition';
import { useFormState, useAssetGroup } from './hooks';

export default function NhomTSCD() {
  const {
    assetGroups,
    isLoading,
    addAssetGroup,
    updateAssetGroup,
    deleteAssetGroup,
    refreshAssetGroups,
    fetchAssetGroupsByType
  } = useAssetGroup();

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState();

  // Group types and filtering state
  const assetGroupTypes = [
    { label: 'Nhóm tài sản 1 (TS1)', value: 'TS1' },
    { label: 'Nhóm tài sản 2 (TS2)', value: 'TS2' },
    { label: 'Nhóm tài sản 3 (TS3)', value: 'TS3' }
  ];

  const [activeGroup, setActiveGroup] = React.useState<string>(assetGroupTypes[0].value);

  // Filter groups based on active group type
  const filteredGroups = React.useMemo(() => {
    return assetGroups.filter(group => group.loai_nhom === activeGroup);
  }, [assetGroups, activeGroup]);

  // Fetch initial data with active group
  React.useEffect(() => {
    fetchAssetGroupsByType(activeGroup);
  }, []);

  // Handle group type filter change
  const handleFilter = (group: string) => {
    setActiveGroup(group);
    fetchAssetGroupsByType(group);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      // Add the active group type to the form data
      const formData = {
        ...data,
        loai_nhom: activeGroup
      };

      if (formMode === 'add') {
        await addAssetGroup(formData);
      } else if (formMode === 'edit' && selectedObj) {
        await updateAssetGroup({
          uuid: selectedObj.uuid,
          ...formData
        });
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const tables = [
    {
      name: '',
      rows: filteredGroups, // use filtered groups
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <AritoSplitPane split='vertical' minSize={200} defaultSize={250}>
        <div className='h-full overflow-y-auto border-r p-4'>
          {/* Sidebar: Loại nhóm */}
          <h2 className='mb-4 text-lg font-semibold'>Loại nhóm</h2>
          <ul>
            {assetGroupTypes.map(group => (
              <li key={group.value} className='mb-2 text-sm'>
                <SidebarButton isActive={activeGroup === group.value} onClick={() => handleFilter(group.value)}>
                  {group.label}
                </SidebarButton>
              </li>
            ))}
          </ul>
        </div>
        <div className='h-full overflow-y-auto'>
          <div className='w-full'>
            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onViewClick={() => selectedObj && handleViewClick()}
              onRefreshClick={() => refreshAssetGroups(activeGroup)}
              isEditDisabled={!selectedObj}
            />

            {isLoading && (
              <div className='flex h-64 items-center justify-center'>
                <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
              </div>
            )}

            {!isLoading && (
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            )}
          </div>
        </div>
      </AritoSplitPane>

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteAssetGroup={deleteAssetGroup}
          clearSelection={clearSelection}
        />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          onAddButtonClick={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDeleteButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}
    </div>
  );
}
