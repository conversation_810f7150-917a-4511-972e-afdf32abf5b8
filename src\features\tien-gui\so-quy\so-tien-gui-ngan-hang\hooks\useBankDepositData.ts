import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export interface BankDepositItem {
  id: string;
  ngay_chung_tu: string;
  so_chung_tu: string;
  ma_khach_hang: string;
  ten_khach_hang: string;
  dien_giai: string;
  tk_doi_ung: string;
  ngoai_te: string;
  ty_gia: number;
  ps_no: number;
  ps_co: number;
  so_du: number;
  ma_chung_tu: string;
  tai_khoan_ngan_hang: string;
}

export interface BankDepositResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BankDepositItem[];
}

export interface UseBankDepositReturn {
  data: BankDepositItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): BankDepositItem[] => {
  return [
    {
      id: '1',
      ngay_chung_tu: '01/04/2025',
      so_chung_tu: 'TG001',
      ma_khach_hang: 'KH001',
      ten_khach_hang: 'Công ty TNHH ABC',
      dien_giai: 'Tiền gửi tiết kiệm kỳ hạn 12 tháng',
      tk_doi_ung: '112',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 0,
      ps_co: *********,
      so_du: *********,
      ma_chung_tu: 'TG',
      tai_khoan_ngan_hang: '112'
    },
    {
      id: '2',
      ngay_chung_tu: '02/04/2025',
      so_chung_tu: 'TG002',
      ma_khach_hang: 'KH002',
      ten_khach_hang: 'Công ty CP XYZ',
      dien_giai: 'Tiền gửi không kỳ hạn',
      tk_doi_ung: '112',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 0,
      ps_co: *********,
      so_du: *********,
      ma_chung_tu: 'TG',
      tai_khoan_ngan_hang: '112'
    },
    {
      id: '3',
      ngay_chung_tu: '03/04/2025',
      so_chung_tu: 'TG003',
      ma_khach_hang: 'KH003',
      ten_khach_hang: 'Công ty TNHH DEF',
      dien_giai: 'Rút tiền gửi tiết kiệm',
      tk_doi_ung: '112',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 200000000,
      ps_co: 0,
      so_du: 600000000,
      ma_chung_tu: 'TG',
      tai_khoan_ngan_hang: '112'
    },
    {
      id: '4',
      ngay_chung_tu: '04/04/2025',
      so_chung_tu: 'TG004',
      ma_khach_hang: 'KH004',
      ten_khach_hang: 'Công ty CP GHI',
      dien_giai: 'Tiền gửi có kỳ hạn 6 tháng',
      tk_doi_ung: '112',
      ngoai_te: 'USD',
      ty_gia: 24500,
      ps_no: 0,
      ps_co: *********,
      so_du: *********,
      ma_chung_tu: 'TG',
      tai_khoan_ngan_hang: '112'
    }
  ];
};

/**
 * Custom hook for managing Bank Deposit Ledger data
 *
 * This hook provides functionality to fetch bank deposit data
 * with mock support for testing and development purposes.
 */
export function useBankDepositData(searchParams: SearchFormValues): UseBankDepositReturn {
  const [data, setData] = useState<BankDepositItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BankDepositResponse>('/tien-gui/so-quy/so-tien-gui-ngan-hang/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
