import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { Checkbox } from '@/components/ui/checkbox';

export const materialStandardColumns: GridColDef[] = [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250
  },
  {
    field: 't_ps_nt',
    headerName: 'Tổng phát sinh',
    width: 120
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120
  }
];

export const inputTableColumns: GridColDef[] = [
  { field: 'ma_ngvkt', headerName: '<PERSON><PERSON> nghiệ<PERSON> vụ', width: 120 },
  { field: 'ten_ngvkt', headerName: 'Tên nghiệp vụ', width: 250 },
  { field: 'tk_no', headerName: 'Tài khoản nợ', width: 120 },
  { field: 'tk_co', headerName: 'Tài khoản có', width: 120 },
  { field: 'ma_kh', headerName: 'Mã khách nợ', width: 120 },
  { field: 'ten_kh', headerName: 'Tên khách hàng', width: 250 },
  { field: 'ma_kh_co', headerName: 'Mã khách có', width: 120 },
  { field: 'ps_nt', headerName: 'Tiền %s', width: 120 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'so_ct0', headerName: 'Số hoá đơn', width: 120 },
  { field: 'ngay_ct0', headerName: 'Ngày hoá đơn', width: 120 },
  { field: 'ps', headerName: 'Tiền', width: 120 },
  { field: 'ma_bp', headerName: 'Bộ phận', width: 120 },
  { field: 'ma_vv', headerName: 'Vụ việc', width: 120 },
  { field: 'ma_hd', headerName: 'Hợp đồng', width: 120 },
  { field: 'ma_dtt', headerName: 'Đợt thanh toán', width: 120 },
  { field: 'ma_ku', headerName: 'Khế ước', width: 120 },
  { field: 'ma_phi', headerName: 'Phí', width: 120 },
  { field: 'ma_sp', headerName: 'Sản phẩm', width: 120 },
  { field: 'ma_lsx', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'ma_cp0', headerName: 'C/p không h/lệ', width: 120 }
];

export const documentSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nk', headerName: 'Mã quyển', width: 120 },
  { field: 'ten_nk', headerName: 'Tên quyển', width: 200 },
  { field: 'so_ct_mau', headerName: 'Số khai báo', width: 200 }
];

export const unitSearchColumns: GridColDef[] = [
  { field: 'checkbox', headerName: '', width: 50, renderCell: params => <Checkbox checked={params.row.checkbox} /> },
  { field: 'ma_unit', headerName: 'Mã đơn vị', width: 150 },
  { field: 'ten_unit', headerName: 'Tên đơn vị', width: 250 },
  { field: 'unit_id', headerName: 'ID', width: 100 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 2 },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    flex: 2,
    renderCell: (params: any) => {
      return params.row.parent_account_code_data?.code;
    }
  },
  {
    field: 'tk_so_cai',
    headerName: 'Tk sổ cái',
    type: 'boolean',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'Tk chi tiết',
    type: 'boolean',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'bac_tk',
    headerName: 'Bậc tk',
    type: 'number',
    flex: 1
  }
];

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kh', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'ten_kh', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'du_cn_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'du_cn_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', flex: 1 },
  { field: 'e_mail', headerName: 'Email', flex: 1 },
  { field: 'dien_thoai', headerName: 'Số điện thoại', flex: 1 }
];
