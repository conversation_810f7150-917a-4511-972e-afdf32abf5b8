import { GridColDef } from '@mui/x-data-grid';

export const InvoiceDebtReportColumns: GridColDef[] = [
  {
    field: 'stt',
    headerName: 'Stt',
    width: 100
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hoá đơn',
    width: 120
  },
  {
    field: 'so_ct0',
    headerName: 'Số hoá đơn',
    width: 120
  },
  {
    field: 'ma_kh',
    headerName: 'Mã NCC',
    width: 100
  },
  {
    field: 'ten_kh',
    headerName: 'Tên nhà cung cấp',
    width: 200
  },
  {
    field: 't_tt',
    headerName: 'Tổng tiền HĐ',
    width: 100
  },
  {
    field: 'tt',
    headerName: 'Đã trả',
    width: 100
  },
  {
    field: 'cl',
    headerName: '<PERSON><PERSON><PERSON> trả',
    width: 100
  },
  {
    field: 'trong_han',
    headerName: 'Trong hạn',
    width: 120
  },
  {
    field: 'qua_han01',
    headerName: 'Qu<PERSON> hạn 1-30 ngày',
    width: 120
  },

  {
    field: 'qua_han02',
    headerName: 'Quá hạn 31-60 ngày',
    width: 120
  },
  {
    field: 'qua_han03',
    headerName: 'Quá hạn 61-90 ngày',
    width: 120
  },
  {
    field: 'qua_han04',
    headerName: 'Quá hạn 91-120 ngày',
    width: 120
  },
  {
    field: 'qua_han05',
    headerName: 'Quá hạn trên 121 ngày',
    width: 120
  },
  {
    field: 'ngay_dh0',
    headerName: 'Ngày đến hạn',
    width: 120
  },
  {
    field: 'han_tt',
    headerName: 'Hạn tt',
    width: 120
  },
  {
    field: 'so_ngay',
    headerName: 'Số ngày đến hạn',
    width: 100
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'ma_unit',
    headerName: 'Đơn vị',
    width: 100
  }
];
