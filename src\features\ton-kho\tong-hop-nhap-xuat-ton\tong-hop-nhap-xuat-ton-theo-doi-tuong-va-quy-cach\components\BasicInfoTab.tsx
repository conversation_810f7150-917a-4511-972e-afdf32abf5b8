import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { IconButton } from '@mui/material';
import { AritoRangeSelect, DateRangePresets, FieldConfig } from '@/components/arito/arito-range-select';
import { PopupFormField } from '@/components/arito/arito-form/pop-up/arito-popup-form-field';
import AritoIcon from '@/components/custom/arito/icon';

const DEFAULT_PRESETS: DateRangePresets = {
  today: 'Hôm nay',
  thisWeek: 'Tuần này',
  thisMonth: 'Tháng này',
  currentToDate: 'Đầu tháng đến hiện tại',
  prevMonth: 'Tháng trước',
  nextMonth: 'Tháng sau',
  month1: 'Tháng 1',
  month2: 'Tháng 2',
  month3: 'Tháng 3',
  month4: 'Tháng 4',
  month5: 'Tháng 5',
  month6: 'Tháng 6',
  month7: 'Tháng 7',
  month8: 'Tháng 8',
  month9: 'Tháng 9',
  month10: 'Tháng 10',
  month11: 'Tháng 11',
  month12: 'Tháng 12',
  quarter1: 'Quý 1',
  quarter2: 'Quý 2',
  quarter3: 'Quý 3',
  quarter4: 'Quý 4',
  thisQuarter: 'Quý này',
  prevQuarter: 'Quý trước',
  quarterToDate: 'Đầu quý đến hiện tại',
  thisYear: 'Năm nay',
  prevYear: 'Năm trước',
  yearToDate: 'Đầu năm đến hiện tại'
};

const DATE_FIELDS: FieldConfig[] = [
  { id: 'ngay_tu', type: 'date' },
  { id: 'ngay_den', type: 'date' }
];

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  const { setValue } = useFormContext();
  const [filterExpanded, setFilterExpanded] = useState(false);

  useEffect(() => {
    // Set default date range to current month
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    setValue('ngay_tu', formatDate(startOfMonth));
    setValue('ngay_den', formatDate(endOfMonth));
  }, [setValue]);

  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  const updateFormValues = (fromDate: string, toDate: string) => {
    setValue('ngay_tu', fromDate);
    setValue('ngay_den', toDate);
  };

  // Updated handler for compatibility with the new AritoRangeSelect interface
  const handlePresetSelect = (
    preset: string,
    formatDate: (date: Date) => string,
    updateFieldValues: Record<string, string> | ((fieldValues: Record<string, string>) => void),
    fields: FieldConfig[]
  ) => {
    const today = new Date();
    let startDate = new Date();
    let endDate = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    const currentDay = today.getDate();

    switch (preset) {
      case 'today':
        // Both start and end are today (defaults)
        break;

      case 'thisWeek': {
        // Start from Sunday of current week
        const dayOfWeek = today.getDay();
        startDate = new Date(today);
        startDate.setDate(currentDay - dayOfWeek);
        endDate = new Date(today);
        endDate.setDate(startDate.getDate() + 6);
        break;
      }

      case 'thisMonth':
        startDate = new Date(currentYear, currentMonth, 1);
        endDate = new Date(currentYear, currentMonth + 1, 0);
        break;

      case 'currentToDate':
        startDate = new Date(currentYear, currentMonth, 1);
        // endDate is today (default)
        break;

      case 'prevMonth':
        startDate = new Date(currentYear, currentMonth - 1, 1);
        endDate = new Date(currentYear, currentMonth, 0);
        break;

      case 'nextMonth':
        startDate = new Date(currentYear, currentMonth + 1, 1);
        endDate = new Date(currentYear, currentMonth + 2, 0);
        break;

      // Handle month presets
      case 'month1':
      case 'month2':
      case 'month3':
      case 'month4':
      case 'month5':
      case 'month6':
      case 'month7':
      case 'month8':
      case 'month9':
      case 'month10':
      case 'month11':
      case 'month12': {
        const monthNumber = parseInt(preset.replace('month', '')) - 1; // 0-11
        startDate = new Date(currentYear, monthNumber, 1);
        endDate = new Date(currentYear, monthNumber + 1, 0);
        break;
      }

      // Handle quarter presets
      case 'quarter1':
      case 'quarter2':
      case 'quarter3':
      case 'quarter4': {
        const quarterNumber = parseInt(preset.replace('quarter', '')) - 1; // 0-3
        const quarterStartMonth = quarterNumber * 3;
        startDate = new Date(currentYear, quarterStartMonth, 1);
        endDate = new Date(currentYear, quarterStartMonth + 3, 0);
        break;
      }

      case 'thisQuarter': {
        const thisQuarterStartMonth = Math.floor(currentMonth / 3) * 3;
        startDate = new Date(currentYear, thisQuarterStartMonth, 1);
        endDate = new Date(currentYear, thisQuarterStartMonth + 3, 0);
        break;
      }

      case 'prevQuarter': {
        const prevQuarterStartMonth = Math.floor((currentMonth - 3) / 3) * 3;
        const prevQuarterYear = prevQuarterStartMonth < 0 ? currentYear - 1 : currentYear;
        const normalizedStartMonth = prevQuarterStartMonth < 0 ? prevQuarterStartMonth + 12 : prevQuarterStartMonth;

        startDate = new Date(prevQuarterYear, normalizedStartMonth, 1);
        endDate = new Date(prevQuarterYear, normalizedStartMonth + 3, 0);
        break;
      }

      case 'quarterToDate': {
        const thisQuarterStartMonth = Math.floor(currentMonth / 3) * 3;
        startDate = new Date(currentYear, thisQuarterStartMonth, 1);
        // endDate is today (default)
        break;
      }

      case 'thisYear':
        startDate = new Date(currentYear, 0, 1);
        endDate = new Date(currentYear, 11, 31);
        break;

      case 'prevYear':
        startDate = new Date(currentYear - 1, 0, 1);
        endDate = new Date(currentYear - 1, 11, 31);
        break;

      case 'yearToDate':
        startDate = new Date(currentYear, 0, 1);
        // endDate is today (default)
        break;

      default:
        return;
    }

    // Format the dates
    const fromDateStr = formatDate(startDate);
    const toDateStr = formatDate(endDate);

    // Use the new interface to update field values
    if (typeof updateFieldValues === 'function') {
      const fieldValues: Record<string, string> = {
        ngay_tu: fromDateStr,
        ngay_den: toDateStr
      };
      updateFieldValues(fieldValues);
    }

    // Also update our form directly
    updateFormValues(fromDateStr, toDateStr);
  };

  return (
    <div className='p-0'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        {/* Thông tin chi tiết công nợ */}
        <div className='space-y-2'>
          <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[2fr,1fr,1fr]'>
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='date'
              label='Kỳ từ/đến'
              name='ky_tu_den'
              disabled={formMode === 'view'}
            />
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='date'
              label=''
              name='ky_tu_den'
              disabled={formMode === 'view'}
            />
            <div className='-ml-4 -mt-4 flex'>
              <AritoRangeSelect fields={DATE_FIELDS} onPresetSelect={handlePresetSelect} presets={DEFAULT_PRESETS} />
            </div>
          </div>
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='multiselect'
            label='Đối tượng tồn kho'
            name='doi-tuong-ton-kho'
            disabled={formMode === 'view'}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Mã kho'
            name='ma_kho'
            disabled={formMode === 'view'}
            withSearch={true}
          />
        </div>
      </div>
    </div>
  );
};
