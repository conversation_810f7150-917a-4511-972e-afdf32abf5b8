'use client';

import {
  <PERSON>,
  Popper,
  <PERSON>lick<PERSON>way<PERSON><PERSON><PERSON>,
  <PERSON>row,
  MenuList,
  ListItemIcon,
  ListItemText,
  Divider,
  MenuItem
} from '@mui/material';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';
import { useState, useRef, useEffect, ReactNode } from 'react';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import AritoIcon from '@/components/custom/arito/icon';

export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: ReactNode;
  shortcut?: string;
  onClick?: () => void;
  type?: 'default' | 'search' | 'divider' | 'radio';
  group?: string;
  selected?: boolean;
}

export interface AritoContextMenuProps {
  items: ContextMenuItem[];
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  onSearch?: (value: string) => void;
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end' | 'right-start' | 'left-start';
}

export function AritoContextMenu({
  items,
  anchorEl,
  open,
  onClose,
  onSearch,
  placement = 'bottom-start'
}: AritoContextMenuProps) {
  const [searchValue, setSearchValue] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Focus search input when menu opens if there's a search item
  useEffect(() => {
    if (open && searchInputRef.current) {
      const hasSearchItem = items.some(item => item.type === 'search');
      if (hasSearchItem) {
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 100);
      }
    }
  }, [open, items]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Close menu on Escape
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <Popper open={open} anchorEl={anchorEl} placement={placement} transition style={{ zIndex: 1300 }}>
      {({ TransitionProps }) => (
        <Grow {...TransitionProps} style={{ transformOrigin: 'top left' }}>
          <Paper
            elevation={3}
            sx={{
              minWidth: '180px',
              maxWidth: '250px',
              overflow: 'hidden',
              borderRadius: 0
            }}
          >
            <ClickAwayListener onClickAway={onClose}>
              <MenuList
                dense
                autoFocusItem={false}
                onKeyDown={handleKeyDown}
                sx={{
                  padding: '3px'
                }}
              >
                {items.map((item, index) => {
                  if (item.type === 'divider') {
                    return (
                      <Divider
                        key={`divider-${index}`}
                        sx={{
                          my: 0,
                          borderColor: 'rgba(0, 0, 0, 0.12)',
                          '&.MuiDivider-root': {
                            margin: '3px 0',
                            padding: 0
                          }
                        }}
                      />
                    );
                  }

                  if (item.type === 'search') {
                    return (
                      <div key={item.id} className='p-0'>
                        <div className='relative flex w-full items-center'>
                          {item.shortcut && (
                            <div className='absolute right-2 bg-gray-100 px-1 text-xs text-gray-500'>
                              {item.shortcut}
                            </div>
                          )}
                          <input
                            ref={searchInputRef}
                            type='text'
                            value={searchValue}
                            onChange={handleSearchChange}
                            placeholder={item.label}
                            className='w-full border border-gray-300 py-1 pl-6 pr-2 text-sm outline-none'
                          />
                        </div>
                      </div>
                    );
                  }

                  return (
                    <MenuItem
                      key={item.id}
                      onClick={() => {
                        if (item.onClick) {
                          item.onClick();
                        }
                        onClose();
                      }}
                      selected={item.selected}
                      className='flex items-center gap-2'
                      sx={{
                        padding: '0px 6px',
                        border: '1px solid transparent',
                        cursor: 'default',
                        '&:hover': {
                          border: '1.3px solid rgb(171, 211, 239)',
                          color: '#007acc',
                          backgroundColor: 'rgba(25, 118, 210, 0.15)',
                          cursor: 'default'
                        },
                        '&.Mui-selected': {
                          backgroundColor: 'transparent'
                        }
                      }}
                    >
                      {item.icon && item.icon}
                      <ListItemText
                        primary={item.label}
                        primaryTypographyProps={{
                          variant: 'body2',
                          style: {
                            fontSize: '0.7rem'
                          }
                        }}
                      />
                      {item.shortcut && <div className='ml-2 text-xs text-gray-500'>{item.shortcut}</div>}
                      {item.selected && <AritoIcon icon={862} />}
                    </MenuItem>
                  );
                })}
              </MenuList>
            </ClickAwayListener>
          </Paper>
        </Grow>
      )}
    </Popper>
  );
}

// Helper function to create context menu items for column operations
export function createColumnMenuItems(
  columnName: string,
  onSort?: (direction: 'asc' | 'desc' | null) => void,
  onHide?: () => void,
  onCustomize?: () => void,
  onClearFilter?: () => void,
  onSearch?: (value: string) => void,
  currentSortDirection?: 'asc' | 'desc' | null
): ContextMenuItem[] {
  return [
    {
      id: 'sort-asc',
      label: 'Sắp xếp tăng dần',
      icon: <AritoIcon icon={13} />,
      type: 'radio',
      group: 'sort',
      selected: currentSortDirection === 'asc',
      onClick: () => onSort && onSort('asc')
    },
    {
      id: 'sort-desc',
      label: 'Sắp xếp giảm dần',
      icon: <AritoIcon icon={24} />,
      type: 'radio',
      group: 'sort',
      selected: currentSortDirection === 'desc',
      onClick: () => onSort && onSort('desc')
    },
    {
      id: 'sort-none',
      label: 'Không sắp xếp',
      icon: <AritoIcon icon={25} />,
      type: 'radio',
      group: 'sort',
      selected: currentSortDirection === null,
      onClick: () => onSort && onSort(null)
    },
    {
      id: 'divider-1',
      label: '',
      type: 'divider'
    },
    {
      id: 'hide-column',
      label: 'Ẩn cột',
      icon: <AritoIcon icon={886} />,
      onClick: onHide
    },
    {
      id: 'customize-column',
      label: 'Tuy chỉnh các cột',
      icon: <AritoIcon icon={887} />,
      onClick: onCustomize
    },
    {
      id: 'divider-2',
      label: '',
      type: 'divider'
    },
    {
      id: 'search',
      label: 'Tìm kiếm',
      type: 'search',
      shortcut: 'Ctrl+L'
    },
    {
      id: 'clear-filter',
      label: `Bỏ lọc cột [${columnName}]`,
      icon: <AritoIcon icon={861} />,
      onClick: onClearFilter
    }
  ];
}
