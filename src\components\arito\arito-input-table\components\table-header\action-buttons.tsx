import { I<PERSON><PERSON><PERSON>on, <PERSON><PERSON><PERSON>, But<PERSON>, Divider } from '@mui/material';
import { AritoInputTableActionButtonsProps } from '../../types';
import AritoIcon from '@/components/custom/arito/icon';
import { buttonSx } from '../../styles/button-styles';

export function ActionButtons({
  rowSelectionModel,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  errorMessage
}: AritoInputTableActionButtonsProps) {
  return (
    <div className='flex flex-1 items-center justify-between'>
      <div className='flex items-center'>
        <Tooltip title='Thêm dòng mới' arrow>
          <IconButton size='small' onClick={onAddRow} sx={buttonSx}>
            <AritoIcon icon={7} />
          </IconButton>
        </Tooltip>

        <Tooltip title='Xóa dòng đã chọn' arrow>
          <span>
            <IconButton size='small' onClick={onDeleteRow} disabled={rowSelectionModel.length === 0} sx={buttonSx}>
              <AritoIcon icon={8} />
            </IconButton>
          </span>
        </Tooltip>
        <Divider orientation='vertical' flexItem sx={{ mx: 1 }} />

        <div className='flex items-center'>
          <Tooltip title='Sao chép dòng' arrow>
            <span>
              <IconButton size='small' onClick={onCopyRow} disabled={rowSelectionModel.length !== 1} sx={buttonSx}>
                <AritoIcon icon={11} />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title='Dán vào dòng' arrow>
            <span>
              <IconButton size='small' onClick={onPasteRow} disabled={rowSelectionModel.length !== 1} sx={buttonSx}>
                <AritoIcon icon={4} />
              </IconButton>
            </span>
          </Tooltip>
        </div>

        <Divider orientation='vertical' flexItem sx={{ mx: 1 }} />

        <div className='flex items-center'>
          <Tooltip title='Di chuyển lên' arrow>
            <span>
              <IconButton
                size='small'
                onClick={() => onMoveRow('up')}
                disabled={rowSelectionModel.length !== 1}
                sx={buttonSx}
              >
                <AritoIcon icon={3} />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title='Di chuyển xuống' arrow>
            <span>
              <IconButton
                size='small'
                onClick={() => onMoveRow('down')}
                disabled={rowSelectionModel.length !== 1}
                sx={buttonSx}
              >
                <AritoIcon icon={30} />
              </IconButton>
            </span>
          </Tooltip>
        </div>

        <Divider orientation='vertical' flexItem sx={{ mx: 1 }} />

        <Tooltip title='Kết xuất dữ liệu' arrow>
          <span>
            <IconButton size='small' onClick={() => {}} disabled={rowSelectionModel.length === 0} sx={buttonSx}>
              <AritoIcon icon={18} />
            </IconButton>
          </span>
        </Tooltip>

        <Tooltip title='Cố định cột' arrow>
          <span>
            <IconButton size='small' onClick={() => {}} disabled={rowSelectionModel.length === 0} sx={buttonSx}>
              <AritoIcon icon={16} />
            </IconButton>
          </span>
        </Tooltip>

        <Divider orientation='vertical' flexItem sx={{ mx: 1 }} />
      </div>

      {errorMessage && <div className='ml-2 text-xs font-medium text-red-500'>{errorMessage}</div>}
    </div>
  );
}

export function ViewModeButtons() {
  return (
    <div className='flex items-center gap-0.5'>
      <Tooltip title='Kết xuất dữ liệu' arrow>
        <Button size='small' variant='text' sx={buttonSx}>
          <AritoIcon icon={18} marginX='0.5rem' />
          Kết xuất dữ liệu
        </Button>
      </Tooltip>
      <Tooltip title='Cố định cột' arrow>
        <Button size='small' variant='text' sx={buttonSx}>
          <AritoIcon icon={16} marginX='0.5rem' />
          Cố định cột
        </Button>
      </Tooltip>
    </div>
  );
}
