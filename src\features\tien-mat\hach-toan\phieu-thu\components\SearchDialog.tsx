import { Button } from '@mui/material';
import { z } from 'zod';
import { <PERSON>Field } from '@/components/custom/arito/form/form-field';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { Label } from '@/components/ui/label';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

// Define the schema for the search form
const searchSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  voucherNumberStart: z.string().optional(),
  voucherNumberEnd: z.string().optional(),
  voucherType: z.string().optional(),
  debitAccount: z.string().optional(),
  description: z.string().optional(),
  customerCode: z.string().optional(),
  creditAccount: z.string().optional(),
  unit: z.string().optional(),
  status: z.string().optional(),
  sortBy: z.string().optional()
});

type SearchFormValues = z.infer<typeof searchSchema>;

export const SearchDialog = ({ open, onClose, onSearch }: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc phiếu thu tiền'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      actions={
        <>
          <Button
            type='submit'
            form='search-form'
            variant='contained'
            sx={{
              borderRadius: 0,
              backgroundColor: '#1976d2',
              '&:hover': { backgroundColor: '#1565c0' }
            }}
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button
            onClick={onClose}
            variant='outlined'
            sx={{
              borderRadius: 0,
              borderColor: '#d32f2f',
              color: '#d32f2f',
              '&:hover': {
                borderColor: '#c62828',
                backgroundColor: 'rgba(211, 47, 47, 0.04)'
              }
            }}
          >
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <div className='space-y-2'>
              <div className='p-3'>
                <div className='flex items-center'>
                  <Label className='mt-3 flex items-center pr-2 text-left text-[13px] text-sm font-normal sm:mb-0'>
                    Ngày c/từ (từ/đến)
                  </Label>
                  <div className='flex flex-1 gap-2'>
                    <FormField name='startDate' type='date' className='w-full' label='' />
                    <FormField name='endDate' type='date' className='w-full' label='' />
                  </div>
                </div>

                <div className='flex items-center'>
                  <Label className='mt-3 flex w-[120px] items-center pr-2 text-left text-[13px] text-sm font-normal sm:mb-0'>
                    Số c/từ (từ/đến)
                  </Label>
                  <div className='flex flex-1 gap-2'>
                    <FormField name='voucherNumberStart' type='text' className='w-full' label='' />
                    <FormField name='voucherNumberEnd' type='text' className='w-full' label='' />
                  </div>
                </div>
                <FormField
                  name='voucherType'
                  type='select'
                  label='Loại xuất'
                  options={[
                    { value: 'Tất cả', label: 'Tất cả' },
                    { value: 'Thu theo hóa đơn', label: '1. Thu theo hóa đơn' },
                    {
                      value: 'Thu theo đối tượng',
                      label: '2. Thu theo đối tượng'
                    },
                    { value: 'Thu khác', label: '3. Thu khác' },
                    { value: 'Rút về nhập quỹ', label: '4. Rút về nhập quỹ' },
                    {
                      value: 'Bán hàng thu tiền ngày',
                      label: '5. Bán hàng thu tiền ngày'
                    }
                  ]}
                  className='w-full'
                  labelClassName='w-[120px]'
                />
                <FormField
                  name='debitAccount'
                  type='text'
                  label='Tài khoản nợ'
                  withSearch
                  searchEndpoint='/api/accounts'
                  searchResultLabelKey='accountName'
                  searchResultValueKey='accountCode'
                  className='w-full'
                  labelClassName='w-[120px]'
                />

                <FormField
                  name='description'
                  type='text'
                  className='w-full'
                  label='Diễn giải'
                  labelClassName='w-[120px]'
                />
              </div>

              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'customer',
                    label: 'Chi tiết',
                    component: (
                      <div className='p-3'>
                        <FormField
                          name='customerCode'
                          type='text'
                          label='Mã khách hàng'
                          withSearch
                          searchEndpoint='/api/customers'
                          searchResultLabelKey='customerName'
                          searchResultValueKey='customerCode'
                          className='w-full'
                          labelClassName='w-[120px]'
                        />

                        <FormField
                          name='creditAccount'
                          type='text'
                          label='Tài khoản có'
                          withSearch
                          searchEndpoint='/api/accounts'
                          searchResultLabelKey='accountName'
                          searchResultValueKey='accountCode'
                          className='w-full'
                          labelClassName='w-[120px]'
                        />
                      </div>
                    )
                  }
                ]}
              />

              <div className='border-t border-t-gray-200 p-3'>
                <FormField
                  name='unit'
                  type='text'
                  label='Đơn vị'
                  withSearch
                  searchEndpoint='/api/units'
                  searchResultLabelKey='unitName'
                  searchResultValueKey='unitCode'
                  className='w-full'
                  labelClassName='w-[120px]'
                />
                <FormField
                  label='Trạng thái'
                  name='status'
                  type='select'
                  options={[
                    { value: 'Tất cả', label: 'Tất cả' },
                    { value: 'Chưa ghi sổ', label: 'Chưa ghi sổ' },
                    { value: 'Chờ duyệt', label: 'Chờ duyệt' },
                    { value: 'Đã ghi sổ', label: 'Đã ghi sổ' },
                    { value: 'Đang thực hiện', label: 'Đang thực hiện' },
                    { value: 'Hoàn thành', label: 'Hoàn thành' },
                    { value: 'Nhập quỹ', label: 'Nhập quỹ' },
                    { value: 'Hủy', label: 'Hủy' }
                  ]}
                  className='w-full'
                  labelClassName='w-[120px]'
                />
                <FormField
                  label={'Lọc theo người sd'}
                  labelClassName='w-[120px]'
                  name='sortBy'
                  type='select'
                  options={[
                    { value: 'Tất cả', label: 'Tất cả' },
                    {
                      value: 'Lọc theo người tạo',
                      label: 'Lọc theo người tạo'
                    }
                  ]}
                />
              </div>
            </div>
          </div>
        }
      />
    </AritoDialog>
  );
};
