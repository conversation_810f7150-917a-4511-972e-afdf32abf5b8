// TypeScript interfaces for BaoCaoSoSanhBanHangHaiKy (Sales Comparison Report Between Two Periods)

export interface BaoCaoSoSanhBanHangHaiKyItem {
  id: string;
  ma_khach_hang: string;
  ten_khach_hang: string;
  ma_san_pham?: string;
  ten_san_pham?: string;
  nhom_khach_hang_1?: string;
  nhom_khach_hang_2?: string;
  nhom_san_pham_1?: string;
  nhom_san_pham_2?: string;

  // Current period data (Kỳ này)
  so_luong_ky_nay: number;
  gia_ban_ky_nay: number;
  doanh_so_ky_nay: number;
  thue_ky_nay: number;
  chiet_khau_ky_nay: number;
  doanh_thu_ky_nay: number;
  gia_von_ky_nay: number;
  tien_von_ky_nay: number;
  lai_ky_nay: number;

  // Previous period data (Kỳ trước)
  so_luong_ky_truoc: number;
  gia_ban_ky_truoc: number;
  doanh_so_ky_truoc: number;
  thue_ky_truoc: number;
  chiet_khau_ky_truoc: number;
  doanh_thu_ky_truoc: number;
  gia_von_ky_truoc: number;
  tien_von_ky_truoc: number;
  lai_ky_truoc: number;

  // Comparison data (So sánh)
  chenh_lech_so_luong: number;
  ty_le_so_luong: number;
  chenh_lech_doanh_so: number;
  ty_le_doanh_so: number;
  chenh_lech_doanh_thu: number;
  ty_le_doanh_thu: number;
  chenh_lech_lai: number;
  ty_le_lai: number;

  // Additional fields
  ma_nhan_vien?: string;
  ten_nhan_vien?: string;
  ma_kho?: string;
  ten_kho?: string;
  ma_don_vi?: string;
  ten_don_vi?: string;
  khu_vuc?: string;
}

export interface BaoCaoSoSanhBanHangHaiKyResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoSoSanhBanHangHaiKyItem[];
}

export interface BaoCaoSoSanhBanHangHaiKySearchFormValues {
  fromDate: string;
  toDate: string;
  previousFromDate: string;
  previousToDate: string;
  reportBy: string;
  groupBy: string;
  customerCode?: string;
  customerGroup1?: string;
  customerGroup2?: string;
  customerGroup3?: string;
  region?: string;
  productCode?: string;
  productType?: string;
  trackInventory?: boolean;
  productGroup1?: string;
  productGroup2?: string;
  productGroup3?: string;
  warehouseCode?: string;
  reportTemplate?: string;
  department?: string;
  project?: string;
  contract?: string;
  paymentPhase?: string;
  agreement?: string;
  fee?: string;
  product?: string;
  productionOrder?: string;
  invalidExpense?: string;
  transactionCode?: string;
  employeeCode?: string;
  itemAccount?: string;
  revenueAccount?: string;
  costAccount?: string;
  batchCode?: string;
  locationCode?: string;
  fromDocumentNumber?: string;
  toDocumentNumber?: string;
  description?: string;
  reportFilterTemplate?: string;
  [key: string]: any;
}

export interface UseBaoCaoSoSanhBanHangHaiKyReturn {
  data: BaoCaoSoSanhBanHangHaiKyItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoSoSanhBanHangHaiKySearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
