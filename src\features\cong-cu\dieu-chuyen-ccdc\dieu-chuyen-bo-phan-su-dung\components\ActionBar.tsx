import { Lock, Pencil, Plus, Refresh<PERSON>w, Trash, Copy } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onFixedColumnsClick?: () => void;
  onRefreshClick?: () => void;
  onExportClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onFixedColumnsClick,
  onRefresh<PERSON>lick,
  onExportClick
}) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Điều chuyển bộ phận sử dụng</h1>}>
    {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />}
    {onCopyClick && <AritoActionButton title='Copy' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Lock} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: onExportClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
