import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  reportDate: z.string().optional(),
  payDate: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
  accountId: z.string().optional(),

  // General tab
  customerCode: z.string().optional(),
  customerGroup1: z.string().optional(),
  customerGroup2: z.string().optional(),
  customerGroup3: z.string().optional(),
  region: z.string().optional(),
  detail: z.string().optional(),
  balance: z.string().optional(),
  numberOfTtDays: z.string().optional(),
  numberOfWarningDays: z.string().optional(),

  // Other tab
  fromDocumentNumber: z.string().optional(),
  toDocumentNumber: z.string().optional(),
  reportFilterTemplate: z.string().optional(),
  reportTemplate: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  reportDate: '',
  payDate: '',
  fromDate: '',
  toDate: '',
  accountId: '',

  // General tab
  customerCode: '',
  customerGroup1: '',
  customerGroup2: '',
  customerGroup3: '',
  region: '',
  detail: '',
  balance: '',
  numberOfTtDays: '',
  numberOfWarningDays: '0',

  // Other tab
  fromDocumentNumber: '',
  toDocumentNumber: '',
  reportFilterTemplate: '',
  reportTemplate: ''
};
