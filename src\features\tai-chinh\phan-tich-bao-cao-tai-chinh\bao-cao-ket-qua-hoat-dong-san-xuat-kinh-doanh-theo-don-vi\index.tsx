'use client';

import React from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import ActionBar from './components/ActionBar';
import { useReportData } from './hooks';

export default function BusinessReportByUnit() {
  const {
    initialSearchDialogOpen,
    showTable,
    searchParams,
    tables,
    handleRowClick,
    isLoading,
    error,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  } = useReportData();

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />
      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            searchParams={searchParams}
            className='border-b border-gray-200'
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} />
          </div>
        </>
      )}
    </div>
  );
}
