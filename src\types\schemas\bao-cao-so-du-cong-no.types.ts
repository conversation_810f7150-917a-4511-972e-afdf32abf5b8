// Types for the debt balance report

export interface BaoCaoSoDuCongNoItem {
  uuid?: string;
  customer_code: string;
  customer_name: string;
  du_no?: number;
  du_co?: number;
}

export interface BaoCaoSoDuCongNoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoSoDuCongNoItem[];
}

export interface BaoCaoSoDuCongNoSearchFormValues {
  date?: string;
  accountId?: string;
  dataType?: 'begin' | 'end';
  customerCode?: string;
  customerGroup1?: string;
  customerGroup2?: string;
  customerGroup3?: string;
  region?: string;
  groupBy?: string[];
  reportTemplate?: 'quantity' | 'quantityAndValue' | 'quantityAndForeignValue';
  [key: string]: any;
}

export interface UseBaoCaoSoDuCongNoReturn {
  data: BaoCaoSoDuCongNoItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoSoDuCongNoSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
