import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { CCDCInventoryItem } from '@/types/schemas';
import { ccdcColumns } from '../cols-definition';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: CCDCInventoryItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const dataRows = data.map(item => ({
      id: item.id,
      stt: item.stt,
      ma_cc: item.ma_cc,
      ten_cc: item.ten_cc,
      nguyen_gia: item.nguyen_gia,
      gt_da_kh_nt0: item.gt_da_kh_nt0,
      gt_cl: item.gt_cl,
      gt_kh_ky: item.gt_kh_ky,
      ngay_mua: item.ngay_mua,
      ngay_kh0: item.ngay_kh0,
      so_ky_kh: item.so_ky_kh,
      ngay_ct: item.ngay_ct,
      so_ct: item.so_ct,
      tk_cc: item.tk_cc,
      tk_kh: item.tk_kh,
      tk_cp: item.tk_cp,
      ma_bp: item.ma_bp,
      nh_cc1: item.nh_cc1,
      nh_cc2: item.nh_cc2,
      nh_cc3: item.nh_cc3,
      ten_bp: item.ten_bp,
      loai_cc: item.loai_cc,
      trang_thai: item.trang_thai,
      ma_nt: item.ma_nt,
      ty_gia: item.ty_gia,
      nguyen_gia_nt: item.nguyen_gia_nt,
      gt_cl_nt: item.gt_cl_nt,
      gt_kh_ky_nt: item.gt_kh_ky_nt
    }));

    const totalRow = {
      id: 'total',
      isTotalRow: true,
      stt: '',
      ma_cc: '',
      ten_cc: 'Tổng cộng',
      nguyen_gia: '',
      gt_da_kh_nt0: '',
      gt_cl: '',
      gt_kh_ky: '',
      ngay_mua: '',
      ngay_kh0: '',
      so_ky_kh: '',
      ngay_ct: '',
      so_ct: '',
      tk_cc: '',
      tk_kh: '',
      tk_cp: '',
      ma_bp: '',
      nh_cc1: '',
      nh_cc2: '',
      nh_cc3: '',
      ten_bp: '',
      loai_cc: '',
      trang_thai: '',
      ma_nt: '',
      ty_gia: '',
      nguyen_gia_nt: '',
      gt_cl_nt: '',
      gt_kh_ky_nt: ''
    };

    const tableData: TableData[] = [
      {
        name: '',
        columns: ccdcColumns,
        rows: [...dataRows, totalRow]
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
