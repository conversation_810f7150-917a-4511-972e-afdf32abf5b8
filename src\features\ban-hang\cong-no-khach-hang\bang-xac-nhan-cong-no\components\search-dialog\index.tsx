'use client';

import { useState } from 'react';
import { AritoDialog, AritoForm, BottomBar, AritoIcon } from '@/components/custom/arito';
import { SearchFormValues, SearchFormSchema } from '../../schema';
import { AccountModel, DoiTuong } from '@/types/schemas';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: SearchFormValues;
  onSubmit?: (data: SearchFormValues) => void;
  onClose: () => void;
}

const SearchDialog = ({ open, onClose, onSubmit, formMode, initialData }: SearchDialogProps) => {
  const [taiKhoan, setTaiKhoan] = useState<AccountModel | null>(null);
  const [khachHang, setKhachHang] = useState<DoiTuong | null>(null);

  const handleSubmit = (data: SearchFormValues) => {
    const searchData = {
      ...data,
      ma_tk: taiKhoan?.uuid || '',
      ma_kh: khachHang?.uuid || ''
    };
    onSubmit?.(searchData);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Bảng xác nhận công nợ'}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={SearchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[900px]'
        headerFields={<BasicInfoTab taiKhoan={taiKhoan} setTaiKhoan={setTaiKhoan} />}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab khachHang={khachHang} setKhachHang={setKhachHang} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab />
          }
        ]}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode={formMode} onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
