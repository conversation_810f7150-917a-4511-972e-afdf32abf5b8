import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const supplierSearchColumns: GridColDef[] = [
  { field: 'customer_code', headerName: 'Mã NCC', width: 120 },
  { field: 'customer_name', headerName: 'Tên nhà cung cấp', width: 250 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', width: 120 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', width: 120 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 120 },
  { field: 'phone', headerName: 'Điện thoại', width: 120 },
  { field: 'email', headerName: 'Email', width: 180 }
];

export const supplierGroupSearchColumns: GridColDef[] = [
  { field: 'supplierGroupCode', headerName: 'Mã nhóm', width: 120 },
  { field: 'supplierGroupName', headerName: 'Tên nhóm', width: 200 }
];

export const regionSearchColumns: GridColDef[] = [
  { field: 'rg_code', headerName: 'Mã khu vực', width: 120 },
  { field: 'rgname', headerName: 'Tên khu vực', width: 200 }
];

export const accountSearchColumns: GridColDef[] = [
  {
    field: 'code',
    headerName: 'Mã tài khoản',
    width: 150,
    editable: false
  },
  {
    field: 'name',
    headerName: 'Tên tài khoản',
    width: 200,
    flex: 1,
    editable: false
  },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    width: 150,
    editable: false,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.parent_account_code_data?.code || '';
    }
  },
  {
    field: 'is_parent_account',
    headerName: 'TK sổ cái',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'TK chi tiết',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'bac_tk',
    headerName: 'Bậc TK',
    width: 100,
    editable: false,
    type: 'number'
  }
];
