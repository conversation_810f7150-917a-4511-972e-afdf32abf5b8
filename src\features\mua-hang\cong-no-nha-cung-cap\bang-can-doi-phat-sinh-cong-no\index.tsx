'use client';

import { useState } from 'react';
import { useBangCanDoiPhatSinhCongNo, useDialogState, useTableData } from './hooks';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import InitialSearchDialog from './components/InitialSearchDialog';
import { ActionBar } from './components';

export default function BangCanDoiPhatSinhCongNoPage() {
  const {
    initialSearchDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick
  } = useDialogState();

  const [dates, setDates] = useState<{ fromDate: string; toDate: string } | null>(null);
  const [searchParams, setSearchParams] = useState<any>({});
  const [searchFieldStates, setSearchFieldStates] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useBangCanDoiPhatSinhCongNo(searchParams);
  const { tables, handleRowClick } = useTableData(data);

  const handleSearchWithData = async (values: any, accountData?: any) => {
    setSearchParams(values);
    setSearchFieldStates({ account: accountData });
    handleInitialSearch(values);
    await fetchData(values);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
        setDates={(fromDate, toDate) => setDates({ fromDate, toDate })}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={() => {}}
            onExportDataClick={() => {}}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={{
              fromDate: searchParams.fromDate,
              toDate: searchParams.toDate,
              ma_tk: searchParams.ma_tk,
              account: searchFieldStates.account
            }}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
