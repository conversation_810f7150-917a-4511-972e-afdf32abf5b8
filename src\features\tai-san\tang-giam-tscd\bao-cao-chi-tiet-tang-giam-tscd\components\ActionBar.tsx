import { Pin, RefreshCw, Search, Sheet } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  subTitle?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  subTitle
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='my-1.5 text-xl font-bold'>Báo cáo chi tiết tăng/giảm TSCĐ</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>{subTitle}</span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}

      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='secondary' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
