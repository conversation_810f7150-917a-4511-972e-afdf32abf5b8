import { FileSearch, RefreshCw, Lock, FileDown } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onEditPrintTemplateClick
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Bảng tính khấu hao tài sản cố định theo tháng (Mẫu chuẩn)</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <div className='mr-2 size-2 rounded-full bg-red-500' />
          Từ kỳ {new Date().getMonth()} năm {new Date().getFullYear()} đến kỳ {new Date().getMonth() + 2} năm{' '}
          {new Date().getFullYear()}
        </span>
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={FileSearch} onClick={onSearchClick} />}
    {onRefreshClick && <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />}
    {onFixedColumnsClick && <AritoActionButton title='Cố định cột' icon={Lock} onClick={onFixedColumnsClick} />}
    {onExportClick && <AritoActionButton title='Kết xuất dữ liệu' icon={FileDown} onClick={onExportClick} />}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
