import React, { useState, useEffect } from 'react';
import { GridRowParams } from '@mui/x-data-grid';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useBusinessReportData } from './useBusinessReportData';
import { SearchFormValues } from '../schema';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData } = useBusinessReportData(searchParams);

  const handleRowClick = (params: GridRowParams) => {
    const reportItem = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
    console.log('Row clicked:', reportItem);
  };

  const tables = [
    {
      name: '<PERSON><PERSON>o c<PERSON>o kết quả hoạt động sản xuất kinh doanh theo đơn vị',
      rows: data.map(item => ({
        id: item.id,
        Stt: item.stt,
        'Chỉ tiêu': item.chi_tieu,
        'Thuyết minh': item.thuyet_minh,
        Tiền: item.tien
      })),
      columns: [
        { field: 'Stt', headerName: 'Mã số', width: 100 },
        { field: 'Chỉ tiêu', headerName: 'Chỉ tiêu', width: 350 },
        { field: 'Thuyết minh', headerName: 'Thuyết minh', width: 150 },
        { field: 'Tiền', headerName: 'Tiền', width: 150, type: 'number' as const }
      ]
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
