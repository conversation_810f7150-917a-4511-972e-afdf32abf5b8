'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { BangHachToanCCDCSearchFormValues } from '@/types/schemas/bang-hach-toan-ccdc.type';
import { ActionBar, SearchDialog, EditPrintTemplateDialog } from './components';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { getDataTableColumns, getTotalRow } from './cols-definition';
import { useBangHachToanCCDC } from './hooks';
import { SearchFormValues } from './schemas';

export default function BangHoachToanPhanBoCCDC() {
  const [showTables, setShowTables] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);

  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useState<BangHachToanCCDCSearchFormValues>({});

  const { data, isLoading, fetchData, refreshData } = useBangHachToanCCDC(searchParams);
  const [dates, setDates] = useState<{
    fromDate: {
      tu_ki: string;
      tu_nam: string;
    };
    toDate: {
      den_ki: string;
      den_nam: string;
    };
  }>({
    fromDate: {
      tu_ki: `${new Date().getMonth() + 1}`,
      tu_nam: `${new Date().getFullYear()}`
    },
    toDate: {
      den_ki: `${new Date().getMonth() + 1}`,
      den_nam: `${new Date().getFullYear()}`
    }
  });

  const handleRowClick = (params: GridRowParams) => {
    if (params.row.id === 'total') return;

    const reportItem = params.row as any;
    setSelectedObj(reportItem);
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchSubmit = async (data: SearchFormValues) => {
    setShowSearchDialog(false);
    setShowTables(true);

    // Convert SearchFormValues to BangHachToanCCDCSearchFormValues
    const searchData: BangHachToanCCDCSearchFormValues = {
      tu_ky: data.tu_ky,
      tu_nam: data.tu_nam,
      den_ky: data.den_ky,
      den_nam: data.den_nam,
      mau_bc: data.mau_bc,
      data_analysis_struct: data.data_analysis_struct
    };

    setSearchParams(searchData);
    await fetchData(searchData);

    setDates({
      fromDate: {
        tu_ki: data.tu_ky?.toString() || '',
        tu_nam: data.tu_nam?.toString() || ''
      },
      toDate: {
        den_ki: data.den_ky?.toString() || '',
        den_nam: data.den_nam?.toString() || ''
      }
    });
  };

  const handleRefresh = async () => {
    await refreshData();
  };

  const handleFixedColumns = () => {
    // Implement fixed columns functionality here
  };

  const handleExportData = () => {
    // Implement export data functionality here
  };

  const handleEditPrintTemplate = () => {
    setShowEditPrintTemplateDialog(true);
  };

  // Convert mock data to table rows format
  const tableRows = data.map(item => ({
    id: item.id,
    stt: item.stt,
    ma_cc: item.ma_cc,
    ten_cc: item.ten_cc,
    tk_kh: item.tk_kh,
    tk_cp: item.tk_cp,
    tien: item.tien,
    ma_bp: item.ma_bp,
    ma_vv: item.ma_vv,
    ma_phi: item.ma_phi
  }));

  // Calculate total row with sum of amounts
  const totalAmount = data.reduce((sum, item) => sum + item.tien, 0);
  const totalRow = {
    ...getTotalRow(tableRows),
    tien: totalAmount
  };

  const tables = [
    {
      name: '',
      rows: [...tableRows, totalRow],
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={() => setShowSearchDialog(false)}
          onSearch={handleSearchSubmit}
        />
      )}

      {showEditPrintTemplateDialog && (
        <EditPrintTemplateDialog
          open={showEditPrintTemplateDialog}
          onClose={() => setShowEditPrintTemplateDialog(false)}
        />
      )}

      {showTables && (
        <div className='w-full'>
          <ActionBar
            onSearch={handleSearch}
            onRefresh={handleRefresh}
            onExportData={handleExportData}
            onFixedColumns={handleFixedColumns}
            onEditPrintTemplate={handleEditPrintTemplate}
            tu_ky={dates.fromDate.tu_ki.toString()}
            den_ky={dates.toDate.den_ki.toString()}
            tu_nam={dates.fromDate.tu_nam.toString()}
            den_nam={dates.toDate.den_nam.toString()}
          />
          <div className='relative flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && (
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}
