import { useState } from 'react';
import { SearchFormValues } from '../types';

export function useDialogState() {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues>({
    from_date: '2025-05-01',
    time_type: 2, // month
    period: 4
  });

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (params: SearchFormValues) => {
    setSearchParams(params);
    setInitialSearchDialogOpen(false);
    setShowTable(true);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    setEditPrintTemplateDialogOpen(true);
  };

  const handleClosePrintTemplateDialog = () => {
    setEditPrintTemplateDialogOpen(false);
  };

  const handleSavePrintTemplate = (data: any) => {
    console.log('Save print template:', data);
    setEditPrintTemplateDialogOpen(false);
  };

  return {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  };
}
