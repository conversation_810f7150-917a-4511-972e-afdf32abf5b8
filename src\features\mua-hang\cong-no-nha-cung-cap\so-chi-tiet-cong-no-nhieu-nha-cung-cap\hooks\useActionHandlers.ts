export interface UseActionHandlersReturn {
  handleRefreshClick: () => void;
  handleFixedColumnsClick: () => void;
  handleExportDataClick: () => void;
  handlePrintClick: () => void;
}

export function useActionHandlers(refreshData?: () => Promise<void>): UseActionHandlersReturn {
  const handleRefreshClick = () => {
    refreshData?.();
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
  };

  const handleExportDataClick = () => {
    console.log('Export data clicked');
  };

  const handlePrintClick = () => {
    console.log('Print clicked');
  };

  return {
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick,
    handlePrintClick
  };
}
