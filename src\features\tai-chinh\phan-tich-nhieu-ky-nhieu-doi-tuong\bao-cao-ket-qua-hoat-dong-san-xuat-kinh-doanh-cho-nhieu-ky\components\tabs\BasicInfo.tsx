import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Từ ngày:</Label>
          <div className='w-48'>
            <FormField name='reportDate' type='date' label='' className='w-full' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại thời gian:</Label>
          <div className='w-48'>
            <FormField
              name='balance'
              label=''
              type='select'
              options={[
                { value: 'day', label: 'Ngày' },
                { value: 'week', label: 'Tuần' },
                { value: 'month', label: 'Tháng' },
                { value: 'quarter', label: 'Quý' },
                { value: 'halfYear', label: 'Nửa năm' },
                { value: 'year', label: 'Năm' }
              ]}
              defaultValue='month'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số kỳ:</Label>
          <div className='w-48'>
            <FormField name='numberOfTtDays' label='' type='number' defaultValue={12} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
