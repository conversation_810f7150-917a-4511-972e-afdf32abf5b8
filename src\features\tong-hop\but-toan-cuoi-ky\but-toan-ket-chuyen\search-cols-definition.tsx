import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account_name', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 2 },
  { field: 'ledger_account', headerName: 'Tài khoản sổ cái', flex: 2 },
  { field: 'is_group', headerName: 'Tài khoản chi tiết', checkboxSelection: true, flex: 2 },
  { field: 'account_level', headerName: 'Bậc tài khoản', flex: 2 }
];

export const departmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'department_name', headerName: 'Tê<PERSON> bộ phận', flex: 2 }
];

export const caseSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã vụ việc', flex: 1 },
  { field: 'case_name', headerName: 'Tên vụ việc', flex: 2 }
];

export const feeCodeSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã phí', flex: 1 },
  { field: 'fee_code_name', headerName: 'Tên phí', flex: 2 }
];

export const contractSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã hợp đồng', flex: 1 },
  { field: 'contract_name', headerName: 'Tên hợp đồng', flex: 2 }
];

export const currencySearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã ngoại tệ', flex: 1 },
  { field: 'currency_name', headerName: 'Tên ngoại tệ', flex: 2 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'unit_name', headerName: 'Tên đơn vị', flex: 2 }
];
