import { GridColDef } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const detailPaymentsColumns: GridColDef[] = [
  { field: 'ngay_ct', headerName: 'Ng<PERSON>y c/từ', width: 150 },
  { field: 'so_ct', headerName: 'Số c/từ', width: 150 },
  { field: 'ngay_ct0', headerName: 'Ngày hoá đơn', width: 150 },
  { field: 'so_ct0', headerName: 'Số hóa đơn', width: 150 },
  { field: 'ma_kh', headerName: 'Mã nhà cung cấp', width: 150 },
  { field: 'ten_kh', headerName: 'Tên nhà cung cấp', width: 150 },
  { field: 'dien_giai', headerName: '<PERSON>ễn giải', width: 150 },
  { field: 't_tt', headerName: 'Tổng tiền', width: 150 },
  { field: 'tt', headerName: '<PERSON><PERSON> thanh to<PERSON>', width: 150 },
  { field: 'cl', headerName: '<PERSON><PERSON>n lại', width: 150 },
  {
    field: 'tt_yn',
    headerName: 'Tất toán',
    width: 100,
    type: 'boolean',
    renderCell: (params: any) => <Checkbox checked={params.row.tt_yn} disabled />
  },
  { field: 'ma_ct', headerName: 'Mã c/từ', width: 150 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 150 }
];
