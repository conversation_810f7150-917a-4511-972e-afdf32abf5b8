import { useState, useCallback } from 'react';
import {
  BangXacNhanCongNoItem,
  BangXacNhanCongNoResponse,
  BangXacNhanCongNoSearchFormValues,
  UseBangXacNhanCongNoReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BangXacNhanCongNoItem[] => {
  return [
    {
      id: '1',
      ma_unit: 'CN',
      ngay_ct: '2024-01-05',
      so_ct: 'XN001',
      tk: '331',
      tk_du: '131',
      dien_giai: '<PERSON>ác nhận công nợ nhà cung cấp ABC Technology',
      ps_no: 50000000,
      ps_co: 0,
      du_no: 50000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '2',
      ma_unit: 'CN',
      ngay_ct: '2024-02-10',
      so_ct: 'XN002',
      tk: '331',
      tk_du: '131',
      dien_giai: '<PERSON><PERSON><PERSON> nhận công nợ nhà cung cấp XYZ Solutions',
      ps_no: 75000000,
      ps_co: 0,
      du_no: 75000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '3',
      ma_unit: 'CN',
      ngay_ct: '2024-03-15',
      so_ct: 'XN003',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp DEF Industries',
      ps_no: 30000000,
      ps_co: 0,
      du_no: 30000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '4',
      ma_unit: 'CN',
      ngay_ct: '2024-06-20',
      so_ct: 'XN004',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp GHI Corp',
      ps_no: 0,
      ps_co: 25000000,
      du_no: 0,
      du_co: 25000000,
      ma_ct: 'XN'
    },
    {
      id: '5',
      ma_unit: 'CN',
      ngay_ct: '2024-09-25',
      so_ct: 'XN005',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp JKL Enterprises',
      ps_no: 100000000,
      ps_co: 0,
      du_no: 100000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '6',
      ma_unit: 'CN',
      ngay_ct: '2024-04-12',
      so_ct: 'XN006',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp MNO Trading',
      ps_no: 45000000,
      ps_co: 0,
      du_no: 45000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '7',
      ma_unit: 'CN',
      ngay_ct: '2024-07-08',
      so_ct: 'XN007',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp PQR Logistics',
      ps_no: 0,
      ps_co: 35000000,
      du_no: 0,
      du_co: 35000000,
      ma_ct: 'XN'
    },
    {
      id: '8',
      ma_unit: 'CN',
      ngay_ct: '2024-11-30',
      so_ct: 'XN008',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp STU Manufacturing',
      ps_no: 85000000,
      ps_co: 0,
      du_no: 85000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '9',
      ma_unit: 'CN',
      ngay_ct: '2024-08-14',
      so_ct: 'XN009',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp VWX Services',
      ps_no: 60000000,
      ps_co: 0,
      du_no: 60000000,
      du_co: 0,
      ma_ct: 'XN'
    },
    {
      id: '10',
      ma_unit: 'CN',
      ngay_ct: '2024-12-15',
      so_ct: 'XN010',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Xác nhận công nợ nhà cung cấp YZ Global',
      ps_no: 0,
      ps_co: 40000000,
      du_no: 0,
      du_co: 40000000,
      ma_ct: 'XN'
    }
  ];
};

/**
 * Custom hook for managing BangXacNhanCongNo (Debt Confirmation Statement) data
 *
 * This hook provides functionality to fetch debt confirmation statement data
 * with mock support for testing and development purposes.
 */
export function useBangXacNhanCongNo(searchParams: BangXacNhanCongNoSearchFormValues): UseBangXacNhanCongNoReturn {
  const [data, setData] = useState<BangXacNhanCongNoItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangXacNhanCongNoSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangXacNhanCongNoResponse>(
        '/mua-hang/cong-no-nha-cung-cap/bang-xac-nhan-cong-no/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
