import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export interface FixedAssetItem {
  id: string;
  stt: number;
  ma_tai_san: string;
  ten_tai_san: string;
  nguyen_gia: number;
  gt_khau_hao: number;
  con_lai: number;
  kh_trong_ky: number;
  ngay_mua: Date;
  ngay_tinh_kh: Date;
  so_ky_khau_hao: number;
  ngay_ct: Date;
  so_ct: string;
  tk_tai_san: string;
  tk_khau_hao: string;
  tk_chi_phi: string;
  ma_bo_phan: string;
  nhom_tai_san_1: string;
  nhom_tai_san_2: string;
  nhom_tai_san_3: string;
}

export interface FixedAssetResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: FixedAssetItem[];
}

export interface UseFixedAssetReturn {
  data: FixedAssetItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): FixedAssetItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_tai_san: 'TS001',
      ten_tai_san: 'Máy tính Dell Inspiron 15',
      nguyen_gia: 15000000,
      gt_khau_hao: 12000000,
      con_lai: 3000000,
      kh_trong_ky: 1500000,
      ngay_mua: new Date(2022, 0, 1),
      ngay_tinh_kh: new Date(2022, 1, 1),
      so_ky_khau_hao: 36,
      ngay_ct: new Date(2022, 0, 1),
      so_ct: 'CT001',
      tk_tai_san: '211',
      tk_khau_hao: '214',
      tk_chi_phi: '642',
      ma_bo_phan: 'IT001',
      nhom_tai_san_1: 'Thiết bị văn phòng',
      nhom_tai_san_2: 'Máy tính',
      nhom_tai_san_3: 'Laptop'
    },
    {
      id: '2',
      stt: 2,
      ma_tai_san: 'TS002',
      ten_tai_san: 'Máy in HP LaserJet Pro',
      nguyen_gia: 8000000,
      gt_khau_hao: 6400000,
      con_lai: 1600000,
      kh_trong_ky: 800000,
      ngay_mua: new Date(2022, 2, 15),
      ngay_tinh_kh: new Date(2022, 3, 1),
      so_ky_khau_hao: 60,
      ngay_ct: new Date(2022, 2, 15),
      so_ct: 'CT002',
      tk_tai_san: '211',
      tk_khau_hao: '214',
      tk_chi_phi: '642',
      ma_bo_phan: 'IT001',
      nhom_tai_san_1: 'Thiết bị văn phòng',
      nhom_tai_san_2: 'Máy in',
      nhom_tai_san_3: 'Laser'
    },
    {
      id: '3',
      stt: 3,
      ma_tai_san: 'TS003',
      ten_tai_san: 'Xe ô tô Toyota Camry',
      nguyen_gia: 800000000,
      gt_khau_hao: 400000000,
      con_lai: 400000000,
      kh_trong_ky: 80000000,
      ngay_mua: new Date(2021, 5, 10),
      ngay_tinh_kh: new Date(2021, 6, 1),
      so_ky_khau_hao: 120,
      ngay_ct: new Date(2021, 5, 10),
      so_ct: 'CT003',
      tk_tai_san: '211',
      tk_khau_hao: '214',
      tk_chi_phi: '642',
      ma_bo_phan: 'VT001',
      nhom_tai_san_1: 'Phương tiện vận tải',
      nhom_tai_san_2: 'Ô tô',
      nhom_tai_san_3: 'Sedan'
    },
    {
      id: '4',
      stt: 4,
      ma_tai_san: 'TS004',
      ten_tai_san: 'Máy điều hòa Daikin 2HP',
      nguyen_gia: 12000000,
      gt_khau_hao: 8000000,
      con_lai: 4000000,
      kh_trong_ky: 1200000,
      ngay_mua: new Date(2021, 7, 20),
      ngay_tinh_kh: new Date(2021, 8, 1),
      so_ky_khau_hao: 120,
      ngay_ct: new Date(2021, 7, 20),
      so_ct: 'CT004',
      tk_tai_san: '211',
      tk_khau_hao: '214',
      tk_chi_phi: '642',
      ma_bo_phan: 'HC001',
      nhom_tai_san_1: 'Thiết bị văn phòng',
      nhom_tai_san_2: 'Điều hòa',
      nhom_tai_san_3: 'Split'
    },
    {
      id: '5',
      stt: 5,
      ma_tai_san: 'TS005',
      ten_tai_san: 'Bàn làm việc gỗ cao cấp',
      nguyen_gia: 5000000,
      gt_khau_hao: 3000000,
      con_lai: 2000000,
      kh_trong_ky: 500000,
      ngay_mua: new Date(2020, 11, 5),
      ngay_tinh_kh: new Date(2021, 0, 1),
      so_ky_khau_hao: 120,
      ngay_ct: new Date(2020, 11, 5),
      so_ct: 'CT005',
      tk_tai_san: '211',
      tk_khau_hao: '214',
      tk_chi_phi: '642',
      ma_bo_phan: 'HC001',
      nhom_tai_san_1: 'Nội thất văn phòng',
      nhom_tai_san_2: 'Bàn ghế',
      nhom_tai_san_3: 'Bàn làm việc'
    }
  ];
};

/**
 * Custom hook for managing Fixed Asset Catalog data
 *
 * This hook provides functionality to fetch fixed asset data
 * with mock support for testing and development purposes.
 */
export function useFixedAssetData(searchParams: SearchFormValues): UseFixedAssetReturn {
  const [data, setData] = useState<FixedAssetItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();
      console.log('Generated Mock Data:', mockData);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setData(mockData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
