import { useState } from 'react';
import { KhaiBaoThongTinCCDC, LoaiTSCDCCDC, BoPhanSuDungCCDC, Group } from '@/types/schemas';

/**
 * Hook for managing search field states in the bang-tinh-phan-bo-ccdc feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // Tool state (from BasicInfo tab)
  const [selectedTool, setSelectedTool] = useState<KhaiBaoThongTinCCDC | null>(null);

  // Tool type state (from DetailsTab)
  const [selectedToolType, setSelectedToolType] = useState<LoaiTSCDCCDC | null>(null);

  // Department state (from DetailsTab)
  const [selectedDepartment, setSelectedDepartment] = useState<BoPhanSuDungCCDC | null>(null);

  // Tool group states (from DetailsTab)
  const [selectedToolGroup1, setSelectedToolGroup1] = useState<Group | null>(null);
  const [selectedToolGroup2, setSelectedToolGroup2] = useState<Group | null>(null);
  const [selectedToolGroup3, setSelectedToolGroup3] = useState<Group | null>(null);

  return {
    // Tool
    selectedTool,
    setSelectedTool,

    // Tool type
    selectedToolType,
    setSelectedToolType,

    // Department
    selectedDepartment,
    setSelectedDepartment,

    // Tool groups
    selectedToolGroup1,
    setSelectedToolGroup1,
    selectedToolGroup2,
    setSelectedToolGroup2,
    selectedToolGroup3,
    setSelectedToolGroup3
  };
};
