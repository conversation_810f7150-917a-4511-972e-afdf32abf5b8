import {
  Copy,
  Eye,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Lock,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Search,
  Table,
  Trash
} from 'lucide-react';
import { Icon } from 'next/dist/lib/metadata/types/metadata-types';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onViewClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  className?: string;
}

export function ActionBar({
  className,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onViewClick,
  isEditDisabled = true,
  isViewDisabled = true
}: Props) {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Báo cáo giá thành sản phẩm theo nhóm yếu tố</h1>}>
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => window.location.reload()} />
      <AritoActionButton title='Cố định cột' icon={Lock} onClick={() => {}} disabled={isEditDisabled} />
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileSpreadsheet} onClick={() => {}} disabled={isEditDisabled} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
      {onViewClick && <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} disabled={isViewDisabled} />}
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
}
