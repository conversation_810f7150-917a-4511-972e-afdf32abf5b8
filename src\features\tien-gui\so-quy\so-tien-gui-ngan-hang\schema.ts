import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Main fields
  tai_khoan: z.string().optional(),
  ngay_tu: dateLike.optional(),
  ngay_den: dateLike.optional(),

  // Basic fields
  date: z.string().optional(),
  accountId: z.string().optional(),
  dataType: z.enum(['begin', 'end']).optional(),

  customerCode: z.string().optional(),

  customerGroup1: z.string().optional(),
  customerGroup2: z.string().optional(),
  customerGroup3: z.string().optional(),

  region: z.string().optional(),

  // MultiSelect không có name nên thường cần xử lý riêng
  // Nếu bạn dùng Controller và có field name như "groupBy", bạn có thể thêm:
  groupBy: z.array(z.string()).optional(),

  reportTemplate: z.enum(['quantity', 'quantityAndValue', 'quantityAndForeignValue']).optional(),

  // Đơn vị
  don_vi: z.string().optional(),
  month: z.number().optional(),
  toYear: z.number().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues = {
  tai_khoan: '',
  ngay_tu: '',
  ngay_den: '',
  date: '',
  accountId: '',
  dataType: undefined,
  customerCode: '',
  customerGroup1: '',
  customerGroup2: '',
  customerGroup3: '',
  region: '',
  groupBy: [],
  reportTemplate: undefined,
  don_vi: '',
  month: undefined,
  toYear: undefined
};
