'use client';

import { GridColDef } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import { balanceSheetMultiPeriodsSchema, initialValues } from './schema';
import { AritoDataTables } from '@/components/arito/arito-data-tables';
import { BasicInfoTab } from './components/filter-tabs/BasicInfoTab';
import { balanceSheetMultiPeriodsColumns } from './cols-definition';
import { DetailTab } from './components/filter-tabs/DetailTab';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { generateColumns } from './utils/generate-cols-utils';
import { OtherTab } from './components/filter-tabs/OtherTab';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { ActionBar } from './components/ActionBar';

export default function BangCanDoiKeToanChoNhieuKy({ initialRows }: { initialRows: any[] }) {
  const [showForm, setShowForm] = useState<boolean>(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [showTable, setShowTable] = useState<boolean>(false);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [currentObj, setCurrentObj] = useState<any>(initialValues);
  const [columns, setColumns] = useState<GridColDef[]>(balanceSheetMultiPeriodsColumns);

  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name
    }))
  );

  useEffect(() => {
    setColumns(generateColumns(currentObj));
  }, [currentObj]);

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    setCurrentObj(data);
    setShowForm(false);
    setShowTable(true);
  };

  const handleDirectSubmit = () => {
    console.log('Direct submit with data:', currentObj);
    // TODO: Implement form submission logic
    setShowForm(false);
    setShowTable(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleSearchClick = () => {
    setShowForm(true);
  };

  const handleRefreshClick = () => {
    console.log('Refresh clicked');
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
  };

  const handleExportDataClick = () => {
    console.log('Export data clicked');
  };

  const handleEditPrintTemplateClick = () => {
    console.log('Edit print template clicked');
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: columns
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <AritoDialog
        open={showForm}
        onClose={handleCloseForm}
        title='Bảng cân đối kế toán'
        maxWidth='md'
        titleIcon={<AritoIcon icon={12} />}
        actions={
          <>
            <Button
              variant='contained'
              type='submit'
              className='bg-[rgba(15,118,110,0.9)] hover:bg-[rgba(15,118,110,1)]'
              onClick={handleDirectSubmit}
            >
              <AritoIcon icon={884} />
              <span className='ml-1'>Đồng ý</span>
            </Button>
            <Button onClick={handleCloseForm} variant='outlined'>
              <AritoIcon icon={885} />
              <span className='ml-1'>Huỷ</span>
            </Button>
          </>
        }
      >
        <div className='flex size-full flex-col overflow-hidden'>
          <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
            <AritoForm<any>
              mode={formMode}
              hasAritoActionBar={false}
              schema={balanceSheetMultiPeriodsSchema}
              initialData={currentObj}
              onSubmit={handleFormSubmit}
              onClose={handleCloseForm}
              className='w-full'
              title='Bảng cân đối kế toán'
              headerFields={
                <div
                  className='max-h-[calc(100vh-150px)] overflow-y-auto'
                  style={{ width: '800px', minWidth: '800px' }}
                >
                  <BasicInfoTab />

                  <AritoHeaderTabs
                    tabs={[
                      {
                        id: 'detail',
                        label: 'Chi tiết',
                        component: <DetailTab />
                      },
                      {
                        id: 'other',
                        label: 'Khác',
                        component: <OtherTab />
                      }
                    ]}
                  />
                </div>
              }
            />
          </div>
        </div>
      </AritoDialog>

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
          />
          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
