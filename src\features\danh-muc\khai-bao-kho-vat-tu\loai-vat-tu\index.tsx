'use client';

import { getItemTypeColumns } from '@/features/danh-muc/khai-bao-kho-vat-tu/loai-vat-tu/cols-definition';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, DeleteDialog } from './components';
import { useFormState, useLoaiVatTu } from './hooks';

export default function Page() {
  const { loaiVatTus, isLoading, addLoaiVatTu, updateLoaiVatTu, deleteLoaiVatTu, refreshLoaiVatTus } = useLoaiVatTu();

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState();

  const handleFormSubmit = (data: any) => {
    try {
      if (formMode === 'add') {
        addLoaiVatTu(data);
      } else if (formMode === 'edit' && selectedObj) {
        updateLoaiVatTu(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const tables = [
    {
      name: '',
      rows: loaiVatTus,
      columns: getItemTypeColumns()
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={() => selectedObj && handleEditClick()}
        onViewClick={() => selectedObj && handleViewClick()}
        onDeleteClick={() => selectedObj && handleDeleteClick()}
        onCopyClick={() => selectedObj && handleCopyClick()}
        onRefreshClick={refreshLoaiVatTus}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
      />

      {!isLoading && (
        <div className='w-full overflow-hidden'>
          <AritoDataTables tables={tables} onRowClick={handleRowClick} selectedRowId={selectedRowIndex || undefined} />
        </div>
      )}

      {isLoading && (
        <div className='flex h-64 items-center justify-center'>
          <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500' />
        </div>
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteLoaiVatTu={deleteLoaiVatTu}
          clearSelection={clearSelection}
        />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onDeleteButtonClick={handleDeleteClick}
          onCopyButtonClick={handleCopyClick}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
        />
      )}
    </div>
  );
}
