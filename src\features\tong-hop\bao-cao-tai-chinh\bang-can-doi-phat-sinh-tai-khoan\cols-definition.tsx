import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { Checkbox } from '@/components/ui/checkbox';

export const TrialBalanceColumns: GridColDef[] = [
  { field: 'tk', headerName: 'Tài khoản', width: 100 },
  { field: 'ten_tk', headerName: 'Tên tài khoản', width: 300 },
  { field: 'no_dk', headerName: 'Dư nợ đầu', width: 100 },
  { field: 'co_dk', headerName: 'Dư có đầu', width: 100 },
  { field: 'ps_no', headerName: 'Ps nợ', width: 100 },
  { field: 'ps_co', headerName: 'Ps có', width: 100 },
  { field: 'no_ck', headerName: 'Dư nợ cuối', width: 100 },
  { field: 'co_ck', headerName: 'Dư có cuối', width: 100 }
];

export const ConstraintColumns: GridColDef[] = [
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 100 },
  {
    field: 'bat_buoc_nhap',
    headerName: 'Bắt buộc nhập',
    width: 120,
    renderCell: params => <Checkbox checked={params.value} />
  }
];

export const printColumns: GridColDef[] = [
  { field: 'columnName', headerName: 'Tên cột', width: 150, editable: true },
  {
    field: 'englishName',
    headerName: 'Tên tiếng Anh',
    width: 150,
    editable: true
  },
  {
    field: 'width',
    headerName: 'Độ rộng',
    width: 100,
    editable: true,
    type: 'number'
  },
  {
    field: 'format',
    headerName: 'Định dạng',
    width: 120,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Text', 'Number', 'Currency', 'Date', 'Percentage']
  },
  {
    field: 'alignment',
    headerName: 'Căn chỉnh',
    width: 100,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Left', 'Center', 'Right']
  },
  {
    field: 'bold',
    headerName: 'In đậm',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'italic',
    headerName: 'Nghiêng',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'total',
    headerName: 'Tổng',
    width: 80,
    editable: true,
    type: 'boolean'
  }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account', headerName: 'Tên tài khoản', flex: 1 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 1 },
  {
    field: 'ledger_account',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'detail_account',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'level', headerName: 'Bậc tk', flex: 1 }
];
