import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const TaxColumns: GridColDef[] = [
  { field: 'ma_cqt', headerName: '<PERSON>ã cơ quan thuế', width: 300 },
  { field: 'ten_cqt', headerName: 'Tên cơ quan thuế', width: 300 },
  { field: 'created', headerName: 'Ngày tạo', width: 300 }
];

export const taxSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cqt', headerName: 'Mã cơ quan thuế', flex: 1 },
  { field: 'ten_cqt', headerName: 'Tên cơ quan thuế', flex: 1 }
];
