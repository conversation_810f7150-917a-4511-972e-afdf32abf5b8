import React, { useState } from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import DocumentNumberRange from '../DocumentNumberRange';
import { KhaiBaoThongTinCCDC } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  searchFieldStates?: {
    selectedTool: KhaiBaoThongTinCCDC | null;
    setSelectedTool: (value: KhaiBaoThongTinCCDC | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  const [localSelectedTool, setLocalSelectedTool] = useState<KhaiBaoThongTinCCDC | null>(null);

  const selectedTool = searchFieldStates?.selectedTool ?? localSelectedTool;
  const setSelectedTool = searchFieldStates?.setSelectedTool ?? setLocalSelectedTool;

  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          {/* 1. Từ kỳ/năm */}
          <Label className='w-40 min-w-40'>Từ kỳ/năm</Label>
          <DocumentNumberRange fromDocumentNumberName='tu_ky' toDocumentNumberName='tu_nam' />
        </div>
        {/* 2. Đến kỳ/năm */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đến kỳ/năm</Label>
          <DocumentNumberRange fromDocumentNumberName='den_ky' toDocumentNumberName='den_nam' />
        </div>
        {/* 3. Mã công cụ */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã công cụ</Label>
          <div className='w-64'>
            <SearchField<KhaiBaoThongTinCCDC>
              type='text'
              displayRelatedField='ma_cc'
              columnDisplay='ma_cc'
              searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_CCDC}`}
              searchColumns={[
                {
                  field: 'ma_cc',
                  headerName: 'Mã công cụ',
                  width: 120
                },
                {
                  field: 'ten_cc',
                  headerName: 'Tên công cụ',
                  width: 200
                }
              ]}
              value={selectedTool?.ma_cc || ''}
              relatedFieldValue={selectedTool?.ma_cc || ''}
              onRowSelection={setSelectedTool}
            />
          </div>
        </div>
        {/* 4. Lọc công cụ */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Lọc công cụ</Label>
          <div className='w-64'>
            <FormField
              name='xu_ly'
              label=''
              type='select'
              options={[
                { value: '0', label: 'Chỉ lấy công cụ còn phân bổ' },
                { value: '1', label: 'Lấy công cụ còn phân bổ hoặc đã hết phân bổ nhưng chưa khai báo giảm/hỏng' }
              ]}
            />
          </div>
        </div>
        {/* 5. Chi tiết theo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chi tiết theo</Label>
          <div className='w-64'>
            <FormField
              name='detailBy'
              label=''
              type='select'
              options={[
                { value: '0', label: 'Công cụ' },
                { value: '1', label: 'Công cụ và bộ phận' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
