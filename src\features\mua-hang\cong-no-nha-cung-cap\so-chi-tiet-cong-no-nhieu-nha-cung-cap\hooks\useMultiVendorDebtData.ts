import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export interface MultiVendorDebtItem {
  id: string;
  supplierCode: string;
  supplierName: string;
  openingDebit: number;
  openingCredit: number;
  debitTransactions: number;
  creditTransactions: number;
  closingDebit: number;
  closingCredit: number;
  disabled: string;
}

export interface MultiVendorDebtResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: MultiVendorDebtItem[];
}

export interface UseMultiVendorDebtReturn {
  data: MultiVendorDebtItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): MultiVendorDebtItem[] => {
  return [
    {
      id: '1',
      supplierCode: 'NCC001',
      supplierName: 'Công ty TNHH ABC',
      openingDebit: 50000000,
      openingCredit: 0,
      debitTransactions: 120000000,
      creditTransactions: 100000000,
      closingDebit: 70000000,
      closingCredit: 0,
      disabled: '1'
    },
    {
      id: '2',
      supplierCode: 'NCC002',
      supplierName: 'Công ty CP XYZ',
      openingDebit: 0,
      openingCredit: 30000000,
      debitTransactions: 80000000,
      creditTransactions: 150000000,
      closingDebit: 0,
      closingCredit: 100000000,
      disabled: '1'
    },
    {
      id: '3',
      supplierCode: 'NCC003',
      supplierName: 'Công ty TNHH DEF',
      openingDebit: 25000000,
      openingCredit: 0,
      debitTransactions: 200000000,
      creditTransactions: 180000000,
      closingDebit: 45000000,
      closingCredit: 0,
      disabled: '1'
    },
    {
      id: '4',
      supplierCode: 'NCC004',
      supplierName: 'Công ty CP GHI',
      openingDebit: 0,
      openingCredit: 15000000,
      debitTransactions: 90000000,
      creditTransactions: 120000000,
      closingDebit: 0,
      closingCredit: 45000000,
      disabled: '1'
    },
    {
      id: '5',
      supplierCode: 'NCC005',
      supplierName: 'Công ty TNHH JKL',
      openingDebit: 35000000,
      openingCredit: 0,
      debitTransactions: 160000000,
      creditTransactions: 140000000,
      closingDebit: 55000000,
      closingCredit: 0,
      disabled: '1'
    },
    {
      id: '6',
      supplierCode: 'NCC006',
      supplierName: 'Công ty CP MNO',
      openingDebit: 0,
      openingCredit: 20000000,
      debitTransactions: 110000000,
      creditTransactions: 160000000,
      closingDebit: 0,
      closingCredit: 70000000,
      disabled: '1'
    },
    {
      id: '7',
      supplierCode: 'NCC007',
      supplierName: 'Công ty TNHH PQR',
      openingDebit: 40000000,
      openingCredit: 0,
      debitTransactions: 180000000,
      creditTransactions: 170000000,
      closingDebit: 50000000,
      closingCredit: 0,
      disabled: '1'
    },
    {
      id: '8',
      supplierCode: 'NCC008',
      supplierName: 'Công ty CP STU',
      openingDebit: 0,
      openingCredit: 25000000,
      debitTransactions: 95000000,
      creditTransactions: 130000000,
      closingDebit: 0,
      closingCredit: 60000000,
      disabled: '1'
    }
  ];
};

/**
 * Custom hook for managing Multi Vendor Debt Detail Ledger data
 *
 * This hook provides functionality to fetch multi vendor debt data
 * with mock support for testing and development purposes.
 */
export function useMultiVendorDebtData(searchParams: SearchFormValues): UseMultiVendorDebtReturn {
  const [data, setData] = useState<MultiVendorDebtItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<MultiVendorDebtResponse>(
        '/mua-hang/cong-no-nha-cung-cap/so-chi-tiet-cong-no-nhieu-nha-cung-cap/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
