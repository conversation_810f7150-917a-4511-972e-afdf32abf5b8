'use client';

import Split from 'react-split';
import { AritoSplitPane } from '@/components/custom/arito/split-pane';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, ConfirmDialog, FormDialog } from './components';
import SidebarButton from '@/components/arito/sidebar-button';
import AritoIcon from '@/components/custom/arito/icon';
import { itemGroupColumns } from './cols-definition';
import { useData, useFormState } from './hooks';
import { GroupType } from '@/types/schemas';
import { useGroup } from '@/hooks/queries';

export const groupTypes = [
  {
    label: 'Nhóm vật tư 1 (VT1)',
    value: GroupType.MATERIAL1,
    shortCode: 'VT1'
  },
  {
    label: 'Nhóm vật tư 2 (VT2)',
    value: GroupType.MATERIAL2,
    shortCode: 'VT2'
  },
  {
    label: 'Nhóm vật tư 3 (VT3)',
    value: GroupType.MATERIAL3,
    shortCode: 'VT3'
  }
];
const DEFAULT_GROUP = groupTypes[0].value;

export default function ItemGroupPage() {
  const {
    groups: groupsVT1,
    isLoading: isLoadingVT1,
    refreshGroups: refreshGroupsVT1,
    fetchGroupsByType: fetchGroupsByTypeVT1,
    addGroup: addGroupVT1,
    updateGroup: updateGroupVT1,
    deleteGroup: deleteGroupVT1
  } = useGroup(GroupType.MATERIAL1);

  const {
    groups: groupsVT2,
    isLoading: isLoadingVT2,
    refreshGroups: refreshGroupsVT2,
    fetchGroupsByType: fetchGroupsByTypeVT2,
    addGroup: addGroupVT2,
    updateGroup: updateGroupVT2,
    deleteGroup: deleteGroupVT2
  } = useGroup(GroupType.MATERIAL2);

  const {
    groups: groupsVT3,
    isLoading: isLoadingVT3,
    refreshGroups: refreshGroupsVT3,
    fetchGroupsByType: fetchGroupsByTypeVT3,
    addGroup: addGroupVT3,
    updateGroup: updateGroupVT3,
    deleteGroup: deleteGroupVT3
  } = useGroup(GroupType.MATERIAL3);

  const getGroupFunctions = (groupType: GroupType) => {
    switch (groupType) {
      case GroupType.MATERIAL1:
        return {
          groups: groupsVT1,
          refreshGroups: refreshGroupsVT1,
          fetchGroupsByType: fetchGroupsByTypeVT1,
          addGroup: addGroupVT1,
          updateGroup: updateGroupVT1,
          deleteGroup: deleteGroupVT1,
          isLoading: isLoadingVT1
        };
      case GroupType.MATERIAL2:
        return {
          groups: groupsVT2,
          refreshGroups: refreshGroupsVT2,
          fetchGroupsByType: fetchGroupsByTypeVT2,
          addGroup: addGroupVT2,
          updateGroup: updateGroupVT2,
          deleteGroup: deleteGroupVT2,
          isLoading: isLoadingVT2
        };
      case GroupType.MATERIAL3:
        return {
          groups: groupsVT3,
          refreshGroups: refreshGroupsVT3,
          fetchGroupsByType: fetchGroupsByTypeVT3,
          addGroup: addGroupVT3,
          updateGroup: updateGroupVT3,
          deleteGroup: deleteGroupVT3,
          isLoading: isLoadingVT3
        };
      default:
        return {
          groups: groupsVT1,
          refreshGroups: refreshGroupsVT1,
          fetchGroupsByType: fetchGroupsByTypeVT1,
          addGroup: addGroupVT1,
          updateGroup: updateGroupVT1,
          deleteGroup: deleteGroupVT1,
          isLoading: isLoadingVT1
        };
    }
  };

  const { activeGroup, handleFilter } = useData(DEFAULT_GROUP);

  const { isLoading, addGroup, updateGroup, deleteGroup, groups, refreshGroups } = getGroupFunctions(activeGroup);

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState(activeGroup);

  const tables = [
    {
      name: '',
      rows: groups,
      columns: itemGroupColumns,
      key: `${activeGroup}-${Date.now()}`
    }
  ];

  const handleFormSubmit = async (data: any) => {
    try {
      const formData = {
        ...data,
        loai_nhom: activeGroup
      };

      if (formMode === 'add') {
        await addGroup(formData);
        await refreshGroups();
      } else if (formMode === 'edit' && selectedObj) {
        await updateGroup(selectedObj.uuid, formData);
        await refreshGroups();
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className='flex h-screen w-screen flex-row overflow-hidden bg-white'>
      <AritoSplitPane split='vertical' minSize={200} defaultSize={300}>
        <div className='h-full overflow-y-auto border-r'>
          <div className='p-4'>
            <h2 className='mb-4 flex items-center gap-1 text-lg font-semibold'>
              <AritoIcon icon={539} /> Danh mục phân nhóm
            </h2>
            <hr className='mb-4' />
            <ul>
              {groupTypes.map(group => (
                <li key={group.value} className='mb-2 text-sm'>
                  <SidebarButton isActive={activeGroup === group.value} onClick={() => handleFilter(group.value)}>
                    {group.label}
                  </SidebarButton>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className='h-full overflow-y-auto'>
          <div className='flex h-full flex-col'>
            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onViewClick={() => selectedObj && handleViewClick()}
              isViewDisabled={!selectedObj}
              activeGroup={activeGroup}
              onRefresh={() => refreshGroups()}
            />

            <div className='flex-1 overflow-hidden'>
              <Split
                className='flex flex-1 flex-col overflow-hidden'
                direction='vertical'
                sizes={[50, 50]}
                minSize={200}
                gutterSize={8}
                gutterAlign='center'
                snapOffset={30}
                dragInterval={1}
                cursor='row-resize'
              >
                <div className='w-full overflow-hidden'>
                  {isLoading && (
                    <div className='flex h-64 items-center justify-center'>
                      <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500' />
                    </div>
                  )}

                  {!isLoading && (
                    <AritoDataTables
                      key={activeGroup}
                      tables={tables}
                      onRowClick={handleRowClick}
                      selectedRowId={selectedRowIndex || undefined}
                      getRowId={row => row.uuid}
                    />
                  )}
                </div>
              </Split>
            </div>
          </div>
        </div>
      </AritoSplitPane>

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onDeleteButtonClick={handleDeleteClick}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          activeGroup={activeGroup}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          selectedObj={selectedObj}
          onClose={handleCloseDelete}
          deleteGroup={deleteGroup}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
