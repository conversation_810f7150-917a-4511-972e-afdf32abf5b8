import { GridColDef } from '@mui/x-data-grid';
import type { ExtendedGridColDef } from '@/components/custom/arito/search-table';

// Main table
export const getDataTableColumns = (): GridColDef[] => [
  { field: 'trang_thai', headerName: 'Trạng thái', width: 120 },
  { field: 'trang_thai_hddt', headerName: 'Trạng thái HĐĐT', width: 150 },
  { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
  { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ma_khach_hang', headerName: 'Mã khách hàng', width: 150 },
  { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
  { field: 'dien_giai', headerName: '<PERSON><PERSON><PERSON> g<PERSON>', width: 250 },
  { field: 'tai_khoan_no', headerName: 'Tk nợ', width: 100 },
  { field: 'tong_tien', headerName: 'Tổng tiền', width: 150 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 },
  { field: 'ma_cua_hang', headerName: 'Mã cửa hàng', width: 120 },
  { field: 'ma_nguon_don', headerName: 'Mã nguồn đơn', width: 150 },
  { field: 'ma_hoa_don', headerName: 'Mã hoá đơn', width: 150 }
];

// Input tab
export const getInputTableColumns = (): GridColDef[] => [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'don_vi_tinh', headerName: 'Đvt', width: 80 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'ton', headerName: 'Tồn', width: 80 },
  { field: 'loai_hang', headerName: 'Loại hàng', width: 120 },
  { field: 'so_luong', headerName: 'Số lượng', width: 100 },
  { field: 'gia_chuan', headerName: 'Giá chuẩn', width: 120 },
  { field: 'gia_ban', headerName: 'Giá bán', width: 120 },
  { field: 'doanh_so', headerName: 'Doanh số', width: 120 },
  { field: 'ti_le_chiet_khau', headerName: 'Tl ck(%)', width: 100 },
  { field: 'giam_gia', headerName: 'Giảm giá', width: 120 },
  { field: 'chiet_khau', headerName: 'Ch.khấu', width: 120 },
  { field: 'dich_danh', headerName: 'Đích danh', width: 100 },
  { field: 'gia_ton', headerName: 'Giá tồn', width: 120 },
  { field: 'tien', headerName: 'Tiền', width: 120 },
  { field: 'ma_thue', headerName: 'Mã thuế', width: 100 },
  { field: 'tai_khoan_thue_co', headerName: 'Tk thuế có', width: 120 },
  { field: 'thue_suat', headerName: 'Thuế suất(%)', width: 120 },
  { field: 'thue', headerName: 'Thuế', width: 120 },
  { field: 'tai_khoan_doanh_thu', headerName: 'Tk doanh thu', width: 150 },
  { field: 'tai_khoan_gia_von', headerName: 'Tk giá vốn', width: 150 },
  { field: 'tai_khoan_kho', headerName: 'Tk kho', width: 120 },
  { field: 'tai_khoan_giam_gia', headerName: 'Tk giảm giá', width: 150 },
  { field: 'tai_khoan_chiet_khau', headerName: 'Tk chiết khấu', width: 150 },
  { field: 'tai_khoan_khuyen_mai', headerName: 'Tk khuyến mãi', width: 150 },
  { field: 'ghi_chu_ten_vat_tu', headerName: 'Ghi chú tên vật tư', width: 250 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 120 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 80 },
  { field: 'san_pham_chung', headerName: 'Sản phẩm', width: 150 }, // Assuming this is a general product column
  { field: 'gia_ton_2', headerName: 'Giá tồn', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'tien_2', headerName: 'Tiền', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'gia_chuan_2', headerName: 'Giá chuẩn', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'gia_ban_2', headerName: 'Giá bán', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'doanh_so_2', headerName: 'Doanh số', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'giam_gia_2', headerName: 'Giảm giá', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'chiet_khau_2', headerName: 'Chiết khấu', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'thue_2', headerName: 'Thuế', width: 120 }, // Added '_2' to avoid duplicate key
  { field: 'so_luong_da_xuat', headerName: 'Sl đã xuất', width: 100 },
  { field: 'so_phieu_xuat', headerName: 'Số PX', width: 120 },
  { field: 'dong_phieu_xuat', headerName: 'Dòng px', width: 100 },
  { field: 'so_don_hang', headerName: 'Số đơn hàng', width: 120 },
  { field: 'dong_don_hang', headerName: 'Dòng ĐH', width: 100 },
  { field: 'ma_hang', headerName: 'Mã hàng', width: 120 },
  { field: 'gia_tu_pos', headerName: 'Giá từ POS', width: 120 },
  { field: 'chiet_khau_pos', headerName: 'CK POS', width: 100 },
  { field: 'giam_gia_pos', headerName: 'Giảm giá POS', width: 120 }
];

// Chi Tiết tab
export const getDetailsTableColumns = (): GridColDef[] => [
  { field: 'trang_thai', headerName: 'Trạng thái', width: 120 },
  { field: 'trang_thai_hddt', headerName: 'Trạng thái HĐĐT', width: 150 },
  { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
  { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ma_khach_hang', headerName: 'Mã khách hàng', width: 150 },
  { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'tai_khoan_no', headerName: 'Tk nợ', width: 100 },
  { field: 'tong_tien', headerName: 'Tổng tiền', width: 150 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 },
  { field: 'ma_cua_hang', headerName: 'Mã cửa hàng', width: 120 },
  { field: 'ma_nguon_don', headerName: 'Mã nguồn đơn', width: 150 },
  { field: 'ma_hoa_don', headerName: 'Mã hoá đơn', width: 150 }
];

// Chiết Khấu tab
export const getDiscountTableColumns = (): GridColDef[] => [
  { field: 'ma_chiet_khau', headerName: 'Mã chiết khấu', width: 150 },
  { field: 'ten_chiet_khau', headerName: 'Tên chiết khấu', width: 200 },
  { field: 'loai_chiet_khau', headerName: 'Loại chiết khấu', width: 150 },
  { field: 'id_chiet_khau', headerName: 'ID ck', width: 100 },
  { field: 'line_chiet_khau', headerName: 'Line ck', width: 100 },
  { field: 'ty_le_chiet_khau', headerName: 'Tỷ lệ ck', width: 100 },
  { field: 'tien_chiet_khau_theo_line', headerName: 'Tiền ck tl', width: 120 },
  { field: 'tien_chiet_khau', headerName: 'Tiền ck', width: 120 },
  { field: 'tang_hang', headerName: 'Tặng hàng', width: 100 },
  { field: 'ma_hang_tang', headerName: 'Mã hàng tặng', width: 150 },
  { field: 'ten_hang_tang', headerName: 'Tên hàng tặng', width: 200 },
  { field: 'don_vi_tinh', headerName: 'Đvt', width: 80 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'so_luong_tang', headerName: 'Sl tặng', width: 80 },
  { field: 'tinh_tren_tien_hang', headerName: 'Tính trên tiền hàng', width: 150 }
];

// Chi tiết chiết khấu tab
export const getDiscountDetailsTableColumns = (): GridColDef[] => [
  { field: 'ma_chiet_khau', headerName: 'Mã chiết khấu', width: 150 },
  { field: 'ten_chiet_khau', headerName: 'Tên chiết khấu', width: 200 },
  { field: 'loai_chiet_khau', headerName: 'Loại chiết khấu', width: 150 },
  { field: 'id_chiet_khau', headerName: 'ID ck', width: 100 },
  { field: 'line_chiet_khau', headerName: 'Line ck', width: 100 },
  { field: 'ty_le_chiet_khau', headerName: 'Tỷ lệ ck', width: 100 },
  { field: 'tien_chiet_khau_theo_line', headerName: 'Tiền ck tl', width: 120 },
  { field: 'tien_chiet_khau', headerName: 'Tiền ck', width: 120 },
  { field: 'tang_hang', headerName: 'Tặng hàng', width: 100 },
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'don_vi_tinh', headerName: 'Đvt', width: 80 },
  { field: 'so_luong', headerName: 'Số lượng', width: 100 },
  { field: 'gia_ban', headerName: 'Giá bán', width: 120 },
  { field: 'tinh_tren_tien_hang', headerName: 'Tính trên tiền hàng', width: 150 },
  { field: 'line_chung_tu', headerName: 'Line ct', width: 100 }
];

// Thông tin thanh toán tab
export const getPaymentInfoTableColumns = (): GridColDef[] => [
  { field: 'ma_hinh_thuc', headerName: 'Mã hình thức', width: 120 },
  { field: 'ten_hinh_thuc', headerName: 'Tên hình thức', width: 150 },
  { field: 'tai_khoan_thanh_toan', headerName: 'Tk thanh toán', width: 150 },
  { field: 'chung_tu', headerName: 'Chứng từ', width: 120 },
  { field: 'ngay_chung_tu', headerName: 'Ngày chứng từ', width: 120 },
  { field: 'quyen_chung_tu', headerName: 'Quyển chứng từ', width: 120 },
  { field: 'so_tien_thanh_toan', headerName: 'Số tiền thanh toán', width: 150 }
];

/* Search Columns */

export const diaChiSearchColumns: ExtendedGridColDef[] = [
  { field: 'address', headerName: 'Địa chỉ' },
  { field: 'description', headerName: 'Diễn giải' }
];

export const employeeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nv', headerName: 'Mã nhân viên' },
  { field: 'ten_nv', headerName: 'Tên nhân viên', flex: 2 }
];

export const transportMethodSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ptvc', headerName: 'Mã phương tiện' },
  { field: 'ten_ptvc', headerName: 'Tên phương tiện' }
];
export const paymentMethodSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_pttt', headerName: 'Mã phương thức' },
  { field: 'ten_pttt', headerName: 'Tên phương thức' }
];
export const deliveryMethodSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ptgh', headerName: 'Mã phương thức' },
  { field: 'ten_ptgh', headerName: 'Tên phương thức' }
];

export const customerSearchColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã khách hàng', flex: 1 },
  { field: 'customer_name', headerName: 'Tên khách hàng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 1 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'parent_account_code', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'is_parent_account', headerName: 'Tk sổ cái', flex: 1, checkboxSelection: true },
  { field: 'active', headerName: 'Tk chi tiết', flex: 1, checkboxSelection: true },
  { field: 'account_level', headerName: 'Bậc tk', flex: 1 }
];

export const paymentSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_pttt', headerName: 'Mã thanh toán', flex: 1 },
  { field: 'ten_pttt', headerName: 'Tên thanh toán', flex: 2 },
  { field: 'han_thanh_toan', headerName: 'Hạn thanh toán', flex: 1 }
];

export const storeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cua_hang', headerName: 'Mã cửa hàng', flex: 1 },
  { field: 'tkcn', headerName: 'Tài khoản công nợ', flex: 2 },
  { field: 'bo_phan', headerName: 'Bộ phận', flex: 2 },
  { field: 'ten_cua_hang', headerName: 'Tên cửa hàng', flex: 2 }
];

export const orderSourceSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nguondon', headerName: 'Mã nguồn đơn', flex: 1 },
  { field: 'ten_nguondon', headerName: 'Tên nguồn đơn', flex: 2 }
];

export const materialSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 120 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'nhom_1', headerName: 'Nhóm 1', width: 120 }
];

export const warehouseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'don_vi', headerName: 'Đơn vị', width: 120 },
  { field: 'theo_doi_vi_tri', headerName: 'Theo dõi vị trí', width: 120 }
];

export const batchSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lo', headerName: 'Mã lô', width: 120 },
  { field: 'ten_lo', headerName: 'Tên lô', width: 280 }
];

export const locationSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 120 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', width: 280 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_don_vi', headerName: 'Mã đơn vị', width: 120 },
  { field: 'ten_don_vi', headerName: 'Tên đơn vị', width: 250 }
];
