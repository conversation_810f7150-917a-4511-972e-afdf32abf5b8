import { useState, useEffect, useCallback } from 'react';
import { LoaiVatTu, LoaiVatTuInput, LoaiVatTuListResponse } from '@/types/schemas/loai-vat-tu.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseLoaiVatTuReturn {
  loaiVatTus: LoaiVatTu[];
  isLoading: boolean;
  addLoaiVatTu: (newLoaiVatTu: LoaiVatTuInput) => Promise<void>;
  updateLoaiVatTu: (uuid: string, updatedLoaiVatTu: LoaiVatTuInput) => Promise<void>;
  deleteLoaiVatTu: (uuid: string) => Promise<void>;
  refreshLoaiVatTus: () => Promise<void>;
}

export const useLoaiVatTu = (initialLoaiVatTus: LoaiVatTu[] = []): UseLoaiVatTuReturn => {
  const [loaiVatTus, setLoaiVatTus] = useState<LoaiVatTu[]>(initialLoaiVatTus);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchLoaiVatTus = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<LoaiVatTuListResponse>(`/entities/${entity.slug}/erp/product-types/`);
      setLoaiVatTus(response.data.results as unknown as LoaiVatTu[]);
    } catch (error) {
      console.error('Error fetching material types:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  const addLoaiVatTu = async (newLoaiVatTu: LoaiVatTuInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const requestData = {
        ma_loai_vat_tu: newLoaiVatTu.ma_loai_vat_tu,
        ten_loai_vat_tu: newLoaiVatTu.ten_loai_vat_tu,
        ten_khac: newLoaiVatTu.ten_khac || null,
        tk_kho: newLoaiVatTu.tk_kho || null,
        tk_doanh_thu: newLoaiVatTu.tk_doanh_thu || null,
        tk_gia_von: newLoaiVatTu.tk_gia_von || null,
        trang_thai:
          newLoaiVatTu.trang_thai !== undefined && newLoaiVatTu.trang_thai !== null
            ? String(newLoaiVatTu.trang_thai)
            : '1'
      };

      const response = await api.post(`/entities/${entity.slug}/erp/product-types/`, requestData);

      const addedLoaiVatTu: LoaiVatTu = response.data;
      setLoaiVatTus(prev => [...prev, addedLoaiVatTu]);
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 400 && error.response.data?.error?.includes('already exists')) {
          alert('Mã loại vật tư đã tồn tại. Vui lòng sử dụng mã khác.');
        }
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateLoaiVatTu = async (uuid: string, updatedLoaiVatTu: LoaiVatTuInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const requestData = {
        ma_loai_vat_tu: updatedLoaiVatTu.ma_loai_vat_tu,
        ten_loai_vat_tu: updatedLoaiVatTu.ten_loai_vat_tu,
        ten_khac: updatedLoaiVatTu.ten_khac || null,
        tk_kho: updatedLoaiVatTu.tk_kho || null,
        tk_doanh_thu: updatedLoaiVatTu.tk_doanh_thu || null,
        tk_gia_von: updatedLoaiVatTu.tk_gia_von || null,
        trang_thai:
          updatedLoaiVatTu.trang_thai !== undefined && updatedLoaiVatTu.trang_thai !== null
            ? String(updatedLoaiVatTu.trang_thai)
            : '1'
      };

      const response = await api.patch(`/entities/${entity.slug}/erp/product-types/${uuid}/`, requestData);

      const updatedLoaiVatTuData: LoaiVatTu = response.data;

      setLoaiVatTus(prev =>
        prev.map(loaiVatTu => (loaiVatTu.uuid === updatedLoaiVatTuData.uuid ? updatedLoaiVatTuData : loaiVatTu))
      );
    } catch (error: any) {
      if (error.response) {
        console.error('Update error:', error.response.data);
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteLoaiVatTu = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    await api.delete(`/entities/${entity.slug}/erp/product-types/${uuid}/`);
    setLoaiVatTus(prev => prev.filter(loaiVatTu => loaiVatTu.uuid !== uuid));
    setIsLoading(false);
  };

  useEffect(() => {
    fetchLoaiVatTus();
  }, [fetchLoaiVatTus]);

  return {
    loaiVatTus,
    isLoading,
    addLoaiVatTu,
    updateLoaiVatTu,
    deleteLoaiVatTu,
    refreshLoaiVatTus: fetchLoaiVatTus
  };
};
