import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Typography
} from '@mui/material';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import React from 'react';
import { ExtendedGridColDef, SearchTable } from '@/components/custom/arito/search-table';
import { fieldStyles, dialogStyles } from '../styles/constants';
import AritoIcon from '@/components/custom/arito/icon';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  title: string;
  searchQuery: string;
  onSearchQueryChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isLoading: boolean;
  searchResults: any[];
  searchColumns?: ExtendedGridColDef[];
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  selectedResult?: any;
  selectedResults?: any[];
  multiSelect?: boolean;
  onResultClick: (result: any | any[]) => void;
  onResultDoubleClick: (result: any) => void;
  onConfirm: (results: any | any[]) => void;
  isMobile: boolean;
}

export const SearchDialog: React.FC<SearchDialogProps> = ({
  open,
  onClose,
  title,
  searchQuery,
  onSearchQueryChange,
  isLoading,
  searchResults,
  searchColumns,
  searchResultLabelKey,
  searchResultValueKey,
  selectedResult,
  selectedResults,
  multiSelect,
  onResultClick,
  onResultDoubleClick,
  onConfirm,
  isMobile
}) => {
  const [isFullScreen, setIsFullScreen] = React.useState<boolean>(isMobile);

  const toggleFullScreen = () => setIsFullScreen(!isFullScreen);

  return (
    <Dialog
      open={open}
      onClose={() => {}} // Empty function to disable closing on backdrop click
      disableEscapeKeyDown // Disable Esc key to close modal
      fullScreen={isMobile || isFullScreen}
      PaperProps={{
        sx: dialogStyles(isMobile, isFullScreen)
      }}
    >
      <DialogTitle sx={fieldStyles.searchDialog.title}>
        <Typography
          component='div'
          sx={{
            fontSize: '16px',
            color: '#666666',
            display: 'flex',
            alignItems: 'center',
            marginLeft: '-12px',
            padding: '0 8px'
          }}
        >
          <AritoIcon icon={584} marginX='8px' />
          {title}
        </Typography>
        {!isMobile && (
          <IconButton
            size='small'
            onClick={toggleFullScreen}
            title={isFullScreen ? 'Thoát toàn màn hình' : 'Toàn màn hình'}
            sx={{ marginLeft: 'auto', padding: '4px' }}
          >
            {isFullScreen ? <FullscreenExitIcon fontSize='small' /> : <FullscreenIcon fontSize='small' />}
          </IconButton>
        )}
      </DialogTitle>

      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', height: 'calc(100% - 75px)' }}>
        <Box sx={fieldStyles.searchDialog.searchBar}>
          <TextField
            variant='outlined'
            placeholder='Tìm kiếm...'
            size='small'
            value={searchQuery}
            onChange={onSearchQueryChange}
            sx={{
              width: '300px',
              ml: 1,
              '& .MuiOutlinedInput-input': {
                padding: '4px 8px 4px 12px',
                fontSize: '0.8rem',
                height: '22px'
              },
              '& .MuiOutlinedInput-root': {
                borderRadius: '0px'
              }
            }}
            InputProps={{
              startAdornment: (
                <Box
                  sx={{ display: 'flex', alignItems: 'center', mr: 0.5, marginRight: '4px', color: 'text.secondary' }}
                >
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    width='14'
                    height='14'
                    viewBox='0 0 20 20'
                    fill='currentColor'
                  >
                    <path
                      fillRule='evenodd'
                      d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z'
                      clipRule='evenodd'
                    />
                  </svg>
                </Box>
              )
            }}
          />

          <Typography
            variant='caption'
            sx={{ color: 'text.secondary', mr: 2, ml: 1, fontSize: '0.7rem', padding: '0 8px' }}
          >
            {multiSelect ? 'Bấm vào hàng để chọn nhiều' : 'Nhấp đúp để chọn ngay'}
          </Typography>
        </Box>

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3, padding: '16px' }}>
            <CircularProgress size={24} />
          </Box>
        ) : (
          <Box
            sx={{
              flexGrow: 1,
              overflowY: 'auto',
              height: '100%',
              maxHeight: 'calc(100% - 40px)',
              '& .MuiDataGrid-root': {
                border: 'none',
                height: '100% !important'
              },
              position: 'relative'
            }}
          >
            <SearchTable
              searchResults={searchResults}
              searchColumns={searchColumns}
              searchResultLabelKey={searchResultLabelKey}
              searchResultValueKey={searchResultValueKey}
              selectedResult={multiSelect ? undefined : selectedResult}
              selectedResults={multiSelect ? selectedResults : undefined}
              multiSelect={multiSelect}
              onResultClick={onResultClick}
              onResultDoubleClick={onResultDoubleClick}
              showActionButtons={true}
              hideButtons={true}
              onConfirm={onConfirm}
              onCancel={onClose}
            />
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={fieldStyles.searchDialog.actions}>
        <Button
          onClick={() => onConfirm(multiSelect ? selectedResults || [] : selectedResult)}
          variant='contained'
          disabled={multiSelect ? !selectedResults?.length : !selectedResult}
          sx={fieldStyles.buttons.confirm}
        >
          <Box component='span' sx={{ display: 'flex', alignItems: 'center' }}>
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Box>
        </Button>
        <Button onClick={onClose} variant='outlined' sx={fieldStyles.buttons.cancel}>
          <Box component='span' sx={{ display: 'flex', alignItems: 'center' }}>
            <AritoIcon icon={885} marginX='4px' />
            Hủy
          </Box>
        </Button>
      </DialogActions>
    </Dialog>
  );
};
