/**
 * TypeScript interfaces for CCDC Inventory (Công Cụ Dụng Cụ Inventory Check)
 *
 * This interface represents the structure for CCDC inventory management,
 * including inventory items, search parameters, and API responses.
 */

import { ApiResponse } from '../api.type';

/**
 * Interface for CCDC Inventory Item
 */
export interface CCDCInventoryItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Serial number
   */
  stt: number;

  /**
   * Tool/Equipment code
   */
  ma_cc: string;

  /**
   * Tool/Equipment name
   */
  ten_cc: string;

  /**
   * Original cost/price
   */
  nguyen_gia: number;

  /**
   * Depreciated value (foreign currency)
   */
  gt_da_kh_nt0: number;

  /**
   * Remaining value
   */
  gt_cl: number;

  /**
   * Depreciation for current period
   */
  gt_kh_ky: number;

  /**
   * Purchase date
   */
  ngay_mua: string;

  /**
   * Depreciation start date
   */
  ngay_kh0: string;

  /**
   * Number of depreciation periods
   */
  so_ky_kh: number;

  /**
   * Document date
   */
  ngay_ct: string;

  /**
   * Document number
   */
  so_ct: string;

  /**
   * Tool account code
   */
  tk_cc: string;

  /**
   * Depreciation account code
   */
  tk_kh: string;

  /**
   * Expense account code
   */
  tk_cp: string;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * Tool group 1
   */
  nh_cc1: string;

  /**
   * Tool group 2
   */
  nh_cc2: string;

  /**
   * Tool group 3
   */
  nh_cc3: string;

  /**
   * Department name
   */
  ten_bp?: string;

  /**
   * Tool type
   */
  loai_cc?: string;

  /**
   * Status
   */
  trang_thai?: string;

  /**
   * Foreign currency code
   */
  ma_nt?: string;

  /**
   * Exchange rate
   */
  ty_gia?: number;

  /**
   * Original cost in foreign currency
   */
  nguyen_gia_nt?: number;

  /**
   * Remaining value in foreign currency
   */
  gt_cl_nt?: number;

  /**
   * Current period depreciation in foreign currency
   */
  gt_kh_ky_nt?: number;
}

/**
 * Interface for CCDC Inventory API Response
 */
export interface CCDCInventoryResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CCDCInventoryItem[];
}

/**
 * Interface for CCDC Inventory Search Form Values
 */
export interface CCDCInventorySearchFormValues {
  /**
   * End date for the report
   */
  den_ngay?: string;

  /**
   * Type filter
   */
  loai?: string;

  /**
   * Department code
   */
  ma_bp?: string;

  /**
   * Report template
   */
  mau_bc?: string;

  /**
   * Report filter template
   */
  report_filter_template?: string;

  /**
   * Data analysis structure
   */
  data_analysis_struct?: string;

  /**
   * Tool group 1 code
   */
  nh1_ma_nhom?: string;

  /**
   * Tool group 2 code
   */
  nh2_ma_nhom?: string;

  /**
   * Tool group 3 code
   */
  nh3_ma_nhom?: string;

  /**
   * Tool type code
   */
  ma_loai_cc?: string;

  /**
   * Tool code filter
   */
  ma_cc_tu?: string;

  /**
   * Tool code filter (to)
   */
  ma_cc_den?: string;

  /**
   * Additional search parameters
   */
  [key: string]: any;
}

/**
 * Interface for useCCDCInventory hook return type
 */
export interface UseCCDCInventoryReturn {
  /**
   * Array of CCDC inventory items
   */
  data: CCDCInventoryItem[];

  /**
   * Loading state
   */
  isLoading: boolean;

  /**
   * Error state
   */
  error: Error | null;

  /**
   * Function to fetch data with search parameters
   */
  fetchData: (searchParams: CCDCInventorySearchFormValues) => Promise<void>;

  /**
   * Function to refresh data
   */
  refreshData: () => Promise<void>;
}
