import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { CTTangGiamTSCDItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

// Extended interface for table display with total row support
interface CTTangGiamTSCDTableItem extends CTTangGiamTSCDItem {
  isTotal?: boolean;
}

export function useTableData(data: CTTangGiamTSCDItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const totalNguyenGia = data.reduce((sum, item) => sum + (item.nguyen_gia || 0), 0);
    const totalGtDaKh = data.reduce((sum, item) => sum + (item.gt_da_kh || 0), 0);
    const totalGtCl = data.reduce((sum, item) => sum + (item.gt_cl || 0), 0);

    const totalRow: CTTangGiamTSCDTableItem = {
      id: 'total',
      ma_ts: '',
      ten_ts: `Tổng cộng`,
      ngay_mua: '',
      so_ky_kh: 0,
      ten_bp: '',
      ten_lts: '',
      ten_tg_ts: '',
      nguyen_gia: totalNguyenGia,
      gt_da_kh: totalGtDaKh,
      gt_cl: totalGtCl,
      isTotal: true
    };

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_ts',
            headerName: 'Mã tài sản',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_ts || '';
            }
          },
          {
            field: 'ten_ts',
            headerName: 'Tên tài sản',
            width: 250
          },
          {
            field: 'ngay_mua',
            headerName: 'Ngày mua',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ngay_mua || '';
            }
          },
          {
            field: 'so_ky_kh',
            headerName: 'Số kỳ khấu hao',
            width: 130,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              return params.row.so_ky_kh || 0;
            }
          },
          {
            field: 'ten_bp',
            headerName: 'Bộ phận',
            width: 150,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_bp || '';
            }
          },
          {
            field: 'ten_lts',
            headerName: 'Loại tài sản',
            width: 150,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_lts || '';
            }
          },
          {
            field: 'ten_tg_ts',
            headerName: 'Lý do',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_tg_ts || '';
            }
          },
          {
            field: 'nguyen_gia',
            headerName: 'Nguyên giá',
            width: 130,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.nguyen_gia || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_da_kh',
            headerName: 'Gt đã khấu hao',
            width: 140,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_da_kh || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_cl',
            headerName: 'Gt còn lại',
            width: 130,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_cl || 0;
              return value.toLocaleString('vi-VN');
            }
          }
        ],
        rows: [totalRow, ...data]
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    // Don't handle clicks on total rows
    if (params.row.isTotal) return;

    console.log('CTTangGiamTSCD row clicked:', {
      assetCode: params.row.ma_ts,
      assetName: params.row.ten_ts,
      purchaseDate: params.row.ngay_mua,
      depreciationPeriods: params.row.so_ky_kh,
      department: params.row.ten_bp,
      assetType: params.row.ten_lts,
      reason: params.row.ten_tg_ts,
      originalValue: params.row.nguyen_gia,
      depreciatedValue: params.row.gt_da_kh,
      remainingValue: params.row.gt_cl,
      fullData: params.row
    });
  };

  return {
    tables,
    handleRowClick
  };
}
