import { <PERSON>Text, Lock, Printer, RefreshCw, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';
import { SearchFormValues } from '../schema';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  searchParams?: SearchFormValues | null;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  searchParams,
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportClick,
  onEditPrintTemplateClick
}) => {
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Bảng cân đối kế toán theo đơn vị</h1>
          {searchParams?.date && (
            <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
              <div className='mr-2 size-2 rounded-full bg-red-500' />
              Ngày {formatDate(searchParams.date)}
            </span>
          )}
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}
      {onPrintClick && (
        <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} disabled={isViewDisabled} />
      )}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Lock} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
      )}
      {onExportClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
      )}

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
