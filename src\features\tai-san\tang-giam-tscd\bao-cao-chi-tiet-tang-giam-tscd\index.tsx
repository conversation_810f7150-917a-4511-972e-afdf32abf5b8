'use client';

import { useState } from 'react';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useTableData, useCTTangGiamTSCD } from './hooks';
import { InitialSearchDialog, ActionBar } from './components';

export default function TheTSCD() {
  const { initialSearchDialogOpen, showTable, handleInitialSearchClose, handleInitialSearch, handleSearchClick } =
    useDialogState();

  const [subTitle, setSubTitle] = useState<string>('');
  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useCTTangGiamTSCD(searchParams);
  const { tables, handleRowClick } = useTableData(data);

  const handleSearchWithData = async (values: any) => {
    console.log('Search executed with data:', values);
    setSearchParams(values);
    setSubTitle(
      `Từ kỳ ${values.tu_ky || ''} năm ${values.tu_nam || ''} đến kỳ ${values.den_ky || ''} năm ${values.den_nam || ''}`
    );
    handleInitialSearch(values, fetchData);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={() => {}}
            className='border-b border-gray-200'
            subTitle={subTitle}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
