import React, { useState } from 'react';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import { getInputTableColumns } from '../../cols-definition';
import { EditPrintTemplateTableType } from '../../schemas';
import { FormMode } from '@/types/form';

function ColumnPrintTab() {
  const [formMode, setFormMode] = useState<FormMode>('view');
  return (
    <AritoInputTable<EditPrintTemplateTableType>
      value={[]}
      columns={getInputTableColumns()}
      mode={formMode}
      tableActionButtons={['add', 'delete', 'copy', 'moveUp', 'moveDown', 'pin', 'export', 'paste']}
    />
  );
}

export default ColumnPrintTab;
