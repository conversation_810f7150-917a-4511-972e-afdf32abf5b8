import React, { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { getDataTableColumns } from '../cols-definition';
import { InvoiceDebtItem } from './useInvoiceDebtData';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: InvoiceDebtItem[] = []): UseTableDataReturn {
  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: getDataTableColumns(),
      rows: []
    }
  ]);

  // Initialize with sample data
  React.useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables];
      updatedTables[0].rows = [
        {
          Stt: 1,
          'Ngày c/từ': '15/04/2025',
          'Số c/từ': 'CT001',
          '<PERSON><PERSON><PERSON> hóa đơn': '10/04/2025',
          '<PERSON><PERSON> hóa đơn': 'HD001',
          '<PERSON><PERSON> khách hàng': 'KH001',
          'Tên khách hàng': 'Công ty TNHH ABC',
          'Mã NVBH': 'NV001',
          'Tên NVBH': 'Nguyễn Văn A',
          'Tổng tiền HĐ': 1000000,
          'Đã thu': 500000,
          'Phải thu': 500000,
          'Trong hạn': 500000,
          'Quá hạn 1 - 45 ngày': 0,
          'Quá hạn 46 - 90 ngày': 0,
          'Quá hạn 91 - 135 ngày': 0,
          'Quá hạn 136 - 180 ngày': 0,
          'Quá hạn trên 181 ngày': 0,
          'Ngày đến hạn': '30/04/2025',
          'Hạn tt': '30/04/2025',
          'Số ngày đến hạn': 15,
          'Diễn giải': 'Thanh toán đợt 1',
          'Đơn vị': 'VNĐ'
        },
        {
          Stt: 2,
          'Ngày c/từ': '16/04/2025',
          'Số c/từ': 'CT002',
          'Ngày hóa đơn': '05/03/2025',
          'Số hóa đơn': 'HD002',
          'Mã khách hàng': 'KH002',
          'Tên khách hàng': 'Doanh nghiệp XYZ',
          'Mã NVBH': 'NV002',
          'Tên NVBH': 'Trần Thị B',
          'Tổng tiền HĐ': 1500000,
          'Đã thu': 1500000,
          'Phải thu': 0,
          'Trong hạn': 0,
          'Quá hạn 1 - 45 ngày': 0,
          'Quá hạn 46 - 90 ngày': 0,
          'Quá hạn 91 - 135 ngày': 0,
          'Quá hạn 136 - 180 ngày': 0,
          'Quá hạn trên 181 ngày': 0,
          'Ngày đến hạn': '20/03/2025',
          'Hạn tt': '20/03/2025',
          'Số ngày đến hạn': -29,
          'Diễn giải': 'Đã thanh toán toàn bộ',
          'Đơn vị': 'VNĐ'
        },
        {
          Stt: 3,
          'Ngày c/từ': '17/04/2025',
          'Số c/từ': 'CT003',
          'Ngày hóa đơn': '01/02/2025',
          'Số hóa đơn': 'HD003',
          'Mã khách hàng': 'KH003',
          'Tên khách hàng': 'Cửa hàng 123',
          'Mã NVBH': 'NV001',
          'Tên NVBH': 'Nguyễn Văn A',
          'Tổng tiền HĐ': 800000,
          'Đã thu': 300000,
          'Phải thu': 500000,
          'Trong hạn': 0,
          'Quá hạn 1 - 45 ngày': 0,
          'Quá hạn 46 - 90 ngày': 500000,
          'Quá hạn 91 - 135 ngày': 0,
          'Quá hạn 136 - 180 ngày': 0,
          'Quá hạn trên 181 ngày': 0,
          'Ngày đến hạn': '16/03/2025',
          'Hạn tt': '16/03/2025',
          'Số ngày đến hạn': -33,
          'Diễn giải': 'Nợ quá hạn 63 ngày',
          'Đơn vị': 'VNĐ'
        }
      ];
      return updatedTables;
    });
  }, []);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,

    handleRowClick
  };
}
