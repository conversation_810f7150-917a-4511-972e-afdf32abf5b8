'use client';

import React from 'react';
import { EditPrintTemplateDialog } from '@/components/custom/arito/edit-print-template';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { InitialSearchDialog, ActionBar } from './components';
import { initialValues } from './schema';

export default function BangKeHoaDonChungTuHangHoaDichVuMuaVaoPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const { tables, handleRowClick } = useTableData(searchParams || initialValues);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} />
          </div>
        </>
      )}
    </div>
  );
}
