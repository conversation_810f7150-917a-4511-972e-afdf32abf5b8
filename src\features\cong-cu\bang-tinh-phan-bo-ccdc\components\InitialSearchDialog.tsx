import React, { useState } from 'react';
import { AritoForm, AritoDialog, AritoHeaderTabs, BottomBar, AritoIcon } from '@/components/custom/arito';
import { searchSchema, initialValues, SearchFormValues } from '../schema';
import { DetailsTab, OtherTab, BasicInfo } from './tabs';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
  setDates: (dates: {
    fromDate: {
      tu_ki: string;
      tu_nam: string;
    };
    toDate: {
      den_ki: string;
      den_nam: string;
    };
  }) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch, setDates }) => {
  const searchFieldStates = useSearchFieldStates();
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');

  const handleSubmit = (data: SearchFormValues) => {
    const combinedData = {
      ...data,
      ma_cc: searchFieldStates.selectedTool?.ma_cc || '',
      ma_loai_cc: searchFieldStates.selectedToolType?.ma_lts || '',
      ma_bp: searchFieldStates.selectedDepartment?.ma_bp || '',
      nh_cc1: searchFieldStates.selectedToolGroup1?.ma_nhom || '',
      nh_cc2: searchFieldStates.selectedToolGroup2?.ma_nhom || '',
      nh_cc3: searchFieldStates.selectedToolGroup3?.ma_nhom || ''
    };
    setDates({
      fromDate: {
        tu_ki: combinedData.tu_ky || '',
        tu_nam: combinedData.tu_nam || ''
      },
      toDate: {
        den_ki: combinedData.den_ky || '',
        den_nam: combinedData.den_nam || ''
      }
    });

    onSearch(combinedData);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng tính phân bổ CCDC'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo
              searchFieldStates={{
                selectedTool: searchFieldStates.selectedTool,
                setSelectedTool: searchFieldStates.setSelectedTool
              }}
            />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: (
                    <DetailsTab
                      formMode={formMode}
                      searchFieldStates={{
                        selectedToolType: searchFieldStates.selectedToolType,
                        setSelectedToolType: searchFieldStates.setSelectedToolType,
                        selectedDepartment: searchFieldStates.selectedDepartment,
                        setSelectedDepartment: searchFieldStates.setSelectedDepartment,
                        selectedToolGroup1: searchFieldStates.selectedToolGroup1,
                        setSelectedToolGroup1: searchFieldStates.setSelectedToolGroup1,
                        selectedToolGroup2: searchFieldStates.selectedToolGroup2,
                        setSelectedToolGroup2: searchFieldStates.setSelectedToolGroup2,
                        selectedToolGroup3: searchFieldStates.selectedToolGroup3,
                        setSelectedToolGroup3: searchFieldStates.setSelectedToolGroup3
                      }}
                    />
                  )
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
        classNameBottomBar='relative flex w-full justify-end gap-2'
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
