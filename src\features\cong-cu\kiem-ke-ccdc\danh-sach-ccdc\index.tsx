'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState, useCallback } from 'react';
import {
  AritoForm,
  AritoDataTables,
  AritoIcon,
  AritoHeaderTabs,
  AritoDialog,
  BottomBar,
  LoadingOverlay
} from '@/components/custom/arito';
import { EditPrintTemplateDialog, ActionBar, BasicInfoTab, DetailTab, OtherTab } from './components';
import { useCCDCInventory, useTableData, useSearchFieldStates } from './hooks';
import { CCDCInventorySearchFormValues } from '@/types/schemas';
import { CCDCSchema, initialValues } from './schema';

export default function DanhSachCCDCPage() {
  const [showForm, setShowForm] = useState(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [showTable, setShowTable] = useState(false);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(initialValues);
  const [searchParams, setSearchParams] = useState<CCDCInventorySearchFormValues>({});
  const searchFieldStates = useSearchFieldStates();
  const [date, setDate] = useState<Date | null>(null);

  const { data: ccdcData, isLoading, error, fetchData } = useCCDCInventory(searchParams);

  // Table data hook
  const { tables } = useTableData(ccdcData);

  const handleRowClick = (params: GridRowParams) => {
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = useCallback(
    async (data: CCDCSchema) => {
      const searchFields: CCDCInventorySearchFormValues = {
        den_ngay: data.den_ngay?.toISOString().split('T')[0],
        loai: data.loai,
        ma_bp: data.ma_bp,
        mau_bc: data.mau_bc,
        report_filter_template: data.report_filter_template,
        data_analysis_struct: data.data_analysis_struct,
        nh1_ma_nhom: searchFieldStates.toolGroup1?.ma_nhom,
        nh2_ma_nhom: searchFieldStates.toolGroup2?.ma_nhom,
        nh3_ma_nhom: searchFieldStates.toolGroup3?.ma_nhom
      };
      setDate(data.den_ngay);
      setSearchParams(searchFields);
      setCurrentObj(data);

      await fetchData(searchFields);

      setShowTable(true);
      setShowForm(false);
    },
    [searchFieldStates, fetchData]
  );

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleSearchClick = () => {
    setShowForm(true);
  };

  const handleRefreshClick = () => {
    console.log('Refresh clicked');
    // TODO: Implement refresh logic
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
    // TODO: Implement fixed columns logic
  };

  const handleExportDataClick = () => {
    console.log('Export data clicked');
    // TODO: Implement export data logic
  };

  const handleEditPrintTemplateClick = () => {
    setShowEditPrintTemplateDialog(true);
    // TODO: Implement edit print template logic
  };

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      {showEditPrintTemplateDialog && (
        <EditPrintTemplateDialog
          open={showEditPrintTemplateDialog}
          onClose={() => setShowEditPrintTemplateDialog(false)}
          onSave={() => {}}
        />
      )}

      <AritoDialog
        open={showForm}
        onClose={handleCloseForm}
        title='Báo cáo danh sách CCDC'
        maxWidth='lg'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm<any>
          mode={formMode}
          hasAritoActionBar={false}
          schema={CCDCSchema}
          initialData={currentObj}
          onSubmit={handleFormSubmit}
          className='w-full'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
              <BasicInfoTab />

              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'detail',
                    label: 'Chi tiết',
                    component: <DetailTab formMode={formMode} searchFieldStates={searchFieldStates} />
                  },
                  {
                    id: 'other',
                    label: 'Khác',
                    component: <OtherTab />
                  }
                ]}
              />
            </div>
          }
          classNameBottomBar='relative flex gap-2 w-full'
          bottomBar={<BottomBar mode={formMode} onClose={handleCloseForm} />}
        />
      </AritoDialog>

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            date={date}
          />
          <div className='w-full overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && (
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
