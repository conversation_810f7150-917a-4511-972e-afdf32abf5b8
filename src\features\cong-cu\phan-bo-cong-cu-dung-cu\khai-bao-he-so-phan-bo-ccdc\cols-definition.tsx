import { GridColDef } from '@mui/x-data-grid';
import { Checkbox } from '@mui/material';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const AllocCoeffColumns: GridColDef[] = [
  { field: 'ma_cong_cu', headerName: 'Mã công cụ', width: 150 },
  { field: 'ten_cong_cu', headerName: 'Tên công cụ', width: 300 },
  { field: 'he_so', headerName: 'Hệ số', width: 100 },
  { field: 'tai_khoan_phan_bo', headerName: 'Tài khoản phân bổ', width: 100 },
  { field: 'tai_khoan_chi_phi', headerName: 'Tài khoản chi phí', width: 100 },
  { field: 'bo_phan_su_dung', headerName: 'Bộ phận sử dụng', width: 150 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'V<PERSON> việc', width: 100 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 100 },
  { field: 'ma_phi', headerName: 'Mã phí', width: 100 }
];

export const ConstraintColumns: GridColDef[] = [
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 100 },
  {
    field: 'bat_buoc_nhap',
    headerName: 'Bắt buộc nhập',
    width: 120,
    renderCell: params => <Checkbox checked={params.value} />
  }
];

export const DependentColumns: GridColDef[] = [
  { field: 'ho_ten', headerName: 'Họ tên', width: 200 },
  { field: 'ngay_sinh', headerName: 'Ngày sinh', width: 100 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 100 },
  { field: 'loai_giay_to', headerName: 'Loại giấy tờ', width: 100 },
  { field: 'so_giay_to', headerName: 'Số giấy tờ', width: 150 }
];

export const ProgressColumns: GridColDef[] = [
  { field: 'ma_dot', headerName: 'Mã đợt', width: 100 },
  { field: 'ten_dot', headerName: 'Tên đợt thanh toán', width: 250 },
  { field: 'ngay_thanh_toan', headerName: 'Ngày t/toán', width: 100 },
  { field: 'ty_le', headerName: 'Tỷ lệ(%)', width: 100 },
  { field: 'gia_tri', headerName: 'Giá trị', width: 100 },
  { field: 'tien_hach_toan', headerName: 'Tiền hạch toán', width: 100 },
  { field: 'han_thanh_toan', headerName: 'Hạn thanh toán', width: 100 },
  { field: 'ghi_chu', headerName: 'Ghi chú', width: 300 }
];

const baseColumns = (idHeader: string, nameHeader: string): ExtendedGridColDef[] => [
  { field: 'id', headerName: idHeader, flex: 1 },
  { field: 'name', headerName: nameHeader, flex: 1 }
];
export const toolSearchColumns = baseColumns('Mã công cụ', 'Tên công cụ');
export const toolSectionSearchColumns = baseColumns('Mã bộ phận', 'Tên bộ phận');
export const sectionSearchColumns = baseColumns('Mã bộ phận', 'Tên bộ phận');
export const caseSearchColumns = baseColumns('Mã vụ việc', 'Tên vụ việc');
export const feeSearchColumns = baseColumns('Mã phí', 'Tên phí');
export const contractSearchColumns = baseColumns('Mã hợp đồng', 'Tên hợp đồng');
export const forexSearchColumns = baseColumns('Mã ngoại tệ', 'Tên ngoại tệ');
export const staffSearchColumns = baseColumns('Mã nhân viên', 'Tên nhân viên');
export const groupSearchColumns = baseColumns('Mã nhóm', 'Tên nhóm');
export const paymentTypeSearchColumns = baseColumns('Mã phương thức', 'Tên phương thức');
export const regionSearchColumns = baseColumns('Mã khu vực', 'Tên khu vực');
export const provinceSearchColumns = baseColumns('Mã tỉnh/thành', 'Tên tỉnh/thành');

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account', headerName: 'Tên tài khoản', flex: 1 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 1 },
  {
    field: 'ledger_account',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'detail_account',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'level', headerName: 'Bậc tk', flex: 1 }
];

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'name', headerName: 'Tên đối tượng', flex: 1 },
  { field: 'receivable_debt', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'payable_debt', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 1 }
];

export const debtPaymentSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã thanh toán', flex: 1 },
  { field: 'name', headerName: 'Tên thanh toán', flex: 1 },
  { field: 'payment_term', headerName: 'Hạn thanh toán', flex: 1 }
];

export const usernameSearchColumns: ExtendedGridColDef[] = [
  { field: 'username', headerName: 'Tên', flex: 1 },
  { field: 'name', headerName: 'Tên đầy đủ', flex: 1 },
  { field: 'manager', headerName: 'Cấp trên', flex: 1 },
  { field: 'id', headerName: 'ID', flex: 1 }
];

export const CustomerInfoColumns: ExtendedGridColDef[] = [
  { field: 'he_thong', headerName: 'Hệ thống', width: 100, checkboxSelection: true },
  { field: 'su_dung', headerName: 'Sử dụng', width: 100, renderCell: params => <Checkbox checked={params.value} /> },
  { field: 'ten_thong_so', headerName: 'Tên thông số', width: 150 },
  { field: 'ten_thong_so_2', headerName: 'Tên 2', width: 150 },
  { field: 'ma_cot', headerName: 'Mã cột', width: 150 },
  { field: 'kieu_du_lieu', headerName: 'Kiểu dữ liệu', width: 150 },
  { field: 'gia_tri_mac_dinh', headerName: 'Giá trị mặc định', width: 120 },
  { field: 'do_rong_dien_giai', headerName: 'Độ rộng diễn giải', width: 150 },
  { field: 'do_rong_tri_gia', headerName: 'Độ rộng trị giá', width: 150 },
  { field: 'do_cao', headerName: 'Độ cao', width: 150 },
  { field: 'ghi_chu', headerName: 'Ghi chú', width: 200 }
];
