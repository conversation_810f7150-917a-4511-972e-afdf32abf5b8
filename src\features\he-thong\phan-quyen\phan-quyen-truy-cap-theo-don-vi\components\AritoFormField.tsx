'use client';

import {
  <PERSON>,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { FullscreenIcon, MinimizeIcon } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { GridColDef } from '@mui/x-data-grid';
import { Controller } from 'react-hook-form';
import dayjs from 'dayjs';
import { AritoFormContext } from '@/components/arito/arito-form/arito-form';
import { AritoInputTable } from '@/components/arito/arito-input-table';
import { SearchTable } from '@/components/custom/arito/search-table';
import AritoIcon from '@/components/custom/arito/icon';
import { useDebounce } from '@/hooks/use-debounce';

export const FormField = ({
  label,
  name,
  type = 'text',
  disabled = false,
  options,
  error,
  withSearch,
  withDate = false,
  className,
  columns,
  searchEndpoint,
  searchResultLabelKey,
  searchResultValueKey,
  searchColumns,
  defaultSearchColumn,
  displayRelatedField,
  defaultValue,
  labelWidth = '100px',
  inputWidth = '1fr'
}: {
  label?: string;
  name: string;
  control?: any;
  type?: 'text' | 'number' | 'select' | 'checkbox' | 'date' | 'table';
  disabled?: boolean;
  options?: { value: any; label: string }[];
  error?: string;
  withSearch?: boolean;
  withDate?: boolean;
  className?: string;
  columns?: any[];
  defaultValue?: any;
  searchEndpoint?: string;
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  searchColumns?: GridColDef[];
  defaultSearchColumn?: string;
  displayRelatedField?: string;
  labelWidth?: string;
  inputWidth?: string;
}) => {
  const { control, errors, isViewMode } = useContext(AritoFormContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fieldError = error || errors?.[name]?.message;
  const [searchDialogOpen, setSearchDialogOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSearchResult, setSelectedSearchResult] = useState<any>(null);
  const [displayText, setDisplayText] = useState<string>(defaultValue);
  const [relatedFieldValue, setRelatedFieldValue] = useState<string>('');
  const [autoSelectMode, setAutoSelectMode] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);

  // Use name as the id
  const fieldId = name;

  // Auto-select first option for select fields if value is not set
  useEffect(() => {
    if (type === 'select' && options && options.length > 0) {
      const fieldValue = control._formValues[name];
      // Check if value is not set (undefined, null, or empty string)
      if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
        // Set the first option as the default value
        control._formState[name] = options[0].value;
      }
    }
  }, [type, options, name, control]);

  useEffect(() => {
    // This effect initializes the displayText if we have an initial form value
    // and a searchEndpoint to fetch the display text
    const getInitialDisplayText = async () => {
      if (!searchEndpoint) return;

      // Determine which keys to use, falling back to defaultSearchColumn if needed
      const valueKey = searchResultValueKey || defaultSearchColumn || 'name';
      const labelKey = searchResultLabelKey || defaultSearchColumn || 'name';

      // Check if we have a stored value to find in our search results
      const initialFormValues = control._formValues;
      const initialValue = initialFormValues[name];

      if (!initialValue) return;

      try {
        // Try to find the item in existing results first
        if (searchResults.length > 0) {
          const matchingResult = searchResults.find(result => result[valueKey] === initialValue);

          if (matchingResult) {
            setDisplayText(matchingResult[labelKey]);
            // Also set the related field value if specified
            if (displayRelatedField && matchingResult[displayRelatedField]) {
              setRelatedFieldValue(matchingResult[displayRelatedField]);
            }
            return;
          }
        }

        // If not found and we have a searchEndpoint, fetch the specific item
        const docType = searchEndpoint.split('/').pop();
        if (!docType) return;

        // Placeholder implementation - ERPNext functionality removed
      } catch (error) {
        console.error('Error fetching initial display text:', error);
      }
    };

    getInitialDisplayText();
  }, [
    control._formValues,
    name,
    searchEndpoint,
    searchResultLabelKey,
    searchResultValueKey,
    searchResults,
    defaultSearchColumn,
    displayRelatedField
  ]);

  const handleSearchClick = () => {
    // Auto set fullscreen on mobile when opening search dialog
    if (isMobile) {
      setIsFullScreen(true);
    }
    setSearchDialogOpen(true);

    if (searchEndpoint) {
      fetchSearchResults('');
    }
  };

  const fetchSearchResults = async (query = '') => {
    if (!searchEndpoint) return;

    setIsLoading(true);
    try {
      const docType = searchEndpoint.split('/').pop();

      if (!docType) {
        throw new Error('Invalid searchEndpoint format');
      }

      // Placeholder implementation - ERPNext functionality removed
      setSearchResults([]);
    } catch (error) {
      console.error('Error fetching search results:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = () => {
    fetchSearchResults(searchQuery);
  };

  const handleSelectSearchResult = (result: any, onChange: (value: any) => void) => {
    // Determine which keys to use, falling back to defaultSearchColumn if needed
    const valueKey = searchResultValueKey || defaultSearchColumn || 'name';
    const labelKey = searchResultLabelKey || defaultSearchColumn || 'name';

    // Store the actual value in the form
    const actualValue = result[valueKey];

    // If the expected key is not found, fall back to 'name' or first available property
    if (actualValue === undefined) {
      // Try to fallback to a suitable property (name, id, or first property)
      const objectKeys = Object.keys(result);
      const fallbackValue = result.name || result.id || (objectKeys.length > 0 ? result[objectKeys[0]] : undefined);

      onChange(fallbackValue);
      setDisplayText(result[labelKey] || String(fallbackValue));
    } else {
      onChange(actualValue);
      // Update the display text separately - this is just for UI
      setDisplayText(result[labelKey] !== undefined ? result[labelKey] : String(actualValue));
    }

    // Set related field value if specified
    if (displayRelatedField && result[displayRelatedField]) {
      setRelatedFieldValue(result[displayRelatedField]);
    } else {
      setRelatedFieldValue('');
    }

    setSearchDialogOpen(false);
  };

  const handleRowSelection = (selectedRows: any[], onChange: (value: any) => void) => {
    if (selectedRows.length > 0) {
      const selectedRow = selectedRows[0];
      setSelectedSearchResult(selectedRow);
    } else {
      setSelectedSearchResult(null);
    }
  };

  const handleConfirmSelection = (onChange: (value: any) => void) => {
    if (selectedSearchResult) {
      handleSelectSearchResult(selectedSearchResult, onChange);
    }
  };

  const handleSelectRow = (result: any, onChange: (value: any) => void) => {
    setSelectedSearchResult(result);
  };

  useEffect(() => {
    if (searchDialogOpen && searchEndpoint) {
      fetchSearchResults(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, searchDialogOpen, searchEndpoint]);

  // Add a function to toggle fullscreen mode
  const toggleFullScreen = () => {
    setIsFullScreen(prevState => !prevState);
  };

  return (
    <div>
      <Box
        className={`form-group ${className || ''}`}
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr', // Single column on mobile
            sm: type === 'table' ? '1fr' : !label ? `0px ${inputWidth}` : `${labelWidth} ${inputWidth}` // Use customizable widths
          },
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: 1,
          mb: { xs: 2, sm: 1 }
        }}
      >
        {type !== 'checkbox' && (
          <Typography
            variant='body2'
            component='label'
            htmlFor={fieldId}
            sx={{
              display: type === 'table' || !label ? 'none' : 'flex',
              alignItems: 'center',
              color: '#000000',
              minWidth: 'fit-content',
              fontSize: '0.75rem',
              width: { xs: '100%', sm: 'auto' },
              marginBottom: { xs: '8px', sm: 0 },
              textAlign: { xs: 'left', sm: 'left' }
            }}
          >
            {label}
          </Typography>
        )}

        <Box
          sx={{
            width: '100%',
            gridColumn: type === 'table' || !label ? '1 / -1' : 'auto'
          }}
        >
          <Controller
            name={name}
            control={control}
            defaultValue={defaultValue || ''}
            render={({ field }) => {
              // The search-related fields will have special handling
              if (withSearch && (type === 'text' || type === 'number')) {
                return (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      width: '100%',
                      flexDirection: {
                        xs: 'column',
                        sm: 'row'
                      }
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        flexShrink: 0,
                        width: '100%',
                        maxWidth: {
                          xs: '100%',
                          sm: displayRelatedField ? '33%' : '100%'
                        }
                      }}
                    >
                      <TextField
                        // We don't spread all field props to avoid value conflicts
                        name={field.name}
                        ref={field.ref}
                        onBlur={field.onBlur}
                        type={type}
                        disabled={disabled}
                        fullWidth
                        size='small'
                        variant='standard'
                        id={fieldId}
                        // Display defaultValue or display text for UI
                        value={defaultValue || displayText || field.value || ''}
                        onChange={e => {
                          const newValue = e.target.value;
                          // Update the actual form value
                          field.onChange(newValue);
                          // Also update the display text since the user is typing
                          setDisplayText(newValue);
                          // Clear related field when user types manually
                          setRelatedFieldValue('');
                        }}
                        sx={{
                          '& .MuiInput-root': {
                            fontSize: '14px',
                            '&:before': {
                              borderBottom: '1px solid #e5e7eb'
                            },
                            '&:hover:not(.Mui-disabled):before': {
                              borderBottom: '1px solid #2563EB'
                            },
                            '&.Mui-focused:after': {
                              borderBottom: '1px solid #2563EB'
                            }
                          },
                          '& .MuiInput-input': {
                            padding: '4px 8px',
                            paddingRight: '28px', // Add space for the search icon
                            textAlign: { xs: 'left', sm: 'left' } // Left align text on mobile
                          },
                          marginTop: type === 'number' ? '8px' : '0px'
                        }}
                        InputProps={{
                          endAdornment: (
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'text.secondary',
                                cursor: 'pointer',
                                position: 'absolute',
                                right: 4,
                                '&:hover': {
                                  color: '#2563EB'
                                }
                              }}
                              onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleSearchClick();
                              }}
                            >
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='16'
                                height='16'
                                viewBox='0 0 20 20'
                                fill='#2563EB'
                              >
                                <path
                                  fillRule='evenodd'
                                  d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z'
                                  clipRule='evenodd'
                                />
                              </svg>
                            </Box>
                          )
                        }}
                      />
                    </Box>

                    {/* Display related field value (like supplier name) beside the input */}
                    {relatedFieldValue && (
                      <Typography
                        variant='body2'
                        sx={{
                          marginLeft: { xs: 0, sm: '12px' },
                          marginTop: { xs: '4px', sm: 0 },
                          fontSize: '14px',
                          color: '#374151',
                          maxWidth: { xs: '100%', sm: '65%' },
                          flexGrow: 1,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          textAlign: { xs: 'left', sm: 'left' }, // Left align on mobile
                          width: '100%' // Full width on mobile
                        }}
                      >
                        {relatedFieldValue}
                      </Typography>
                    )}

                    {/* Search Dialog */}
                    <Dialog
                      open={searchDialogOpen}
                      onClose={() => {}} // Empty function to disable closing on backdrop click
                      disableEscapeKeyDown // Disable Esc key to close modal
                      fullScreen={isMobile || isFullScreen} // Auto fullscreen on mobile devices
                      PaperProps={{
                        sx: {
                          borderRadius: '0px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                          overflow: 'hidden',
                          ...(isMobile || isFullScreen
                            ? {
                                height: '100%',
                                width: '100%',
                                maxWidth: '100vw',
                                maxHeight: '100vh',
                                margin: 0
                              }
                            : {
                                height: '450px',
                                width: '850px',
                                maxHeight: '80vh',
                                maxWidth: '90vw'
                              })
                        }
                      }}
                    >
                      <DialogTitle
                        sx={{
                          backgroundColor: '#f2f7fc',
                          color: '#000000',
                          fontWeight: 'bold',
                          fontSize: '18px',
                          py: 1,
                          px: 2,
                          borderBottom: '1px solid #d1d5db',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          minHeight: '40px',
                          height: '40px'
                        }}
                      >
                        <Box
                          sx={{
                            fontSize: '16px',
                            color: '#666666',
                            display: 'flex',
                            alignItems: 'center',
                            marginLeft: '-12px'
                          }}
                        >
                          <AritoIcon icon={584} marginX='8px' />
                          <Typography component='span'>Danh mục {String(label).toLowerCase()}</Typography>
                        </Box>
                        {/* Fullscreen toggle button - hidden on mobile */}
                        {!isMobile && (
                          <IconButton
                            size='small'
                            onClick={toggleFullScreen}
                            title={isFullScreen ? 'Thoát toàn màn hình' : 'Toàn màn hình'}
                            sx={{
                              // color: "#53a3a3",
                              marginLeft: 'auto',
                              padding: '4px'
                            }}
                          >
                            {isFullScreen ? <MinimizeIcon fontSize='small' /> : <FullscreenIcon fontSize='small' />}
                          </IconButton>
                        )}
                      </DialogTitle>
                      <DialogContent
                        sx={{
                          p: 0,
                          display: 'flex',
                          flexDirection: 'column',
                          height: 'calc(100% - 75px)'
                        }}
                      >
                        {!searchEndpoint ? (
                          <Typography color='error' sx={{ py: 2, px: 3 }}>
                            Vui lòng cung cấp searchEndpoint để sử dụng chức năng tìm kiếm.
                          </Typography>
                        ) : (
                          <>
                            <Box
                              sx={{
                                p: 1,
                                borderBottom: '1px solid #e5e7eb',
                                display: 'flex',
                                alignItems: 'center',
                                height: '38px',
                                backgroundColor: '#f9fcfd',
                                justifyContent: 'space-between',
                                marginLeft: '-1%'
                              }}
                            >
                              <TextField
                                variant='outlined'
                                placeholder='Tìm kiếm...'
                                size='small'
                                value={searchQuery}
                                onChange={handleSearchQueryChange}
                                sx={{
                                  width: '300px',
                                  ml: 1,
                                  '& .MuiOutlinedInput-input': {
                                    padding: '4px 8px 4px 12px',
                                    fontSize: '0.8rem',
                                    height: '22px'
                                  },
                                  '& .MuiOutlinedInput-root': {
                                    borderRadius: '0px'
                                  }
                                }}
                                InputProps={{
                                  startAdornment: (
                                    <Box
                                      sx={{
                                        color: 'text.secondary',
                                        display: 'flex',
                                        alignItems: 'center',
                                        mr: 0.5
                                      }}
                                    >
                                      <svg
                                        xmlns='http://www.w3.org/2000/svg'
                                        width='14'
                                        height='14'
                                        viewBox='0 0 20 20'
                                        fill='#2563EB'
                                      >
                                        <path
                                          fillRule='evenodd'
                                          d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z'
                                          clipRule='evenodd'
                                        />
                                      </svg>
                                    </Box>
                                  )
                                }}
                              />

                              <Typography
                                variant='caption'
                                sx={{
                                  color: 'text.secondary',
                                  mr: 2,
                                  ml: 1,
                                  fontSize: '0.7rem'
                                }}
                              >
                                Nhấp đúp để chọn ngay
                              </Typography>
                            </Box>

                            {isLoading ? (
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'center',
                                  p: 3
                                }}
                              >
                                <CircularProgress size={24} />
                              </Box>
                            ) : (
                              <Box
                                sx={{
                                  flexGrow: 1,
                                  overflowY: 'auto',
                                  height: '100%',
                                  maxHeight: 'calc(100% - 40px)',
                                  '& .MuiDataGrid-root': {
                                    border: 'none',
                                    height: '100% !important'
                                  }
                                }}
                              >
                                {/* Always render SearchTable, even with empty results */}
                                <SearchTable
                                  searchResults={searchResults}
                                  searchColumns={searchColumns}
                                  searchResultLabelKey={searchResultLabelKey}
                                  searchResultValueKey={searchResultValueKey}
                                  selectedResult={selectedSearchResult}
                                  onResultClick={result => {
                                    setSelectedSearchResult(result);
                                  }}
                                  onResultDoubleClick={result => {
                                    handleSelectSearchResult(result, field.onChange);
                                  }}
                                />
                              </Box>
                            )}
                          </>
                        )}
                      </DialogContent>
                      <DialogActions
                        sx={{
                          borderTop: '1px solid #e5e7eb',
                          py: 1,
                          px: 2,
                          backgroundColor: '#f2f7fc',
                          display: 'flex',
                          justifyContent: 'flex-end',
                          minHeight: '43px',
                          height: '43px'
                        }}
                      >
                        <Button
                          onClick={() => handleConfirmSelection(field.onChange)}
                          variant='contained'
                          disabled={!selectedSearchResult}
                          sx={{
                            backgroundColor: '#2563EB',
                            '&:hover': {
                              backgroundColor: '#1E40AF'
                            },
                            '&.Mui-disabled': {
                              backgroundColor: '#94a3b8',
                              color: 'white'
                            },
                            textTransform: 'none',
                            fontWeight: 'normal',
                            borderRadius: '2px',
                            padding: '3px 10px',
                            minWidth: '80px',
                            fontSize: '0.8rem'
                          }}
                        >
                          <AritoIcon icon={884} marginX='4px' />
                          Đồng ý
                        </Button>
                        <Button
                          onClick={() => setSearchDialogOpen(false)}
                          variant='outlined'
                          sx={{
                            color: '#2563EB',
                            borderColor: '#2563EB',
                            borderRadius: '2px',
                            '&:hover': {
                              borderColor: '#1E40AF',
                              backgroundColor: 'rgba(37, 99, 235, 0.1)'
                            },
                            textTransform: 'none',
                            fontWeight: 'normal',
                            minWidth: '80px',
                            padding: '3px 10px',
                            fontSize: '0.8rem',
                            ml: 1
                          }}
                        >
                          <AritoIcon icon={885} marginX='4px' />
                          Hủy
                        </Button>
                      </DialogActions>
                    </Dialog>
                  </Box>
                );
              }

              // Other field types remain unchanged
              switch (type) {
                case 'table':
                  return (
                    <div className='h-[300px] w-screen'>
                      <AritoInputTable
                        value={field.value ? field.value : []}
                        columns={columns || []}
                        onChange={field.onChange}
                        mode={disabled ? 'view' : 'edit'}
                      />
                    </div>
                  );
                case 'select':
                  return (
                    <Select
                      {...field}
                      disabled={disabled}
                      size='small'
                      fullWidth
                      id={fieldId}
                      sx={{
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none'
                        },
                        '&.MuiOutlinedInput-root': {
                          borderBottom: '1px solid #e5e7eb',
                          borderRadius: 0,
                          '&:hover': {
                            borderBottom: '1px solid #2563EB'
                          },
                          '&.Mui-focused': {
                            borderBottom: '1px solid #2563EB'
                          }
                        },
                        '& .MuiSelect-select': {
                          padding: '4px 8px',
                          fontSize: '14px'
                        },
                        backgroundColor: 'transparent',
                        position: 'relative',
                        top: '-4px'
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            borderRadius: '4px',
                            marginTop: '4px'
                          }
                        }
                      }}
                    >
                      {options?.map(option => (
                        <MenuItem key={option.value.toString()} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  );
                case 'checkbox':
                  return (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: inputWidth !== '1fr' ? inputWidth : '100%', // Use inputWidth if provided
                        position: 'relative',
                        top: '-4px'
                      }}
                    >
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            disabled={disabled}
                            id={fieldId}
                            title={label}
                            sx={{
                              color: '#d1d5db',
                              '&.Mui-checked': {
                                color: '#2563EB'
                              },
                              '&:hover': {
                                color: disabled ? '#d1d5db' : '#2563EB'
                              }
                            }}
                          />
                        }
                        label={
                          <Typography variant='body2' sx={{ color: '#000000', fontSize: '0.875rem' }}>
                            {label}
                          </Typography>
                        }
                      />
                    </Box>
                  );
                case 'date':
                  return (
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        value={field.value ? dayjs(field.value) : null}
                        onChange={(newValue: dayjs.Dayjs | null) => {
                          field.onChange(newValue ? newValue.format('YYYY-MM-DD') : null);
                        }}
                        disabled={disabled}
                        format='DD/MM/YYYY'
                        localeText={{
                          cancelButtonLabel: 'Hủy',
                          toolbarTitle: 'Chọn ngày',
                          todayButtonLabel: 'Hôm nay',
                          previousMonth: 'Tháng trước',
                          nextMonth: 'Tháng sau'
                        }}
                        slotProps={{
                          textField: {
                            size: 'small',
                            fullWidth: true,
                            variant: 'standard',
                            inputProps: { id: fieldId },
                            sx: {
                              '& .MuiInput-root': {
                                fontSize: '14px',
                                '&:before': {
                                  borderBottom: '1px solid #e5e7eb'
                                },
                                '&:hover:not(.Mui-disabled):before': {
                                  borderBottom: '1px solid #2563EB'
                                },
                                '&.Mui-focused:after': {
                                  borderBottom: '1px solid #2563EB'
                                }
                              },
                              '& .MuiInput-input': {
                                padding: '4px 8px'
                              },
                              '& .MuiInputAdornment-root': {
                                marginRight: '0'
                              },
                              '& .MuiIconButton-root': {
                                padding: '4px'
                              },
                              '& .MuiSvgIcon-root': {
                                color: '#2563EB',
                                marginRight: '8px',
                                fontSize: '18px'
                              },
                              position: 'relative',
                              top: '-4px',
                              marginTop: '8px'
                            }
                          }
                        }}
                      />
                    </LocalizationProvider>
                  );
                default:
                  return (
                    <TextField
                      {...field}
                      type={type}
                      disabled={disabled}
                      fullWidth
                      size='small'
                      variant='standard'
                      id={fieldId}
                      value={defaultValue || field.value || ''}
                      sx={{
                        '& .MuiInput-root': {
                          fontSize: '14px',
                          '&:before': {
                            borderBottom: '1px solid #e5e7eb'
                          },
                          '&:hover:not(.Mui-disabled):before': {
                            borderBottom: '1px solid #2563EB'
                          },
                          '&.Mui-focused:after': {
                            borderBottom: '1px solid #2563EB'
                          }
                        },
                        '& .MuiInput-input': {
                          padding: '4px 8px'
                        },
                        marginTop: type === 'number' ? '8px' : '0px'
                      }}
                    />
                  );
              }
            }}
          />
        </Box>
      </Box>

      {fieldError && (
        <Typography
          variant='body2'
          color='error'
          sx={{
            marginTop: 1,
            marginLeft: { lg: '160px' }
          }}
        >
          {fieldError}
        </Typography>
      )}
    </div>
  );
};
