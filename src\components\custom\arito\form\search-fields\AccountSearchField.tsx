import React from 'react';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface AccountSearchFieldProps {
  name?: string;
  label?: string;
  className?: string;
  labelClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

// Define the search columns for accounts
const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'tk_so_cai', headerName: 'Tk sổ cái', flex: 1 },
  { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', flex: 1, checkboxSelection: true },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const AccountSearchField: React.FC<AccountSearchFieldProps> = ({
  name = 'accountCode',
  label = 'Tài khoản',
  className = 'grid grid-cols-[150px,1fr] items-center',
  labelClassName,
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='text'
        label=''
        searchEndpoint='accounting/accounts'
        searchColumns={accountSearchColumns}
        defaultSearchColumn='ma_tai_khoan'
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default AccountSearchField;
