import { useState, useCallback, useEffect } from 'react';
import {
  PurchaseOrderStatusItem,
  PurchaseOrderStatusResponse,
  SearchFormValues,
  UsePurchaseOrderStatusReturn
} from '../types';
import api from '@/lib/api';

const generateMockData = (): PurchaseOrderStatusItem[] => {
  return [
    {
      syspivot: '',
      sysorder: 1,
      sysprint: '',
      systotal: '',
      id: '1',
      line: 1,
      unit_id: 'CN001',
      ngay_ct: '2024-01-15',
      so_ct: 'DDH2024001',
      ma_kh: 'NCC001',
      status: 'Đang thực hiện',
      ma_ct: 'DDH',
      ma_vt: 'VT001',
      dvt: 'Tấm',
      he_so: 1,
      ngay_giao: '2024-02-15',
      gia: 25000000,
      tien: 250000000,
      so_luong: 10,
      sl_nhap: 8,
      sl_hd: 8,
      sl_dh: 10,
      sl_tl: 0,
      sl_cl: 2,
      ten_kh: 'Công ty TNHH Thép Việt Nam',
      ten_vt: 'Thép tấm A36 dày 10mm',
      ten_ttct: 'Đang thực hiện',
      ma_unit: 'CN001'
    },
    {
      syspivot: '',
      sysorder: 2,
      sysprint: '',
      systotal: '',
      id: '2',
      line: 2,
      unit_id: 'CN001',
      ngay_ct: '2024-01-16',
      so_ct: 'DDH2024002',
      ma_kh: 'NCC002',
      status: 'Hoàn thành',
      ma_ct: 'DDH',
      ma_vt: 'VT002',
      dvt: 'Cái',
      he_so: 1,
      ngay_giao: '2024-02-16',
      gia: 500,
      tien: 5000000,
      so_luong: 10000,
      sl_nhap: 10000,
      sl_hd: 10000,
      sl_dh: 10000,
      sl_tl: 0,
      sl_cl: 0,
      ten_kh: 'Công ty CP Ốc vít Hà Nội',
      ten_vt: 'Ốc vít M8x20',
      ten_ttct: 'Hoàn thành',
      ma_unit: 'CN001'
    },
    {
      syspivot: '',
      sysorder: 3,
      sysprint: '',
      systotal: '',
      id: '3',
      line: 3,
      unit_id: 'CN002',
      ngay_ct: '2024-01-17',
      so_ct: 'DDH2024003',
      ma_kh: 'NCC003',
      status: 'Chờ duyệt',
      ma_ct: 'DDH',
      ma_vt: 'VT003',
      dvt: 'Mét',
      he_so: 1,
      ngay_giao: '2024-02-17',
      gia: 45000,
      tien: 45000000,
      so_luong: 1000,
      sl_nhap: 0,
      sl_hd: 0,
      sl_dh: 1000,
      sl_tl: 0,
      sl_cl: 1000,
      ten_kh: 'Công ty TNHH Điện Miền Bắc',
      ten_vt: 'Dây điện đồng 2.5mm²',
      ten_ttct: 'Chờ duyệt',
      ma_unit: 'CN002'
    },
    {
      syspivot: '',
      sysorder: 4,
      sysprint: '',
      systotal: '',
      id: '4',
      line: 4,
      unit_id: 'CN001',
      ngay_ct: '2024-01-18',
      so_ct: 'DDH2024004',
      ma_kh: 'NCC004',
      status: 'Đang thực hiện',
      ma_ct: 'DDH',
      ma_vt: 'VT004',
      dvt: 'Lít',
      he_so: 1,
      ngay_giao: '2024-02-18',
      gia: 180000,
      tien: 18000000,
      so_luong: 100,
      sl_nhap: 60,
      sl_hd: 60,
      sl_dh: 100,
      sl_tl: 0,
      sl_cl: 40,
      ten_kh: 'Công ty CP Sơn Đông Á',
      ten_vt: 'Sơn chống gỉ màu xám',
      ten_ttct: 'Đang thực hiện',
      ma_unit: 'CN001'
    },
    {
      syspivot: '',
      sysorder: 5,
      sysprint: '',
      systotal: '',
      id: '5',
      line: 5,
      unit_id: 'CN003',
      ngay_ct: '2024-01-19',
      so_ct: 'DDH2024005',
      ma_kh: 'NCC005',
      status: 'Lập chứng từ',
      ma_ct: 'DDH',
      ma_vt: 'VT005',
      dvt: 'Cái',
      he_so: 1,
      ngay_giao: '2024-02-19',
      gia: 250000,
      tien: 12500000,
      so_luong: 50,
      sl_nhap: 0,
      sl_hd: 0,
      sl_dh: 50,
      sl_tl: 0,
      sl_cl: 50,
      ten_kh: 'Công ty TNHH Bearing Việt Nam',
      ten_vt: 'Bearing 6205-2RS',
      ten_ttct: 'Lập chứng từ',
      ma_unit: 'CN003'
    },
    // Summary/Total row
    {
      syspivot: '',
      sysorder: 999,
      sysprint: '',
      systotal: 'TOTAL',
      id: 'total',
      line: 999,
      unit_id: '',
      ngay_ct: '',
      so_ct: '',
      ma_kh: '',
      status: '',
      ma_ct: '',
      ma_vt: '',
      dvt: '',
      he_so: 0,
      ngay_giao: '',
      gia: 0,
      tien: 330500000, // Sum of all tien values
      so_luong: 11160, // Sum of all so_luong values
      sl_nhap: 10068, // Sum of all sl_nhap values
      sl_hd: 10068, // Sum of all sl_hd values
      sl_dh: 11160, // Sum of all sl_dh values
      sl_tl: 0, // Sum of all sl_tl values
      sl_cl: 1092, // Sum of all sl_cl values
      ten_kh: 'TỔNG CỘNG',
      ten_vt: '',
      ten_ttct: '',
      ma_unit: ''
    }
  ];
};

/**
 * Custom hook for managing Purchase Order Status Report data
 *
 * This hook provides functionality to fetch purchase order status data
 * with mock support for testing and development purposes.
 */
export function usePurchaseOrderStatusData(searchParams: SearchFormValues): UsePurchaseOrderStatusReturn {
  const [data, setData] = useState<PurchaseOrderStatusItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<PurchaseOrderStatusResponse>(
        '/mua-hang/bao-cao-mua-hang/bao-cao-tinh-trang-don-hang-mua/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
