import { useState, useCallback, useEffect } from 'react';
import { TaxReportItem, TaxReportResponse, SearchFormValues, UseTaxReportReturn } from '../schema';
import api from '@/lib/api';

const generateMockData = (): TaxReportItem[] => {
  // Regular data rows
  const mockData = Array.from({ length: 10 }, (_, index) => ({
    id: (index + 1).toString(),
    stt: index + 1,
    ngay_hoa_don: new Date(2023, 0, index + 1), // January 1-10, 2023
    so_hoa_don: `HD${1000 + index}`,
    so_chung_tu: `CT${2000 + index}`,
    ngay_chung_tu: new Date(2023, 0, index + 5), // January 5-14, 2023
    ky_hieu: `AA/23E`,
    ma_nha_cung_cap: `NCC${100 + index}`,
    ten_nha_cung_cap: `Nhà cung cấp ${index + 1}`,
    ma_so_thue: `${1000000000 + index * 111111}`,
    dia_chi: `Địa chỉ ${index + 1}, Quận ${(index % 10) + 1}, TP. <PERSON><PERSON>`,
    mat_hang: `Hàng hóa dịch vụ ${index + 1}`,
    tien_hang: 10000000 + index * 1000000,
    thue_suat: '10%',
    tien_thue: (10000000 + index * 1000000) * 0.1,
    tong_tien: (10000000 + index * 1000000) * 1.1,
    ghi_chu: `Ghi chú cho hóa đơn ${index + 1}`,
    tai_khoan_no: '133',
    tai_khoan_co: '331',
    bo_phan: `BP${index + 1}`,
    vu_viec: `VV${index + 1}`,
    hop_dong: `HD${index + 1}`,
    dot_thanh_toan: `ĐTT${index + 1}`,
    khe_uoc: `KU${index + 1}`,
    phi: `P${index + 1}`,
    san_pham: `SP${index + 1}`,
    lenh_san_xuat: `LSX${index + 1}`,
    ma_chung_tu: `MCT${index + 1}`
  }));

  // Calculate totals for summary rows
  const totalTienHang = mockData.reduce((sum, item) => sum + (item.tien_hang as number), 0);
  const totalTienThue = mockData.reduce((sum, item) => sum + (item.tien_thue as number), 0);
  const totalTongTien = mockData.reduce((sum, item) => sum + (item.tong_tien as number), 0);

  // Summary rows
  const summaryRows: TaxReportItem[] = [
    {
      id: 'summary-1',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: 'Tổng cộng',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang,
      thue_suat: '',
      tien_thue: totalTienThue,
      tong_tien: totalTongTien,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-8',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: '',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: '',
      thue_suat: '',
      tien_thue: '',
      tong_tien: '',
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-2',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap:
        '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang * 0.7,
      thue_suat: '',
      tien_thue: totalTienThue * 0.7,
      tong_tien: totalTongTien * 0.7,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-9',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: '',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: '',
      thue_suat: '',
      tien_thue: '',
      tong_tien: '',
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-3',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: 'Tổng',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang * 0.7,
      thue_suat: '',
      tien_thue: totalTienThue * 0.7,
      tong_tien: totalTongTien * 0.7,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-4',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang * 0.3,
      thue_suat: '',
      tien_thue: totalTienThue * 0.3,
      tong_tien: totalTongTien * 0.3,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-10',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: '',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: '',
      thue_suat: '',
      tien_thue: '',
      tong_tien: '',
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-5',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: 'Tổng',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang * 0.7,
      thue_suat: '',
      tien_thue: totalTienThue * 0.7,
      tong_tien: totalTongTien * 0.7,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-6',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang * 0.3,
      thue_suat: '',
      tien_thue: totalTienThue * 0.3,
      tong_tien: totalTongTien * 0.3,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-11',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: '',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: '',
      thue_suat: '',
      tien_thue: '',
      tong_tien: '',
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    },
    {
      id: 'summary-7',
      isSummary: true,
      stt: '',
      ngay_hoa_don: null,
      so_hoa_don: '//',
      so_chung_tu: '//',
      ngay_chung_tu: null,
      ky_hieu: '//',
      ma_nha_cung_cap: '',
      ten_nha_cung_cap: 'Tổng',
      ma_so_thue: '',
      dia_chi: '',
      mat_hang: '',
      tien_hang: totalTienHang * 0.3,
      thue_suat: '',
      tien_thue: totalTienThue * 0.3,
      tong_tien: totalTongTien * 0.3,
      ghi_chu: '',
      tai_khoan_no: '',
      tai_khoan_co: '',
      bo_phan: '',
      vu_viec: '',
      hop_dong: '',
      dot_thanh_toan: '',
      khe_uoc: '',
      phi: '',
      san_pham: '',
      lenh_san_xuat: '',
      ma_chung_tu: ''
    }
  ];

  // Combine regular data with summary rows
  return [...mockData, ...summaryRows];
};

/**
 * Custom hook for managing Tax Report data
 *
 * This hook provides functionality to fetch tax report data
 * with mock support for testing and development purposes.
 */
export function useTaxReportData(searchParams: SearchFormValues): UseTaxReportReturn {
  const [data, setData] = useState<TaxReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<TaxReportResponse>(
        '/thue/bang-ke-thue/bang-ke-hoa-don-chung-tu-hang-hoa-dich-vu-mua-vao/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
