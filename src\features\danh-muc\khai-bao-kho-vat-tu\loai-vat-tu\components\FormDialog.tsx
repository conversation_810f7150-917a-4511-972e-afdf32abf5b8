import { Button } from '@mui/material';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { materialTypeSchema as itemTypeSchema } from '../schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { LoaiVatTu, LoaiVatTuInput } from '@/types/schemas';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { useSearchFieldStates } from '../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { AccountTab } from './AccountTab';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: LoaiVatTuInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: LoaiVatTu;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

export const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FormDialogProps) => {
  const accountStates = useSearchFieldStates();

  const handleSubmit = async (data: any) => {
    try {
      const trangThai = data.trang_thai !== undefined && data.trang_thai !== null ? Number(data.trang_thai) : 1;

      const updatedData = {
        ma_loai_vat_tu: data.ma_loai_vat_tu,
        ten_loai_vat_tu: data.ten_loai_vat_tu,
        ten_khac: data.ten_khac || null,
        trang_thai: trangThai,
        tk_kho: accountStates.warehouseAccount?.uuid || null,
        tk_doanh_thu: accountStates.revenueAccount?.uuid || null,
        tk_gia_von: accountStates.costAccount?.uuid || null
      };

      onSubmit(updatedData);
      onClose();
    } catch (err: any) {
      console.error(err);
    }
  };

  const isCopyMode = formMode === 'add' && initialData !== undefined;

  const title = isCopyMode
    ? 'Sao chép loại vật tư'
    : formMode === 'add'
      ? 'Thêm loại vật tư'
      : formMode === 'edit'
        ? 'Sửa loại vật tư'
        : 'Xem loại vật tư';

  return (
    <AritoDialog
      open={open}
      title={title}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={699} />}
    >
      <AritoForm
        hasAritoActionBar={false}
        mode={formMode}
        initialData={
          isCopyMode
            ? {
                ...initialData,
                uuid: undefined,
                ma_loai_vat_tu: `${initialData?.ma_loai_vat_tu || ''}_copy`
              }
            : formMode === 'add'
              ? { trang_thai: 1 }
              : initialData
        }
        onSubmit={handleSubmit}
        schema={itemTypeSchema}
        headerFields={<BasicInfoTab formMode={formMode} />}
        className='w-[900px]'
        tabs={[
          {
            id: 'Account',
            label: 'Tài khoản',
            component: (
              <AccountTab
                formMode={formMode}
                warehouseAccount={accountStates.warehouseAccount}
                setWarehouseAccount={accountStates.setWarehouseAccount}
                revenueAccount={accountStates.revenueAccount}
                setRevenueAccount={accountStates.setRevenueAccount}
                costAccount={accountStates.costAccount}
                setCostAccount={accountStates.setCostAccount}
                discountAccount={accountStates.discountAccount}
                setDiscountAccount={accountStates.setDiscountAccount}
                promotionAccount={accountStates.promotionAccount}
                setPromotionAccount={accountStates.setPromotionAccount}
                returnAccount={accountStates.returnAccount}
                setReturnAccount={accountStates.setReturnAccount}
                wipAccount={accountStates.wipAccount}
                setWipAccount={accountStates.setWipAccount}
                materialAccount={accountStates.materialAccount}
                setMaterialAccount={accountStates.setMaterialAccount}
              />
            )
          }
        ]}
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            {formMode === 'view' && (
              <BottomBar
                mode={formMode}
                onAdd={onAddButtonClick}
                onEdit={onEditButtonClick}
                onDelete={onDeleteButtonClick}
                onCopy={onCopyButtonClick}
                onSubmit={() => {}}
                onClose={onClose}
              />
            )}

            {formMode !== 'view' && (
              <>
                <Button
                  type='submit'
                  variant='contained'
                  className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
                >
                  <AritoIcon icon={884} className='mx-1' />
                  Đồng ý
                </Button>

                <Button onClick={() => onClose()} variant='outlined'>
                  <AritoIcon icon={885} className='mx-1' />
                  Huỷ
                </Button>
              </>
            )}
          </>
        }
      />
    </AritoDialog>
  );
};

export default FormDialog;
