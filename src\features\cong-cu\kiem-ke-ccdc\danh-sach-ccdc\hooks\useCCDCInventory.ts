import { useState, useCallback } from 'react';
import {
  CCDCInventoryItem,
  CCDCInventoryResponse,
  CCDCInventorySearchFormValues,
  UseCCDCInventoryReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): CCDCInventoryItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_cc: 'CC001',
      ten_cc: '<PERSON><PERSON><PERSON> t<PERSON>h xách tay Dell Latitude 5520',
      nguyen_gia: 25000000,
      gt_da_kh_nt0: 8333333,
      gt_cl: 16666667,
      gt_kh_ky: 2083333,
      ngay_mua: '2023-01-15',
      ngay_kh0: '2023-02-01',
      so_ky_kh: 36,
      ngay_ct: '2024-01-15',
      so_ct: 'CT001',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'IT001',
      nh_cc1: 'CNTT',
      nh_cc2: 'LAPTOP',
      nh_cc3: 'DELL',
      ten_bp: 'Phòng Công nghệ thông tin',
      loai_cc: 'Thiết bị CNTT',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 25000000,
      gt_cl_nt: 16666667,
      gt_kh_ky_nt: 2083333
    },
    {
      id: '2',
      stt: 2,
      ma_cc: 'CC002',
      ten_cc: 'Máy in HP LaserJet Pro M404dn',
      nguyen_gia: 8500000,
      gt_da_kh_nt0: 4250000,
      gt_cl: 4250000,
      gt_kh_ky: 708333,
      ngay_mua: '2023-03-20',
      ngay_kh0: '2023-04-01',
      so_ky_kh: 24,
      ngay_ct: '2024-01-15',
      so_ct: 'CT002',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'HC001',
      nh_cc1: 'CNTT',
      nh_cc2: 'PRINTER',
      nh_cc3: 'HP',
      ten_bp: 'Phòng Hành chính',
      loai_cc: 'Thiết bị văn phòng',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 8500000,
      gt_cl_nt: 4250000,
      gt_kh_ky_nt: 708333
    },
    {
      id: '3',
      stt: 3,
      ma_cc: 'CC003',
      ten_cc: 'Bàn làm việc gỗ công nghiệp',
      nguyen_gia: 3500000,
      gt_da_kh_nt0: 2916667,
      gt_cl: 583333,
      gt_kh_ky: 291667,
      ngay_mua: '2022-06-10',
      ngay_kh0: '2022-07-01',
      so_ky_kh: 24,
      ngay_ct: '2024-01-15',
      so_ct: 'CT003',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'KT001',
      nh_cc1: 'NOI_THAT',
      nh_cc2: 'BAN_GHE',
      nh_cc3: 'GO',
      ten_bp: 'Phòng Kế toán',
      loai_cc: 'Nội thất văn phòng',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 3500000,
      gt_cl_nt: 583333,
      gt_kh_ky_nt: 291667
    },
    {
      id: '4',
      stt: 4,
      ma_cc: 'CC004',
      ten_cc: 'Máy photocopy Canon iR2625i',
      nguyen_gia: 45000000,
      gt_da_kh_nt0: 18750000,
      gt_cl: 26250000,
      gt_kh_ky: 1875000,
      ngay_mua: '2023-05-15',
      ngay_kh0: '2023-06-01',
      so_ky_kh: 48,
      ngay_ct: '2024-01-15',
      so_ct: 'CT004',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'HC001',
      nh_cc1: 'CNTT',
      nh_cc2: 'PHOTOCOPY',
      nh_cc3: 'CANON',
      ten_bp: 'Phòng Hành chính',
      loai_cc: 'Thiết bị văn phòng',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 45000000,
      gt_cl_nt: 26250000,
      gt_kh_ky_nt: 1875000
    },
    {
      id: '5',
      stt: 5,
      ma_cc: 'CC005',
      ten_cc: 'Điều hòa Daikin FTKC35UAVMV',
      nguyen_gia: 12000000,
      gt_da_kh_nt0: 6000000,
      gt_cl: 6000000,
      gt_kh_ky: 500000,
      ngay_mua: '2023-04-10',
      ngay_kh0: '2023-05-01',
      so_ky_kh: 48,
      ngay_ct: '2024-01-15',
      so_ct: 'CT005',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'KT001',
      nh_cc1: 'DIEN_LANH',
      nh_cc2: 'DIEU_HOA',
      nh_cc3: 'DAIKIN',
      ten_bp: 'Phòng Kế toán',
      loai_cc: 'Thiết bị điện lạnh',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 12000000,
      gt_cl_nt: 6000000,
      gt_kh_ky_nt: 500000
    },
    {
      id: '6',
      stt: 6,
      ma_cc: 'CC006',
      ten_cc: 'Ghế xoay văn phòng cao cấp',
      nguyen_gia: 2500000,
      gt_da_kh_nt0: 1875000,
      gt_cl: 625000,
      gt_kh_ky: 208333,
      ngay_mua: '2022-08-20',
      ngay_kh0: '2022-09-01',
      so_ky_kh: 24,
      ngay_ct: '2024-01-15',
      so_ct: 'CT006',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'NS001',
      nh_cc1: 'NOI_THAT',
      nh_cc2: 'BAN_GHE',
      nh_cc3: 'XOAY',
      ten_bp: 'Phòng Nhân sự',
      loai_cc: 'Nội thất văn phòng',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 2500000,
      gt_cl_nt: 625000,
      gt_kh_ky_nt: 208333
    },
    {
      id: '7',
      stt: 7,
      ma_cc: 'CC007',
      ten_cc: 'Máy chiếu Epson EB-X41',
      nguyen_gia: 15000000,
      gt_da_kh_nt0: 7500000,
      gt_cl: 7500000,
      gt_kh_ky: 625000,
      ngay_mua: '2023-02-28',
      ngay_kh0: '2023-03-01',
      so_ky_kh: 48,
      ngay_ct: '2024-01-15',
      so_ct: 'CT007',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'DT001',
      nh_cc1: 'CNTT',
      nh_cc2: 'PROJECTOR',
      nh_cc3: 'EPSON',
      ten_bp: 'Phòng Đào tạo',
      loai_cc: 'Thiết bị trình chiếu',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 15000000,
      gt_cl_nt: 7500000,
      gt_kh_ky_nt: 625000
    },
    {
      id: '8',
      stt: 8,
      ma_cc: 'CC008',
      ten_cc: 'Tủ sắt văn phòng 4 ngăn',
      nguyen_gia: 4200000,
      gt_da_kh_nt0: 3150000,
      gt_cl: 1050000,
      gt_kh_ky: 350000,
      ngay_mua: '2022-09-15',
      ngay_kh0: '2022-10-01',
      so_ky_kh: 24,
      ngay_ct: '2024-01-15',
      so_ct: 'CT008',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'HC001',
      nh_cc1: 'NOI_THAT',
      nh_cc2: 'TU_SAT',
      nh_cc3: 'VAN_PHONG',
      ten_bp: 'Phòng Hành chính',
      loai_cc: 'Nội thất văn phòng',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 4200000,
      gt_cl_nt: 1050000,
      gt_kh_ky_nt: 350000
    },
    {
      id: '9',
      stt: 9,
      ma_cc: 'CC009',
      ten_cc: 'Máy scan Canon CanoScan LiDE 300',
      nguyen_gia: 3200000,
      gt_da_kh_nt0: 2133333,
      gt_cl: 1066667,
      gt_kh_ky: 266667,
      ngay_mua: '2022-11-10',
      ngay_kh0: '2022-12-01',
      so_ky_kh: 24,
      ngay_ct: '2024-01-15',
      so_ct: 'CT009',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'IT001',
      nh_cc1: 'CNTT',
      nh_cc2: 'SCANNER',
      nh_cc3: 'CANON',
      ten_bp: 'Phòng Công nghệ thông tin',
      loai_cc: 'Thiết bị CNTT',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 3200000,
      gt_cl_nt: 1066667,
      gt_kh_ky_nt: 266667
    },
    {
      id: '10',
      stt: 10,
      ma_cc: 'CC010',
      ten_cc: 'Bàn họp gỗ tự nhiên 8 chỗ ngồi',
      nguyen_gia: 18000000,
      gt_da_kh_nt0: 13500000,
      gt_cl: 4500000,
      gt_kh_ky: 1500000,
      ngay_mua: '2022-12-20',
      ngay_kh0: '2023-01-01',
      so_ky_kh: 24,
      ngay_ct: '2024-01-15',
      so_ct: 'CT010',
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'HC001',
      nh_cc1: 'NOI_THAT',
      nh_cc2: 'BAN_HOP',
      nh_cc3: 'GO_TU_NHIEN',
      ten_bp: 'Phòng Hành chính',
      loai_cc: 'Nội thất văn phòng',
      trang_thai: 'Đang sử dụng',
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia_nt: 18000000,
      gt_cl_nt: 4500000,
      gt_kh_ky_nt: 1500000
    }
  ];
};

/**
 * Custom hook for managing CCDC Inventory data
 *
 * This hook provides functionality to fetch CCDC inventory data
 * with mock support for testing and development purposes.
 */
export function useCCDCInventory(searchParams: CCDCInventorySearchFormValues): UseCCDCInventoryReturn {
  const [data, setData] = useState<CCDCInventoryItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: CCDCInventorySearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<CCDCInventoryResponse>('/cong-cu/kiem-ke-ccdc/danh-sach-ccdc/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred while fetching CCDC inventory data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
