import { z } from 'zod';

export const searchSchema = z.object({
  // Basic Info fields
  fromMonth: z.coerce.number().optional(),
  fromYear: z.coerce.number().optional(),
  toMonth: z.coerce.number().optional(),
  toYear: z.coerce.number().optional(),

  // Details Tab fields
  ma_vt: z.string().optional(), // Mã sản phẩm
  ma_bo_phan: z.string().optional(), // Mã bộ phận
  so_lenh_sx: z.string().optional(), // Số lệnh sản xuất
  don_vi: z.string().optional(), // Đơn vị
  reportTemplate: z.enum(['TC', 'NT']).optional(), // Mẫu báo cáo: TC = Tiền chuẩn, NT = Ngoại tệ

  // Other Tab fields
  reportFilterTemplate: z.string().optional(), // Mẫu lọc báo cáo
  dataAnalysisTemplate: z.string().optional() // Mẫu phân tích dữ liệu
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  // Basic Info fields
  fromMonth: new Date().getMonth() + 1,
  fromYear: new Date().getFullYear(),
  toMonth: new Date().getMonth() + 1,
  toYear: new Date().getFullYear(),

  // Details Tab fields
  ma_vt: '',
  ma_bo_phan: '',
  so_lenh_sx: '',
  don_vi: '',
  reportTemplate: 'TC',

  // Other Tab fields
  reportFilterTemplate: 'user_filter',
  dataAnalysisTemplate: 'no_analysis'
};
