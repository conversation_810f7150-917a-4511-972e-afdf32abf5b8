import { useFormContext } from 'react-hook-form';
import React from 'react';
import { supplierSearchColumns, supplierGroupSearchColumns, regionSearchColumns } from './cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const DetailTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Mã nhà cung cấp */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã nhà cung cấp:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
              searchColumns={supplierSearchColumns}
              dialogTitle='Danh mục nhà cung cấp'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              onValueChange={value => {
                setValue('ma_ncc', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_ncc', row.customer_code);
                  setValue('ten_ncc', row.customer_name);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Nhóm NCC */}
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm NCC:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                  searchColumns={supplierGroupSearchColumns}
                  dialogTitle='Danh mục nhóm nhà cung cấp 1'
                  columnDisplay='supplierGroupCode'
                  displayRelatedField='supplierGroupName'
                  onValueChange={value => {
                    setValue('nhom_ncc_1', value);
                  }}
                  onRowSelection={row => {
                    if (row) {
                      setValue('nhom_ncc_1', row.supplierGroupCode);
                    }
                  }}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                  searchColumns={supplierGroupSearchColumns}
                  dialogTitle='Danh mục nhóm nhà cung cấp 2'
                  columnDisplay='supplierGroupCode'
                  displayRelatedField='supplierGroupName'
                  onValueChange={value => {
                    setValue('nhom_ncc_2', value);
                  }}
                  onRowSelection={row => {
                    if (row) {
                      setValue('nhom_ncc_2', row.supplierGroupCode);
                    }
                  }}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                  searchColumns={supplierGroupSearchColumns}
                  dialogTitle='Danh mục nhóm nhà cung cấp 3'
                  columnDisplay='supplierGroupCode'
                  displayRelatedField='supplierGroupName'
                  onValueChange={value => {
                    setValue('nhom_ncc_3', value);
                  }}
                  onRowSelection={row => {
                    if (row) {
                      setValue('nhom_ncc_3', row.supplierGroupCode);
                    }
                  }}
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>

        {/* Khu vực */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Khu vực:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
              searchColumns={regionSearchColumns}
              dialogTitle='Danh mục khu vực'
              columnDisplay='rg_code'
              displayRelatedField='rgname'
              onValueChange={value => {
                setValue('khu_vuc', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('khu_vuc', row.rg_code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Bao gồm số dư 0 */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Bao gồm số dư 0:</Label>
          <FormField name='bao_gom_so_du_0' type='checkbox' className='flex-1' />
        </div>

        {/* Chi tiết theo chứng từ */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Chi tiết theo chứng từ:</Label>
          <FormField name='chi_tiet_theo_chung_tu' type='checkbox' className='flex-1' />
        </div>

        {/* Sắp xếp theo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Sắp xếp theo:</Label>
          <div className='w-64'>
            <FormField
              name='sap_xep_theo'
              label=''
              type='select'
              options={[
                { value: 'ma_ncc', label: 'Mã nhà cung cấp' },
                { value: 'ten_ncc', label: 'Tên nhà cung cấp' },
                { value: 'so_du', label: 'Số dư' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
