import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* 1. Kỳ này từ / đến */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON> này từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
