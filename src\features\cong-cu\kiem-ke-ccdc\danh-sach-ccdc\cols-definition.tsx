import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const ccdcColumns: GridColDef[] = [
  { field: 'stt', headerName: 'Stt', width: 100 },
  { field: 'ma_cc', headerName: 'Mã công cụ', width: 100 },
  { field: 'ten_cc', headerName: 'Tên công cụ', width: 100 },
  { field: 'nguyen_gia', headerName: 'Nguyên giá', width: 100 },
  { field: 'gt_da_kh_nt0', headerName: 'Gt đã phân bổ', width: 100 },
  { field: 'gt_cl', headerName: 'Còn lại', width: 100 },
  { field: 'gt_kh_ky', headerName: 'P/b trong kỳ', width: 100 },
  { field: 'ngay_mua', headerName: 'Ngày mua', width: 100 },
  { field: 'ngay_kh0', headerName: '<PERSON><PERSON><PERSON> tính pb', width: 100 },
  { field: 'so_ky_kh', headerName: 'Số kỳ phân bổ', width: 100 },
  { field: 'ngay_ct', headerName: 'Ngày ct', width: 100 },
  { field: 'so_ct', headerName: 'Số ct', width: 100 },
  { field: 'tk_cc', headerName: 'Tk công cụ', width: 100 },
  { field: 'tk_kh', headerName: 'Tk phân bổ', width: 100 },
  { field: 'tk_cp', headerName: 'Tk chi phí', width: 100 },
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 100 },
  { field: 'nh_cc1', headerName: 'Nhóm công cụ 1', width: 100 },
  { field: 'nh_cc2', headerName: 'Nhóm công cụ 2', width: 100 },
  { field: 'nh_cc3', headerName: 'Nhóm công cụ 3', width: 100 }
];

export const printColumns: GridColDef[] = [
  { field: 'columnName', headerName: 'Tên cột', width: 150, editable: true },
  {
    field: 'englishName',
    headerName: 'Tên tiếng Anh',
    width: 150,
    editable: true
  },
  {
    field: 'width',
    headerName: 'Độ rộng',
    width: 100,
    editable: true,
    type: 'number'
  },
  {
    field: 'format',
    headerName: 'Định dạng',
    width: 120,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Text', 'Number', 'Currency', 'Date', 'Percentage']
  },
  {
    field: 'alignment',
    headerName: 'Căn chỉnh',
    width: 100,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Left', 'Center', 'Right']
  },
  {
    field: 'bold',
    headerName: 'In đậm',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'italic',
    headerName: 'Nghiêng',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'total',
    headerName: 'Tổng',
    width: 80,
    editable: true,
    type: 'boolean'
  }
];

export const bodyPartSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'name', headerName: 'Tên bộ phận', flex: 1 }
];

export const toolGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã nhóm', flex: 1 },
  { field: 'name', headerName: 'Tên nhóm', flex: 1 }
];

export const toolTypeSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Loại công cụ', flex: 1 },
  { field: 'name', headerName: 'Tên loại công cụ', flex: 1 }
];

export const availableFields = [
  'Stt',
  'Mã công cụ',
  'Tên công cụ',
  'Nguyên giá nt',
  'Gt đã phân bổ nt',
  'Còn lại nt',
  'P/b trong kỳ nt',
  'Ngoại tệ',
  'Tỷ giá',
  'Nguyên giá',
  'Gt đã phân bổ',
  'Còn lại',
  'P/b trong kỳ',
  'Ngày mua',
  'Ngày tính pb',
  'Số kỳ phân bổ',
  'Ngày ct',
  'Số ct',
  'Tk công cụ',
  'Tk phân bổ',
  'Tk chi phí',
  'Mã bộ phận',
  'Mã vụ việc',
  'Mã phí',
  'Nhóm công cụ 1',
  'Nhóm công cụ 2',
  'Nhóm công cụ 3'
];
