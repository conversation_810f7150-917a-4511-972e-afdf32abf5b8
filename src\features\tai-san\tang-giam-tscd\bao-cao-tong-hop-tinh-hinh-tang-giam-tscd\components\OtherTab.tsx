import { IconButton } from '@mui/material';
import { PopupFormField } from '@/components/arito/arito-form/pop-up/arito-popup-form-field';
import AritoIcon from '@/components/custom/arito/icon';

export const OtherTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <div className='space-y-2'>
        {/* Mẫu lọc báo cáo */}
        <div className='grid grid-cols-1 gap-x-8 lg:grid lg:grid-cols-[2fr,1fr]'>
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[120px,1fr]'
            type='select'
            label='Mẫu lọc báo cáo'
            name='mau_loc_bao_cao'
            options={[{ value: 0, label: 'Người dùng tự lọc' }]}
            disabled={formMode === 'view'}
          />

          <div className='relative'>
            <div className='group relative'>
              <IconButton
                size='small'
                color='primary'
                disabled={formMode === 'view'}
                className='square-full border border-gray-300'
                style={{ width: '32px', height: '32px', borderRadius: '4px' }}
              >
                <AritoIcon icon={624} />
              </IconButton>
              <div className='absolute left-0 z-10 hidden bg-white shadow-lg group-hover:block'>
                <button className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'>
                  <AritoIcon icon={7} />
                  <div className='ml-2'>Lưu mẫu mới</div>
                </button>
                <button className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'>
                  <AritoIcon icon={75} />
                  <div className='ml-2'>Lưu đè vào mẫu đang chọn</div>
                </button>
                <button className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'>
                  <AritoIcon icon={8} />
                  <div className='ml-2'>Xoá mẫu đang chọn</div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mẫu phân tích dữ liệu */}
        <div className='grid grid-cols-1 gap-x-8 lg:grid lg:grid-cols-[2fr,1fr]'>
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[120px,1fr]'
            type='select'
            label='Mẫu phân tích DL'
            name='mau_phan_tich_dl'
            options={[{ value: 0, label: 'Không phân tích' }]}
            disabled={formMode === 'view'}
          />

          <div className='relative'>
            <div className='group relative'>
              <IconButton
                size='small'
                color='primary'
                disabled={formMode === 'view'}
                className='square-full border border-gray-300'
                style={{ width: '32px', height: '32px', borderRadius: '4px' }}
              >
                <AritoIcon icon={790} />
              </IconButton>
              <div className='absolute left-0 z-10 hidden bg-white shadow-lg group-hover:block'>
                <button className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'>
                  <AritoIcon icon={7} />
                  <div className='ml-2'>Tạo mẫu phân tích mới</div>
                </button>
                <button className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'>
                  <AritoIcon icon={9} />
                  <div className='ml-2'>Sửa mẫu đang chọn</div>
                </button>
                <button className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'>
                  <AritoIcon icon={8} />
                  <div className='ml-2'>Xoá mẫu đang chọn</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);
