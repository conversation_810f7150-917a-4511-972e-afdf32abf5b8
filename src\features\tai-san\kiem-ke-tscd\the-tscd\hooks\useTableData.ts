import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { TheTaiSanCoDinhItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: TheTaiSanCoDinhItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          { field: 'stt', headerName: 'Stt', width: 60, type: 'number' },
          { field: 'ma_ts', headerName: 'Mã tài sản', width: 120 },
          { field: 'ten_ts', headerName: 'Tên tài sản', width: 250 },
          { field: 'ngay_tang', headerName: 'Ngày tăng', width: 120 },
          { field: 'ngay_kh', headerName: '<PERSON><PERSON>y tính kh', width: 120 },
          { field: 'so_ky_kh', headerName: '<PERSON><PERSON> kỳ khấu hao', width: 130, type: 'number' },
          { field: 'tk_ts', headerName: 'Tk tài sản', width: 100 },
          { field: 'tk_kh', headerName: 'Tk khấu hao', width: 110 },
          { field: 'tk_cp', headerName: 'Tk chi phí', width: 100 },
          { field: 'ma_bp', headerName: 'Mã bộ phận', width: 120 },
          { field: 'so_ct', headerName: 'Số chứng từ gốc', width: 150 }
        ],
        rows: data
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
