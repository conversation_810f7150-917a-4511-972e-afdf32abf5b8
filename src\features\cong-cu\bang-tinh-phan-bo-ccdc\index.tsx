'use client';

import React, { useState } from 'react';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import { InitialSearchDialog, ActionBar } from './components';
import { useBangTinhPhanBoCCDC } from '@/hooks/queries';

export default function AllocationSpreedsheetReport() {
  const {
    initialSearchDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick
  } = useDialogState();

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useBangTinhPhanBoCCDC(searchParams);
  const { tables, handleRowClick } = useTableData(data);

  const [dates, setDates] = useState<{
    fromDate: {
      tu_ki: string;
      tu_nam: string;
    };
    toDate: {
      den_ki: string;
      den_nam: string;
    };
  }>({
    fromDate: {
      tu_ki: `${new Date().getMonth() + 1}`,
      tu_nam: `${new Date().getFullYear()}`
    },
    toDate: {
      den_ki: `${new Date().getMonth() + 1}`,
      den_nam: `${new Date().getFullYear()}`
    }
  });

  const handleSearchWithData = async (values: any) => {
    setSearchParams(values);
    await fetchData(values);
    handleInitialSearch(values);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
        setDates={setDates}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            tu_ky={dates.fromDate.tu_ki}
            tu_nam={dates.fromDate.tu_nam}
            den_ky={dates.toDate.den_ki}
            den_nam={dates.toDate.den_nam}
          />

          {isLoading && <LoadingOverlay />}

          <div className='flex-1 overflow-hidden'>
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
