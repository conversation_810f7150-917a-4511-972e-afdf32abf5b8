import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Kỳ/Năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='ky' type='text' className='w-20' />
            <FormField name='nam' type='text' className='w-24' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
