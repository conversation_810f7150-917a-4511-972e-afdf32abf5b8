import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Main fields
  ngay_tu: dateLike,
  ngay_den: dateLike,

  // General fields
  ma_khach_hang: z.string().optional(),
  nhom_khach_hang: z.array(z.string()).optional(),
  khu_vuc: z.string().optional(),
  ma_vat_tu: z.string().optional(),
  loai_vat_tu: z.string().optional(),
  nhom_vat_tu: z.array(z.string()).optional(),
  mau_bao_cao: z.string().optional(),

  // Other fields
  ma_giao_dich: z.string().optional(),
  tai_khoan_vat_tu: z.string().optional(),
  tai_khoan_doanh_thu: z.string().optional(),
  ma_lo: z.string().optional(),
  ma_vi_tri: z.string().optional(),
  so_c_tu_to: z.string().optional(),
  dien_giai: z.string().optional()
});

export const initialValues = {
  // Main fields
  ngay_tu: '2025-04-01',
  ngay_den: '2025-04-03',
  so_chung_tu_tu: '',
  so_chung_tu_den: '',

  // General fields
  ma_khach_hang: '',
  nhom_khach_hang: [],
  ma_vat_tu: '',
  loai_vat_tu: '',
  nhom_vat_tu: [],
  ma_kho: '',
  han_giao_hang: '',
  trang_thai: '0',
  mau_bao_cao: '0',

  // Other fields
  loai_don_hang: '0',
  ma_giao_dich: '',
  ma_lo: '',
  ma_vi_tri: '',
  dien_giai: '',
  mau_loc_bao_cao: '0'
};
