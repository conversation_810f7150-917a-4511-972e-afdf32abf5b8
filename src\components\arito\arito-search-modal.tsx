import { Dialog, DialogContent, IconButton, Typography, Box, Divider, useMediaQuery, useTheme } from '@mui/material';
import CropSquareIcon from '@mui/icons-material/CropSquare';
import MinimizeIcon from '@mui/icons-material/Minimize';
import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { GridColDef } from '@mui/x-data-grid';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoIcon from '@/components/custom/arito/icon';

interface AritoSearchModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (value: any) => void;
  title: string;
  columns: GridColDef[];
  data?: any[];
  loading?: boolean;
  docType?: string;
  filters?: any[][];
  searchPlaceholder?: string;
}

// Modal action button component
interface ModalActionButtonProps {
  label: string;
  icon: number;
  onClick?: () => void;
  type?: 'button' | 'submit';
  primary?: boolean;
}

const ModalActionButton: React.FC<ModalActionButtonProps> = ({
  label,
  icon,
  onClick,
  type = 'button',
  primary = true
}) => {
  const baseStyle = {
    padding: '6px 6px',
    paddingRight: '12px',
    cursor: 'default',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px',
    fontSize: '14px',
    width: '100px',
    transition: 'all 0.2s',
    whiteSpace: 'nowrap' as const,
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  };

  const primaryStyle = {
    ...baseStyle,
    backgroundColor: '#53a3a3',
    color: 'white',
    border: 'none'
  };

  const secondaryStyle = {
    ...baseStyle,
    backgroundColor: 'white',
    color: '#53a3a3',
    border: '1px solid #53a3a3'
  };

  return (
    <button
      type={type}
      onClick={onClick}
      style={primary ? primaryStyle : secondaryStyle}
      onMouseOver={e => {
        if (primary) {
          e.currentTarget.style.backgroundColor = '#438585';
        } else {
          e.currentTarget.style.backgroundColor = '#53a3a3';
          e.currentTarget.style.color = 'white';
        }
      }}
      onMouseOut={e => {
        if (primary) {
          e.currentTarget.style.backgroundColor = '#53a3a3';
        } else {
          e.currentTarget.style.backgroundColor = 'white';
          e.currentTarget.style.color = '#53a3a3';
        }
      }}
    >
      <AritoIcon icon={icon} marginX='4px' />
      {label}
    </button>
  );
};

const AritoSearchModal: React.FC<AritoSearchModalProps> = ({
  visible,
  onClose,
  onSelect,
  title,
  columns,
  data = [],
  loading: initialLoading = false,
  docType,
  filters,
  searchPlaceholder
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [tableData, setTableData] = useState<any[]>(data);
  const [loading, setLoading] = useState(initialLoading);
  // Add a ref to track if initial selection has been done
  const initialSelectionDone = React.useRef(false);

  // Sample data for demonstration when no data or docType is provided
  const sampleData = React.useMemo(() => {
    return Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      code: `CODE${i + 1}`,
      name: `Item ${i + 1}`
    }));
  }, []);

  // Update tableData when data prop changes
  useEffect(() => {
    if (data.length > 0) {
      setTableData(data);
    }
  }, [data]);

  // Fetch data from API when docType is provided
  useEffect(() => {
    const fetchData = async () => {
      if (!docType) return;

      try {
        setLoading(true);

        // Placeholder implementation - ERPNext functionality removed
        const result: any[] = [];

        // Transform data to match the expected format
        const formattedData = result.map((item, index) => ({
          id: index + 1,
          ...item
        }));

        setTableData(formattedData);
        // Reset the initial selection flag when new data is fetched
        initialSelectionDone.current = false;
      } catch (error) {
        console.error('Error fetching search data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (visible) {
      fetchData();
    }
  }, [visible, docType, columns, filters]);

  // Initialize selectedRow with the first row when data is loaded
  useEffect(() => {
    // Determine which data to display
    const displayData = tableData.length > 0 ? tableData : data.length > 0 ? data : sampleData;

    // Only set the first row as selected when data is first loaded
    if (visible && displayData.length > 0 && !initialSelectionDone.current) {
      console.log('Auto-selecting first row:', displayData[0]);
      setSelectedRow(displayData[0]);
      initialSelectionDone.current = true;
    }
  }, [tableData, data, sampleData, visible]);

  // Reset the selection flag when the modal is closed
  useEffect(() => {
    if (!visible) {
      initialSelectionDone.current = false;
    }
  }, [visible]);

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleRowSelect = (row: any) => {
    console.log('Row selected:', row);
    // This will override the auto-selection
    setSelectedRow(row);
    // Mark that we've made a manual selection
    initialSelectionDone.current = true;
  };

  const handleAccept = () => {
    console.log('handleAccept called, selectedRow:', selectedRow);

    if (selectedRow) {
      console.log('Accepting selected row:', selectedRow);
      onSelect(selectedRow);
      onClose();
    } else {
      // If somehow no row is selected but we have data, select the first row
      const displayData = tableData.length > 0 ? tableData : data.length > 0 ? data : sampleData;
      if (displayData.length > 0) {
        console.log('No row was selected, using first row:', displayData[0]);
        onSelect(displayData[0]);
        onClose();
      } else {
        onClose();
      }
    }
  };

  const handleDoubleClick = (row: any) => {
    console.log('Double-clicked row:', row);
    // Make sure we're using the actual row object
    onSelect(row);
    onClose();
  };

  if (!visible) return null;

  // Determine which data to display
  const displayData = tableData.length > 0 ? tableData : data.length > 0 ? data : sampleData;

  return (
    <Dialog
      open={visible}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick') {
          onClose();
        }
      }}
      maxWidth={isFullScreen ? false : 'md'}
      fullWidth={isMobile || isFullScreen}
      fullScreen={isFullScreen}
      BackdropProps={{
        style: { backgroundColor: 'transparent' }
      }}
      PaperProps={{
        style: {
          backgroundColor: '#f8fcfd',
          overflow: 'hidden',
          borderRadius: 0,
          minWidth: isMobile ? '100%' : isFullScreen ? '100%' : '850px',
          height: isFullScreen ? '100%' : 'auto'
        }
      }}
    >
      <Box
        sx={{
          backgroundColor: '#f0f7fc',
          padding: 0,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #e0e0e0',
          height: '36px'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            paddingLeft: '8px'
          }}
        >
          <AritoIcon icon={315} marginX='4px' />
          <Typography color='#444' variant='body2'>
            {title}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          <IconButton
            sx={{
              color: 'black',
              padding: 0,
              borderRadius: 0,
              height: '100%',
              paddingX: 2.5,
              '&:hover': {
                backgroundColor: '#d9e6ed'
              }
            }}
            size='small'
            onClick={toggleFullScreen}
          >
            {isFullScreen ? (
              <MinimizeIcon fontSize='small' sx={{ fontSize: '12px' }} />
            ) : (
              <CropSquareIcon fontSize='small' sx={{ fontSize: '12px' }} />
            )}
          </IconButton>
          <IconButton
            sx={{
              color: 'black',
              paddingX: 2.5,
              borderRadius: 0,
              height: '100%',
              '&:hover': {
                backgroundColor: '#d9e6ed'
              }
            }}
            size='small'
            onClick={onClose}
          >
            <CloseIcon sx={{ fontSize: '12px' }} />
          </IconButton>
        </Box>
      </Box>

      <DialogContent
        sx={{
          padding: '0px',
          display: 'flex',
          flexDirection: 'column',
          height: 'fit'
        }}
      >
        <div className='h-[300px]'>
          <AritoInputTable
            columns={columns}
            value={displayData}
            mode='search'
            onRowSelect={handleRowSelect}
            onRowDoubleClick={handleDoubleClick}
            loading={loading}
            searchPlaceholder={searchPlaceholder || `Tìm kiếm ${title}`}
          />
        </div>

        <Divider />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '8px',
            padding: '6px'
          }}
        >
          <ModalActionButton label='Đồng ý' icon={884} onClick={handleAccept} primary={true} />
          <ModalActionButton label='Huỷ' icon={885} onClick={onClose} primary={false} />
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AritoSearchModal;
