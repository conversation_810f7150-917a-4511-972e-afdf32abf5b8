import { useState, useCallback, useEffect } from 'react';
import { BusinessReportItem, BusinessReportResponse, UseBusinessReportReturn } from '../types';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

const generateMockData = (): BusinessReportItem[] => {
  return [
    {
      id: '1',
      ma_so: '01',
      chi_tieu: 'Doanh thu thuần về bán hàng và cung cấp dịch vụ',
      thuyet_minh: 'V.1',
      tong_tien: 125000000000,
      binh_quoi: ***********,
      nguyen_kiem: ***********,
      le_van_sy: ***********,
      dinh_bo_linh: 8000000000,
      duong_so_8: ***********,
      phan_van_tri: ***********,
      ngo_tat_to: ***********,
      nguyen_van_qua: 9000000000,
      van_kiep: 6000000000,
      tan_quy: 5000000000,
      dong_den: 0
    },
    {
      id: '2',
      ma_so: '02',
      chi_tieu: '<PERSON><PERSON><PERSON> giảm trừ doanh thu',
      thuyet_minh: 'V.2',
      tong_tien: 2500000000,
      binh_quoi: 300000000,
      nguyen_kiem: 240000000,
      le_van_sy: 360000000,
      dinh_bo_linh: 160000000,
      duong_so_8: 440000000,
      phan_van_tri: 280000000,
      ngo_tat_to: 320000000,
      nguyen_van_qua: 180000000,
      van_kiep: 120000000,
      tan_quy: 100000000,
      dong_den: 0
    },
    {
      id: '3',
      ma_so: '10',
      chi_tieu: 'Doanh thu thuần về bán hàng và cung cấp dịch vụ',
      thuyet_minh: '',
      tong_tien: 122500000000,
      binh_quoi: 14700000000,
      nguyen_kiem: 11760000000,
      le_van_sy: 17640000000,
      dinh_bo_linh: 7840000000,
      duong_so_8: 21560000000,
      phan_van_tri: 13720000000,
      ngo_tat_to: 15680000000,
      nguyen_van_qua: 8820000000,
      van_kiep: 5880000000,
      tan_quy: 4900000000,
      dong_den: 0
    },
    {
      id: '4',
      ma_so: '11',
      chi_tieu: 'Giá vốn hàng bán',
      thuyet_minh: 'V.3',
      tong_tien: 85750000000,
      binh_quoi: 10290000000,
      nguyen_kiem: 8232000000,
      le_van_sy: 12348000000,
      dinh_bo_linh: 5488000000,
      duong_so_8: 15092000000,
      phan_van_tri: 9604000000,
      ngo_tat_to: 10976000000,
      nguyen_van_qua: 6174000000,
      van_kiep: 4116000000,
      tan_quy: 3430000000,
      dong_den: 0
    },
    {
      id: '5',
      ma_so: '20',
      chi_tieu: 'Lợi nhuận gộp về bán hàng và cung cấp dịch vụ',
      thuyet_minh: '',
      tong_tien: 36750000000,
      binh_quoi: 4410000000,
      nguyen_kiem: 3528000000,
      le_van_sy: 5292000000,
      dinh_bo_linh: 2352000000,
      duong_so_8: 6468000000,
      phan_van_tri: 4116000000,
      ngo_tat_to: 4704000000,
      nguyen_van_qua: 2646000000,
      van_kiep: 1764000000,
      tan_quy: 1470000000,
      dong_den: 0
    },
    {
      id: '6',
      ma_so: '21',
      chi_tieu: 'Doanh thu hoạt động tài chính',
      thuyet_minh: 'V.4',
      tong_tien: 1225000000,
      binh_quoi: 147000000,
      nguyen_kiem: 117600000,
      le_van_sy: 176400000,
      dinh_bo_linh: 78400000,
      duong_so_8: 215600000,
      phan_van_tri: 137200000,
      ngo_tat_to: 156800000,
      nguyen_van_qua: 88200000,
      van_kiep: 58800000,
      tan_quy: 49000000,
      dong_den: 0
    },
    {
      id: '7',
      ma_so: '22',
      chi_tieu: 'Chi phí tài chính',
      thuyet_minh: 'V.5',
      tong_tien: 612500000,
      binh_quoi: 73500000,
      nguyen_kiem: 58800000,
      le_van_sy: 88200000,
      dinh_bo_linh: 39200000,
      duong_so_8: 107800000,
      phan_van_tri: 68600000,
      ngo_tat_to: 78400000,
      nguyen_van_qua: 44100000,
      van_kiep: 29400000,
      tan_quy: 24500000,
      dong_den: 0
    },
    {
      id: '8',
      ma_so: '23',
      chi_tieu: 'Phần lãi trong công ty liên doanh, liên kết',
      thuyet_minh: '',
      tong_tien: 0,
      binh_quoi: 0,
      nguyen_kiem: 0,
      le_van_sy: 0,
      dinh_bo_linh: 0,
      duong_so_8: 0,
      phan_van_tri: 0,
      ngo_tat_to: 0,
      nguyen_van_qua: 0,
      van_kiep: 0,
      tan_quy: 0,
      dong_den: 0
    },
    {
      id: '9',
      ma_so: '24',
      chi_tieu: 'Chi phí bán hàng',
      thuyet_minh: 'V.6',
      tong_tien: 12250000000,
      binh_quoi: 1470000000,
      nguyen_kiem: 1176000000,
      le_van_sy: 1764000000,
      dinh_bo_linh: 784000000,
      duong_so_8: 2156000000,
      phan_van_tri: 1372000000,
      ngo_tat_to: 1568000000,
      nguyen_van_qua: 882000000,
      van_kiep: 588000000,
      tan_quy: 490000000,
      dong_den: 0
    },
    {
      id: '10',
      ma_so: '25',
      chi_tieu: 'Chi phí quản lý doanh nghiệp',
      thuyet_minh: 'V.7',
      tong_tien: ***********,
      binh_quoi: 2205000000,
      nguyen_kiem: 1764000000,
      le_van_sy: 2646000000,
      dinh_bo_linh: 1176000000,
      duong_so_8: 3234000000,
      phan_van_tri: 2058000000,
      ngo_tat_to: 2352000000,
      nguyen_van_qua: 1323000000,
      van_kiep: 882000000,
      tan_quy: 735000000,
      dong_den: 0
    }
  ];
};

/**
 * Custom hook for managing Business Report data
 *
 * This hook provides functionality to fetch business report data
 * with mock support for testing and development purposes.
 */
export function useBusinessReportData(searchParams: SearchFormValues): UseBusinessReportReturn {
  const [data, setData] = useState<BusinessReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BusinessReportResponse>(
        '/phan-tich/phan-tich-bao-cao-tai-chinh/bao-cao-ket-qua-hoat-dong-san-xuat-kinh-doanh-theo-doi-tuong/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
