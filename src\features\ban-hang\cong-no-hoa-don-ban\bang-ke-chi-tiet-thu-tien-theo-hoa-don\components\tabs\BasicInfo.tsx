import { useFormContext } from 'react-hook-form';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  const { setValue } = useFormContext();
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày h.toán từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_h_toan_tu' toDateName='ngay_h_toan_den' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON><PERSON><PERSON> thanh toán đến:</Label>
          <div>
            <FormField name='duoc_thanh_toan_den' type='date' defaultValue={new Date()} />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            onValueChange={value => {
              setValue('ma_tk', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('ma_tk', row.code);
                setValue('ten_tk', row.name);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[470px] max-w-full'
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
