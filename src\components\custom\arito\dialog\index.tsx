import { Dialog, DialogTitle, DialogContent, DialogActions, IconButton, useMediaQuery, useTheme } from '@mui/material';
import { FullscreenIcon, MinimizeIcon, X } from 'lucide-react';
import React, { useEffect, useImperativeHandle } from 'react';
import { dialogPaperStyle, dialogTitleStyle, dialogContentStyle, dialogActionsStyle } from './styles';
import { useAritoDialog } from './hooks';

export interface AritoDialogProps {
  /**
   * Dialog title
   */
  title: string;

  /**
   * Whether the dialog is open (optional, defaults to true)
   */
  open?: boolean;

  /**
   * Callback when the dialog is closed (optional)
   */
  onClose?: () => void;

  /**
   * Dialog content as children
   */
  children: React.ReactNode;

  /**
   * Dialog action buttons
   */
  actions?: React.ReactNode;

  /**
   * Whether to disable closing on backdrop click
   * @default false
   */
  disableBackdropClose?: boolean;

  /**
   * Whether to disable closing on Escape key
   * @default false
   */
  disableEscapeKeyDown?: boolean;

  /**
   * Whether to show fullscreen toggle button
   * @default true
   */
  showFullscreenToggle?: boolean;

  /**
   * Whether the dialog is in fullscreen mode
   * @default false
   */
  fullScreen?: boolean;

  /**
   * Callback when fullscreen mode is toggled
   */
  onFullscreenToggle?: () => void;

  /**
   * Maximum width of the dialog
   * @default 'md'
   */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;

  /**
   * Custom icon to show in the title
   */
  titleIcon?: React.ReactNode;

  classNameContent?: string;

  classNameContainer?: string;
}

export const AritoDialog = React.forwardRef<{ open: () => void }, AritoDialogProps>(
  (
    {
      title,
      open = true,
      onClose,
      children,
      actions,
      disableBackdropClose = false,
      disableEscapeKeyDown = false,
      showFullscreenToggle = true,
      fullScreen: propFullScreen = false,
      onFullscreenToggle,
      maxWidth = 'md',
      titleIcon,
      classNameContent,
      classNameContainer
    },
    ref
  ) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    const {
      isOpen,
      setIsOpen,
      isFullScreen,
      setIsFullScreen,
      toggleFullScreen,
      open: openDialog
    } = useAritoDialog({
      initialOpen: open,
      initialFullScreen: propFullScreen,
      onFullscreenToggle
    });

    useEffect(() => {
      setIsFullScreen(propFullScreen);
    }, [propFullScreen, setIsFullScreen]);

    useEffect(() => {
      setIsOpen(open);
    }, [open, setIsOpen]);

    const shouldBeFullScreen = isMobile || isFullScreen;
    useImperativeHandle(
      ref,
      () => ({
        open: openDialog
      }),
      [openDialog]
    );

    return (
      <Dialog
        open={isOpen}
        onClose={disableBackdropClose ? () => {} : onClose}
        disableEscapeKeyDown={disableEscapeKeyDown}
        fullScreen={shouldBeFullScreen}
        maxWidth={maxWidth}
        className={classNameContainer}
        PaperProps={{
          sx: dialogPaperStyle(isMobile, isFullScreen)
        }}
      >
        <DialogTitle sx={dialogTitleStyle}>
          <div className='flex items-center'>
            {titleIcon}
            <span className={`text-base font-medium ${titleIcon ? 'ml-2' : ''}`}>{title}</span>
          </div>

          <div className='ml-auto flex'>
            {showFullscreenToggle && !isMobile && (
              <IconButton
                onClick={toggleFullScreen}
                title={isFullScreen ? 'Exit fullscreen' : 'Fullscreen'}
                className='mr-1 p-1'
              >
                {isFullScreen && <MinimizeIcon size={18} />}
                {!isFullScreen && <FullscreenIcon size={18} />}
              </IconButton>
            )}
            <IconButton onClick={disableBackdropClose ? () => {} : onClose} title='Close' className='p-1'>
              <X size={18} />
            </IconButton>
          </div>
        </DialogTitle>

        <DialogContent sx={dialogContentStyle} className={classNameContent}>
          {children}
        </DialogContent>

        {actions && <DialogActions sx={dialogActionsStyle}>{actions}</DialogActions>}
      </Dialog>
    );
  }
);

AritoDialog.displayName = 'AritoDialog';
