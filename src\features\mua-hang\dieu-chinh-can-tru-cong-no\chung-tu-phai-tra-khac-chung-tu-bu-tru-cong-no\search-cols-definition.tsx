import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const documentNumberSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nk', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_nk', headerName: 'Tên quyển', flex: 2 },
  { field: 'so_ct_mau', headerName: 'Số khai báo', flex: 1 }
];

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kh', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'ten_kh', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'du_cn_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'du_cn_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'ma_so_thue', headerName: '<PERSON><PERSON> số thuế', flex: 1 },
  { field: 'e_mail', headerName: 'Email', flex: 1 },
  { field: 'dien_thoai', headerName: '<PERSON>ố điện thoại', flex: 1 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'parent_account_code_data', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'tk_so_cai', headerName: 'Tk sổ cái', flex: 1 },
  { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', flex: 1, checkboxSelection: true },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'dvt', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'ten_dvt', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'uuid', headerName: 'ID', flex: 1 }
];

export const departmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', flex: 2 }
];

export const caseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vu_viec', headerName: 'Mã vụ việc', flex: 1 },
  { field: 'ten_vu_viec', headerName: 'Tên vụ việc', flex: 2 }
];

export const contractSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_hd', headerName: 'Mã hợp đồng', flex: 1 },
  { field: 'ten_hd', headerName: 'Tên hợp đồng', flex: 2 }
];

export const paymentPhaseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_dtt', headerName: 'Mã đợt', flex: 1 },
  { field: 'ten_dtt', headerName: 'Tên đợt thanh toán', flex: 2 },
  { field: 'ngay_tt', headerName: 'Ngày thanh toán', flex: 1 },
  { field: 'ty_le', headerName: 'Tỷ lệ (%)', flex: 1 },
  { field: 'tien', headerName: 'Tiền', flex: 1 }
];

export const covenantSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_khe_uoc', headerName: 'Mã khế ước', flex: 1 },
  { field: 'ten_khe_uoc', headerName: 'Tên khế ước', flex: 2 }
];

export const feeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_phi', headerName: 'Mã phí', flex: 1 },
  { field: 'ten_phi', headerName: 'Tên phí', flex: 2 }
];

export const productSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', flex: 1 },
  { field: 'ten_vt', headerName: 'Tên vật tư', flex: 2 },
  { field: 'dvt_data.ten_dvt', headerName: 'Đơn vị tính', flex: 1 }
];

export const productionOrderSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lsx', headerName: 'Mã lệnh sản xuất', flex: 1 },
  { field: 'ten_lsx', headerName: 'Tên lệnh sản xuất', flex: 2 }
];

export const invalidExpenseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cp', headerName: 'Mã chi phí không hợp lệ', flex: 1 },
  { field: 'ten_cp', headerName: 'Tên chi phí không hợp lệ', flex: 2 }
];
