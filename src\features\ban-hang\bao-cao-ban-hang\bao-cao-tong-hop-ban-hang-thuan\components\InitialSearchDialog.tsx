import React, { useState } from 'react';

import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import { searchSchema, initialValues } from '../schemas';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import AritoIcon from '@/components/custom/arito/icon';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);
  const [showValidationDialog, setShowValidationDialog] = useState<boolean>(false);

  const validateDateRange = (data: any) => {
    // <PERSON><PERSON>m tra ngày từ và ngày đến
    if (!data.ngay_tu || !data.ngay_den || data.ngay_tu.trim() === '' || data.ngay_den.trim() === '') {
      return false;
    }
    return true;
  };

  const handleSubmit = (data: any) => {
    // Kiểm tra validation trước khi submit
    if (!validateDateRange(data)) {
      setShowValidationDialog(true);
      return;
    }
    onSearch(data);
  };

  const handleDirectSubmit = () => {
    // Kiểm tra validation trước khi submit
    if (!validateDateRange(formValues)) {
      setShowValidationDialog(true);
      return;
    }
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo tổng hợp bán hàng thuần'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' className='bg-[#26827C]' onClick={handleDirectSubmit}>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
      />

      {/* Dialog thông báo validation */}
      {showValidationDialog && (
        <AritoDialog
          open={showValidationDialog}
          onClose={() => setShowValidationDialog(false)}
          title='Thông báo'
          maxWidth='sm'
          disableBackdropClose={true}
          disableEscapeKeyDown={true}
          titleIcon={<AritoIcon icon={260} />}
          actions={
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              onClick={() => setShowValidationDialog(false)}
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
          }
        >
          <p className='min-w-96 p-4 text-base font-medium'>
            Ngày từ/đến không được để trống hoặc giá trị không hợp lệ
          </p>
        </AritoDialog>
      )}
    </AritoDialog>
  );
};

export default InitialSearchDialog;
