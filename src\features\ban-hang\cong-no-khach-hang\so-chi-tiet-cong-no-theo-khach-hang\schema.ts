import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  ngay_ct1: z.string().optional(), // From date
  ngay_ct2: z.string().optional(), // To date
  ct_vt: z.boolean().optional(), // Material details flag
  so_du: z.boolean().optional(), // Balance calculation flag

  groupBy: z.array(z.string()).optional(),

  mau_bc: z.number().optional(), // Report template
  data_analysis_struct: z.string().optional(), // Data analysis structure
  nguoi_lap: z.string().optional() // Creator
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_ct1: '', // From date
  ngay_ct2: '', // To date
  ct_vt: false, // Material details flag
  so_du: false, // Balance calculation flag

  groupBy: [], // dùng cho MultiSelect

  mau_bc: 20, // Report template (default value from backend)
  data_analysis_struct: '', // Data analysis structure
  nguoi_lap: '' // Creator
};
