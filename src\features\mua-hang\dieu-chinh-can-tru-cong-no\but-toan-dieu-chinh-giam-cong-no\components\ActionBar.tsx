import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';
interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON><PERSON> toán điều chỉnh giảm công nợ</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton
      title='Sửa'
      icon={Pencil}
      onClick={onEditClick}
      // disabled={isEditDisabled}
    />
    <AritoActionButton
      title='Xóa'
      icon={Trash}
      onClick={onDeleteClick}
      // disabled={isDeleteDisabled}
    />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Binoculars} onClick={onSearchClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: RefreshCw,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: () => {},
          group: 0
        },
        {
          title: 'In nhiều',
          icon: Printer,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: FileDown,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Tải mẫu Excel',
          icon: FileSpreadsheet,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: FileUp,
          onClick: () => {},
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);
