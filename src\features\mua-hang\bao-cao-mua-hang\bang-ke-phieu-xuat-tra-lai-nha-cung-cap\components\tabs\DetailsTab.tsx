import { useFormContext } from 'react-hook-form';
import { useState } from 'react';
import {
  warehouseSearchColumns,
  accountSearchColumns,
  customerSearchColumns,
  ngoaiTeSearchColumns
} from './cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const { setValue, getValues } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã kho:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Kho hàng'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              onValueChange={value => {
                setValue('ma_kho', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_kho', row.ma_kho);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản có:</Label>
          <div className='w-3/4'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan_co', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan_co', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản nợ:</Label>
          <div className='w-3/4'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan_no', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan_no', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã khách hàng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={customerSearchColumns}
              dialogTitle='Danh mục khách hàng'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              onValueChange={value => {
                setValue('ma_khach_hang', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_khach_hang', row.customer_code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Ngoại tệ:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
              searchColumns={ngoaiTeSearchColumns}
              dialogTitle='Ngoại tệ'
              columnDisplay='ma_nt'
              displayRelatedField='ten_nt'
              onValueChange={value => {
                setValue('ngoai_te', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ngoai_te', row.ma_nt);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải:</Label>
          <div className='w-2/3'>
            <FormField name='dien_giai' label='' type='text' className='w-full' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-1/2'>
            <FormField
              name='mau_bao_cao'
              label=''
              type='select'
              options={[
                { value: 'SL', label: 'Mẫu số lượng' },
                { value: 'SLGT', label: 'Mẫu số lượng và giá trị' },
                { value: 'SLGTNT', label: 'Mẫu số lượng và giá trị ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
