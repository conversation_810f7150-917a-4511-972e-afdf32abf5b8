import { z } from 'zod';

export const searchSchema = z.object({
  // Basic Info fields - mapped to backend API
  tu_ky: z.string().optional(),
  tu_nam: z.string().optional(),
  den_ky: z.string().optional(),
  den_nam: z.string().optional(),
  xu_ly: z.string().optional(),
  detailBy: z.string().optional(),

  // Details tab fields - mapped to backend API
  mau_bc: z.string().optional(),

  // Other tab fields
  data_analysis_struct: z.string().optional(),
  ma_unit: z.string().optional(),
  nguoi_lap: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  // Basic Info fields - mapped to backend API
  tu_ky: '',
  tu_nam: '',
  den_ky: '',
  den_nam: '',
  xu_ly: '1',
  detailBy: '1',

  // Details tab fields - mapped to backend API
  mau_bc: '20',

  // Other tab fields
  data_analysis_struct: '',
  ma_unit: '',
  nguoi_lap: ''
};
