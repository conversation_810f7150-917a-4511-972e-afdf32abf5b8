import { useState, useCallback, useEffect } from 'react';
import { BalanceSheetItem, BalanceSheetResponse, UseBalanceSheetReturn, SearchFormValues } from '../schema';
import api from '@/lib/api';

const generateMockData = (): BalanceSheetItem[] => {
  return [
    {
      id: '1',
      target: 'TÀI SẢN NGẮN HẠN',
      code: '100',
      description: 'I',
      company: 15420000000,
      total: 15420000000
    },
    {
      id: '2',
      target: 'Tiền và các khoản tương đương tiền',
      code: '110',
      description: 'I.1',
      company: 2*********,
      total: 2*********
    },
    {
      id: '3',
      target: 'Tiền',
      code: '111',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '4',
      target: '<PERSON><PERSON><PERSON> khoản tương đương tiền',
      code: '112',
      description: '',
      company: 2000000000,
      total: 2000000000
    },
    {
      id: '5',
      target: '<PERSON><PERSON><PERSON> tư tài chính ngắn hạn',
      code: '120',
      description: 'I.2',
      company: *********0,
      total: *********0
    },
    {
      id: '6',
      target: 'Chứng khoán kinh doanh',
      code: '121',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '7',
      target: 'Dự phòng giảm giá chứng khoán kinh doanh',
      code: '122',
      description: '',
      company: -50000000,
      total: -50000000
    },
    {
      id: '8',
      target: 'Đầu tư nắm giữ đến ngày đáo hạn',
      code: '123',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '9',
      target: 'Các khoản phải thu ngắn hạn',
      code: '130',
      description: 'I.3',
      company: 4200000000,
      total: 4200000000
    },
    {
      id: '10',
      target: 'Phải thu của khách hàng',
      code: '131',
      description: '',
      company: 3*********,
      total: 3*********
    },
    {
      id: '11',
      target: 'Trả trước cho người bán',
      code: '132',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '12',
      target: 'Phải thu nội bộ ngắn hạn',
      code: '133',
      description: '',
      company: 0,
      total: 0
    },
    {
      id: '13',
      target: 'Phải thu theo tiến độ kế hoạch hợp đồng xây dựng',
      code: '134',
      description: '',
      company: 0,
      total: 0
    },
    {
      id: '14',
      target: 'Các khoản phải thu khác',
      code: '135',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '15',
      target: 'Dự phòng phải thu ngắn hạn khó đòi',
      code: '139',
      description: '',
      company: -30000000,
      total: -30000000
    },
    {
      id: '16',
      target: 'Hàng tồn kho',
      code: '140',
      description: 'I.4',
      company: 5870000000,
      total: 5870000000
    },
    {
      id: '17',
      target: 'Hàng tồn kho',
      code: '141',
      description: '',
      company: *********0,
      total: *********0
    },
    {
      id: '18',
      target: 'Dự phòng giảm giá hàng tồn kho',
      code: '149',
      description: '',
      company: -*********,
      total: -*********
    },
    {
      id: '19',
      target: 'Tài sản ngắn hạn khác',
      code: '150',
      description: 'I.5',
      company: *********0,
      total: *********0
    },
    {
      id: '20',
      target: 'Chi phí trả trước ngắn hạn',
      code: '151',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '21',
      target: 'Thuế giá trị gia tăng được khấu trừ',
      code: '152',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '22',
      target: 'Thuế và các khoản khác phải thu Nhà nước',
      code: '154',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '23',
      target: 'Tài sản ngắn hạn khác',
      code: '158',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '24',
      target: 'TÀI SẢN DÀI HẠN',
      code: '200',
      description: 'II',
      company: 28*********,
      total: 28*********
    },
    {
      id: '25',
      target: 'Các khoản phải thu dài hạn',
      code: '210',
      description: 'II.1',
      company: *********,
      total: *********
    },
    {
      id: '26',
      target: 'Phải thu dài hạn của khách hàng',
      code: '211',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '27',
      target: 'Trả trước cho người bán dài hạn',
      code: '212',
      description: '',
      company: *********,
      total: *********
    },
    {
      id: '28',
      target: 'Vốn kinh doanh ở đơn vị trực thuộc',
      code: '213',
      description: '',
      company: 0,
      total: 0
    },
    {
      id: '29',
      target: 'Phải thu nội bộ dài hạn',
      code: '214',
      description: '',
      company: 0,
      total: 0
    },
    {
      id: '30',
      target: 'Phải thu dài hạn khác',
      code: '218',
      description: '',
      company: *********,
      total: *********
    }
  ];
};

const useBalanceSheetData = (): UseBalanceSheetReturn => {
  const [data, setData] = useState<BalanceSheetItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchParams, setSearchParams] = useState<SearchFormValues>({
    date: '',
    selected_report_template: 'balance_sheet_tt200',
    report_template: 'standard_currency',
    report_analysis_template: 'balance_sheet_unit'
  });

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);
    setSearchParams(searchParams);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BalanceSheetResponse>(
        '/tai-chinh/phan-tich-bao-cao-tai-chinh/bang-can-doi-ke-toan-theo-don-vi/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    searchParams,
    fetchData,
    refreshData
  };
};

export default useBalanceSheetData;
