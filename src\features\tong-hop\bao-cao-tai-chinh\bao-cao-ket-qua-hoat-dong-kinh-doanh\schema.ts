import { z } from 'zod';

export const SearchFormSchema = z.object({
  period_from: z.string().nonempty(),
  period_to: z.string().nonempty(),
  prev_period_from: z.string().nonempty(),
  prev_period_to: z.string().nonempty()
});
export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  period_from: new Date().toISOString(),
  period_to: new Date().toISOString(),
  prev_period_from: new Date().toISOString(),
  prev_period_to: new Date().toISOString()
};

// Business Report Item interface based on the provided fields
export interface BusinessReportItem {
  id: string;
  syspivot?: string;
  systotal?: string;
  sysprint?: string;
  sysorder?: number;
  stt_in?: string;
  ma_so: string;
  chi_tieu: string;
  thuyet_minh: string;
  xchi_tieu?: string;
  xchi_tieu2?: string;
  ky_nay: number | string;
  ky_truoc: number | string;
  tk?: string;
  tk_du?: string;
  tai_khoan?: string; // Mapped from tk/tk_du
  dau_cuoi?: string;
  drilldown_controller?: string;
  isSummary?: boolean;
}

export interface BusinessReportResponse {
  results: BusinessReportItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UseBusinessReportReturn {
  data: BusinessReportItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
