import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export interface BudgetReportItem {
  id: string;
  chi_tieu: string;
  ma_so: string;
  is_bold: boolean;
  // Trong kỳ
  ngan_sach_ky: number;
  trong_ky: number;
  chenh_lech_ky: number;
  ty_le_ky: number;
  // Lũy kế từ đầu năm
  ngan_sach_nam: number;
  tu_dau_nam: number;
  chenh_lech_nam: number;
  ty_le_nam: number;
}

export interface BudgetReportResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BudgetReportItem[];
}

export interface UseBudgetReportReturn {
  data: BudgetReportItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): BudgetReportItem[] => {
  return [
    {
      id: '1',
      chi_tieu: 'DOANH THU',
      ma_so: 'DT',
      is_bold: true,
      ngan_sach_ky: 1500000000,
      trong_ky: 1450000000,
      chenh_lech_ky: -50000000,
      ty_le_ky: 96.67,
      ngan_sach_nam: 18000000000,
      tu_dau_nam: 17500000000,
      chenh_lech_nam: -500000000,
      ty_le_nam: 97.22
    },
    {
      id: '2',
      chi_tieu: 'Doanh thu bán hàng',
      ma_so: 'DT01',
      is_bold: false,
      ngan_sach_ky: 1200000000,
      trong_ky: 1180000000,
      chenh_lech_ky: -20000000,
      ty_le_ky: 98.33,
      ngan_sach_nam: 14400000000,
      tu_dau_nam: 14200000000,
      chenh_lech_nam: -200000000,
      ty_le_nam: 98.61
    },
    {
      id: '3',
      chi_tieu: 'Doanh thu dịch vụ',
      ma_so: 'DT02',
      is_bold: false,
      ngan_sach_ky: 300000000,
      trong_ky: 270000000,
      chenh_lech_ky: -30000000,
      ty_le_ky: 90.0,
      ngan_sach_nam: 3600000000,
      tu_dau_nam: 3300000000,
      chenh_lech_nam: -300000000,
      ty_le_nam: 91.67
    },
    {
      id: '4',
      chi_tieu: 'CHI PHÍ',
      ma_so: 'CP',
      is_bold: true,
      ngan_sach_ky: 1200000000,
      trong_ky: 1150000000,
      chenh_lech_ky: 50000000,
      ty_le_ky: 95.83,
      ngan_sach_nam: 14400000000,
      tu_dau_nam: 13800000000,
      chenh_lech_nam: 600000000,
      ty_le_nam: 95.83
    },
    {
      id: '5',
      chi_tieu: 'Chi phí nhân sự',
      ma_so: 'CP01',
      is_bold: false,
      ngan_sach_ky: 500000000,
      trong_ky: 480000000,
      chenh_lech_ky: 20000000,
      ty_le_ky: 96.0,
      ngan_sach_nam: 6000000000,
      tu_dau_nam: 5800000000,
      chenh_lech_nam: 200000000,
      ty_le_nam: 96.67
    },
    {
      id: '6',
      chi_tieu: 'Chi phí vận hành',
      ma_so: 'CP02',
      is_bold: false,
      ngan_sach_ky: 300000000,
      trong_ky: 320000000,
      chenh_lech_ky: -20000000,
      ty_le_ky: 106.67,
      ngan_sach_nam: 3600000000,
      tu_dau_nam: 3700000000,
      chenh_lech_nam: -100000000,
      ty_le_nam: 102.78
    },
    {
      id: '7',
      chi_tieu: 'Chi phí marketing',
      ma_so: 'CP03',
      is_bold: false,
      ngan_sach_ky: 200000000,
      trong_ky: 190000000,
      chenh_lech_ky: 10000000,
      ty_le_ky: 95.0,
      ngan_sach_nam: 2400000000,
      tu_dau_nam: 2200000000,
      chenh_lech_nam: 200000000,
      ty_le_nam: 91.67
    },
    {
      id: '8',
      chi_tieu: 'Chi phí khác',
      ma_so: 'CP04',
      is_bold: false,
      ngan_sach_ky: 200000000,
      trong_ky: 160000000,
      chenh_lech_ky: 40000000,
      ty_le_ky: 80.0,
      ngan_sach_nam: 2400000000,
      tu_dau_nam: 2100000000,
      chenh_lech_nam: 300000000,
      ty_le_nam: 87.5
    },
    {
      id: '9',
      chi_tieu: 'LỢI NHUẬN',
      ma_so: 'LN',
      is_bold: true,
      ngan_sach_ky: 300000000,
      trong_ky: 300000000,
      chenh_lech_ky: 0,
      ty_le_ky: 100.0,
      ngan_sach_nam: 3600000000,
      tu_dau_nam: 3700000000,
      chenh_lech_nam: 100000000,
      ty_le_nam: 102.78
    }
  ];
};

/**
 * Custom hook for managing Budget Report data
 *
 * This hook provides functionality to fetch budget report data
 * with mock support for testing and development purposes.
 */
export function useBudgetReportData(searchParams: SearchFormValues): UseBudgetReportReturn {
  const [data, setData] = useState<BudgetReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BudgetReportResponse>(
        '/ngan-sach/bao-cao-ngan-sach/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
