// TypeScript interfaces for BangXacNhanCongNo (Debt Confirmation Statement)

export interface BangXacNhanCongNoItem {
  id?: string;
  ma_unit: string;
  ngay_ct: string;
  so_ct: string;
  tk: string;
  tk_du: string;
  dien_giai: string;
  ps_no: number;
  ps_co: number;
  du_no: number;
  du_co: number;
  ma_ct: string;
}

export interface BangXacNhanCongNoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangXacNhanCongNoItem[];
}

export interface BangXacNhanCongNoSearchFormValues {
  ngay_ct1?: Date;
  ngay_ct2?: Date;
  tk?: string;
  ngay_hd1?: Date;
  ngay_hd2?: Date;
  ma_kh?: string;
  so_du?: string;
  ct_yn?: string;
  mau_bc?: string;
  report_filtering?: string;
  data_analysis_struct?: string;
  [key: string]: any;
}

export interface UseBangXacNhanCongNoReturn {
  data: BangXacNhanCongNoItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangXacNhanCongNoSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
