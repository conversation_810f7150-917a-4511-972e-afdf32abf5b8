import React from 'react';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { FormField } from '@/components/custom/arito/form/form-field';

interface EmployeeSearchFieldProps {
  name?: string;
  label?: string;
  className?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

// Define the search columns for employees
const employeeSearchColumns: ExtendedGridColDef[] = [
  { field: 'employeeCode', headerName: 'Mã nhân viên', flex: 1 },
  { field: 'employeeName', headerName: 'Tên nhân viên', flex: 2 },
  { field: 'department', headerName: 'Phòng ban', flex: 1 },
  { field: 'position', headerName: 'Chức vụ', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 1 }
];

export const EmployeeSearchField: React.FC<EmployeeSearchFieldProps> = ({
  name = 'employeeCode',
  label = 'Mã nhân viên',
  className = 'grid grid-cols-[150px,1fr] items-center',
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <FormField
      className={className}
      label={label}
      name={name}
      type='text'
      searchEndpoint='hr/employees'
      searchColumns={employeeSearchColumns}
      defaultSearchColumn='employeeCode'
      disabled={disabled || formMode === 'view'}
    />
  );
};

export default EmployeeSearchField;
