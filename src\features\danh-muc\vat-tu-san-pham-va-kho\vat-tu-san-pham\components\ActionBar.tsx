import { Eye, FileText, <PERSON>cil, Pin, Plus, Printer, RefreshCcw, Search, Trash } from 'lucide-react';
import { useState } from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onViewClick?: () => void;
  onCopyClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  className?: string;
}

export function ActionBar({
  className,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onViewClick,
  onCopyClick,
  isViewDisabled = true
}: Props) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục vật tư, sản phẩm</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
      <AritoActionButton title='Xoá' icon={Trash} onClick={onDeleteClick} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick || (() => {})} />
      <AritoActionButton title='Refresh' icon={RefreshCcw} onClick={() => {}} />
      {/* <AritoActionButton title='Cố định cột' icon={Pin} onClick={() => {}} /> */}
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 1
          }
          // {
          //   title: 'Phân nhóm nhiều vật tư, sản phẩm',
          //   icon: <AritoIcon icon={774} />,
          //   onClick: () => {},
          //   group: 2
          // },
          // {
          //   title: 'Ẩn/hiển thị cây phân nhóm',
          //   icon: <AritoIcon icon={538} />,
          //   onClick: () => {},
          //   group: 2
          // }
        ]}
      />
    </AritoActionBar>
  );
}
