import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import type { BaoCaoSoDuCongNoItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BaoCaoSoDuCongNoItem[]): UseTableDataReturn {
  const calculateTotals = (items: BaoCaoSoDuCongNoItem[]) => {
    return items.reduce(
      (totals, item) => {
        return {
          du_no: totals.du_no + (item.du_no || 0),
          du_co: totals.du_co + (item.du_co || 0)
        };
      },
      {
        du_no: 0,
        du_co: 0
      }
    );
  };

  const totals = calculateTotals(data);

  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'customer_code',
            headerName: 'Mã NCC',
            width: 130
          },
          {
            field: 'customer_name',
            headerName: 'Tên nhà cung cấp',
            width: 250,
            cellClassName: (params: any) => (params.row.isTotalRow ? 'font-extrabold' : '')
          },

          {
            field: 'du_no',
            headerName: 'Dư nợ',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row.isTotalRow ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.du_no > 0 ? params.row.du_no : '';
            }
          },
          {
            field: 'du_co',
            headerName: 'Dư có',
            width: 140,
            type: 'number',
            cellClassName: (params: any) => (params.row.isTotalRow ? 'font-extrabold' : ''),
            renderCell: (params: any) => {
              return params.row.du_co > 0 ? params.row.du_co : '';
            }
          }
        ],
        rows: [
          {
            customer_code: '',
            customer_name: 'Tổng cộng',
            du_no: totals.du_no,
            du_co: totals.du_co,
            isTotalRow: true
          },
          ...data
        ]
      }
    ];

    return tableData;
  }, [data, totals]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
