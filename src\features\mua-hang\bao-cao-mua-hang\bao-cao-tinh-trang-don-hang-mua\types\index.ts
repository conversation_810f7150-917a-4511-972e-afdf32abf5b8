// TypeScript interfaces for Purchase Order Status Report

export interface PurchaseOrderStatusItem {
  // System fields
  syspivot: string;
  sysorder: number;
  sysprint: string;
  systotal: string;
  id: string;
  line: number;
  unit_id: string;

  // Document fields
  ngay_ct: string;
  so_ct: string;
  ma_kh: string;
  status: string;
  ma_ct: string;
  ma_vt: string;
  dvt: string;
  he_so: number;

  // Date and pricing fields
  ngay_giao: string;
  gia: number;
  tien: number;

  // Quantity fields
  so_luong: number;
  sl_nhap: number;
  sl_hd: number;
  sl_dh: number;
  sl_tl: number;
  sl_cl: number;

  // Name fields
  ten_kh: string;
  ten_vt: string;
  ten_ttct: string;
  ma_unit: string;
}

export interface PurchaseOrderStatusResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: PurchaseOrderStatusItem[];
}

export interface SearchFormValues {
  ngay_tu?: string;
  ngay_den?: string;
  so_chung_tu_tu?: string;
  so_chung_tu_den?: string;
  ma_nha_cung_cap?: string;
  nhom_nha_cung_cap?: string[];
  ma_vat_tu?: string;
  loai_vat_tu?: string;
  nhom_vat_tu?: string[];
  ma_kho?: string;
  han_giao_hang?: string;
  trang_thai?: string;
  mau_bao_cao?: string;
  loai_don_hang?: string;
  ma_giao_dich?: string;
  ma_lo?: string;
  ma_vi_tri?: string;
  dien_giai?: string;
  mau_loc_bao_cao?: string;
  [key: string]: any;
}

export interface UsePurchaseOrderStatusReturn {
  data: PurchaseOrderStatusItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
