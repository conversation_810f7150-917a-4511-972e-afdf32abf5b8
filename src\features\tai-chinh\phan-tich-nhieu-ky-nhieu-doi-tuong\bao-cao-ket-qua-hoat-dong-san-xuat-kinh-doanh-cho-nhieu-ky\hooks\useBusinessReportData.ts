import { useState, useCallback, useEffect } from 'react';
import { BusinessReportItem, BusinessReportResponse, SearchFormValues, UseBusinessReportReturn } from '../types';
import api from '@/lib/api';

const generateMockData = (): BusinessReportItem[] => {
  return [
    {
      id: '1',
      ma_so: '01',
      chi_tieu: '1. <PERSON><PERSON><PERSON> thu bán hàng và cung cấp dịch vụ',
      thang_1: 1000000000,
      thang_2: 1200000000,
      thang_3: 1100000000,
      thang_4: 1300000000,
      thang_5: 1150000000,
      thang_6: 1250000000,
      thang_7: 1400000000,
      thang_8: 1350000000,
      thang_9: 1500000000,
      thang_10: 1450000000,
      thang_11: 1600000000,
      thang_12: 1550000000,
      tong_cong: ***********
    },
    {
      id: '2',
      ma_so: '01A',
      chi_tieu: '- <PERSON><PERSON><PERSON> thu bán hàng và cung cấp dịch vụ',
      thang_1: 800000000,
      thang_2: 900000000,
      thang_3: 850000000,
      thang_4: 950000000,
      thang_5: 900000000,
      thang_6: 1000000000,
      thang_7: 1100000000,
      thang_8: 1050000000,
      thang_9: 1200000000,
      thang_10: 1150000000,
      thang_11: 1300000000,
      thang_12: 1250000000,
      tong_cong: 12450000000
    },
    {
      id: '3',
      ma_so: '02',
      chi_tieu: '2. Các khoản giảm trừ doanh thu',
      thang_1: 200000000,
      thang_2: 300000000,
      thang_3: 250000000,
      thang_4: 350000000,
      thang_5: 250000000,
      thang_6: 250000000,
      thang_7: 300000000,
      thang_8: 300000000,
      thang_9: 300000000,
      thang_10: 300000000,
      thang_11: 300000000,
      thang_12: 300000000,
      tong_cong: 3400000000
    },
    {
      id: '4',
      ma_so: '02A',
      chi_tieu: '+ Chiết khấu thương mại',
      thang_1: 100000000,
      thang_2: 150000000,
      thang_3: 120000000,
      thang_4: 180000000,
      thang_5: 120000000,
      thang_6: 120000000,
      thang_7: 150000000,
      thang_8: 150000000,
      thang_9: 150000000,
      thang_10: 150000000,
      thang_11: 150000000,
      thang_12: 150000000,
      tong_cong: 1690000000
    },
    {
      id: '5',
      ma_so: '02B',
      chi_tieu: '+ Giảm giá',
      thang_1: 50000000,
      thang_2: 80000000,
      thang_3: 70000000,
      thang_4: 90000000,
      thang_5: 70000000,
      thang_6: 70000000,
      thang_7: 80000000,
      thang_8: 80000000,
      thang_9: 80000000,
      thang_10: 80000000,
      thang_11: 80000000,
      thang_12: 80000000,
      tong_cong: 910000000
    },
    {
      id: '6',
      ma_so: '02C',
      chi_tieu: '+ Hàng bán bị trả lại',
      thang_1: 50000000,
      thang_2: 70000000,
      thang_3: 60000000,
      thang_4: 80000000,
      thang_5: 60000000,
      thang_6: 60000000,
      thang_7: 70000000,
      thang_8: 70000000,
      thang_9: 70000000,
      thang_10: 70000000,
      thang_11: 70000000,
      thang_12: 70000000,
      tong_cong: 800000000
    },
    {
      id: '7',
      ma_so: '10',
      chi_tieu: '3. Doanh thu thuần về bán hàng và cung cấp dịch vụ',
      thang_1: 800000000,
      thang_2: 900000000,
      thang_3: 850000000,
      thang_4: 950000000,
      thang_5: 900000000,
      thang_6: 1000000000,
      thang_7: 1100000000,
      thang_8: 1050000000,
      thang_9: 1200000000,
      thang_10: 1150000000,
      thang_11: 1300000000,
      thang_12: 1250000000,
      tong_cong: 12450000000
    },
    {
      id: '8',
      ma_so: '11',
      chi_tieu: '4. Giá vốn hàng bán',
      thang_1: 600000000,
      thang_2: 700000000,
      thang_3: 650000000,
      thang_4: 750000000,
      thang_5: 700000000,
      thang_6: 750000000,
      thang_7: 800000000,
      thang_8: 780000000,
      thang_9: 850000000,
      thang_10: 820000000,
      thang_11: 900000000,
      thang_12: 880000000,
      tong_cong: 9180000000
    },
    {
      id: '9',
      ma_so: '20',
      chi_tieu: '5. Lợi nhuận gộp về bán hàng và cung cấp dịch vụ',
      thang_1: 200000000,
      thang_2: 200000000,
      thang_3: 200000000,
      thang_4: 200000000,
      thang_5: 200000000,
      thang_6: 250000000,
      thang_7: 300000000,
      thang_8: 270000000,
      thang_9: 350000000,
      thang_10: 330000000,
      thang_11: 400000000,
      thang_12: 370000000,
      tong_cong: 3270000000
    },
    {
      id: '10',
      ma_so: '21',
      chi_tieu: '6. Doanh thu hoạt động tài chính',
      thang_1: 50000000,
      thang_2: 60000000,
      thang_3: 55000000,
      thang_4: 65000000,
      thang_5: 60000000,
      thang_6: 70000000,
      thang_7: 75000000,
      thang_8: 70000000,
      thang_9: 80000000,
      thang_10: 75000000,
      thang_11: 85000000,
      thang_12: 80000000,
      tong_cong: 825000000
    }
  ];
};

/**
 * Custom hook for managing Business Report data
 *
 * This hook provides functionality to fetch business report data
 * with mock support for testing and development purposes.
 */
export function useBusinessReportData(searchParams: SearchFormValues): UseBusinessReportReturn {
  const [data, setData] = useState<BusinessReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BusinessReportResponse>(
        '/tai-chinh/phan-tich-nhieu-ky-nhieu-doi-tuong/bao-cao-ket-qua-hoat-dong-san-xuat-kinh-doanh-cho-nhieu-ky/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
