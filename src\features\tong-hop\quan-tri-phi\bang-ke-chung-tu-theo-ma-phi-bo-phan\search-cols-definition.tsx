import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'object_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'debt', headerName: 'Công nợ p/thu', flex: 2 },
  { field: 'credit', headerName: 'Công nợ p/trả', flex: 2 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 2 },
  { field: 'email', headerName: 'Email', flex: 2 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 2 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'unit_name', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'id', headerName: 'ID', flex: 2 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account_name', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 2 },
  { field: 'ledger_account', headerName: 'Tài khoản sổ cái', flex: 2 },
  { field: 'is_group', headerName: 'Tài khoản chi tiết', checkboxSelection: true, flex: 2 },
  { field: 'account_level', headerName: 'Bậc tài khoản', flex: 2 }
];

export const currencySearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã ngoại tệ', flex: 1 },
  { field: 'currency_name', headerName: 'Tên ngoại tệ', flex: 2 }
];

export const documentCodeSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã chứng từ', flex: 1 },
  { field: 'document_code_name', headerName: 'Tên chứng từ', flex: 2 }
];

export const departmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'department_name', headerName: 'Tên bộ phận', flex: 2 }
];

export const caseSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã vụ việc', flex: 1 },
  { field: 'case_name', headerName: 'Tên vụ việc', flex: 2 }
];

export const contractSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã hợp đồng', flex: 1 },
  { field: 'contract_name', headerName: 'Tên hợp đồng', flex: 2 }
];

export const feeSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã phí', flex: 1 },
  { field: 'fee_name', headerName: 'Tên phí', flex: 2 }
];

export const paymentSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đợt thanh toán', flex: 1 },
  { field: 'payment_name', headerName: 'Tên đợt thanh toán', flex: 2 },
  { field: 'payment_date', headerName: 'Ngày thanh toán', flex: 2 },
  { field: 'payment_rate', headerName: 'Tỷ lệ (%)', flex: 2 },
  { field: 'payment_amount', headerName: 'Tiền', flex: 2 }
];

export const agreementSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã hợp đồng', flex: 1 },
  { field: 'agreement_name', headerName: 'Tên hợp đồng', flex: 2 }
];
