import { Printer, Search, Pin, RefreshCcw, FileText } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onPrintClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintClick?: () => void;
  searchParams?: any;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onPrintClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onEditPrintClick,
  searchParams
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Báo cáo công nợ theo hoá đơn</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <div className='mr-2 size-2 rounded-full bg-red-500' />
          Ngày báo cáo {(searchParams?.ngay_bc as Date).toLocaleDateString('vi-VN')}
        </span>
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}
    {onPrintClick && (
      <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} disabled={isViewDisabled} />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCcw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onExportClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
