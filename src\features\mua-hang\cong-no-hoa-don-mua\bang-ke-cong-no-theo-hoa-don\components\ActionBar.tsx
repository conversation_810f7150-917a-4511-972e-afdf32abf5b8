import { Pencil, Plus, RefreshCw, Table, FileDown, Printer } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import { AritoMenuActionButton } from './arito-menu-action-custom';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  printedSample: any[];
  onSearch: () => void;
  onCreate: () => void;
  onRefresh: () => void;
  onPinColumns: () => void;
  onPrintEditClick: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export function ActionBar({
  className,
  printedSample,
  onSearch,
  onCreate,
  onRefresh,
  onPinColumns,
  onPrintEditClick,
  isEditDisabled = true
}: Props) {
  return (
    <AritoActionBar titleComponent={<h1 className='relative text-xl font-bold'><PERSON><PERSON><PERSON> cáo công nợ theo hóa đơn</h1>}>
      <>
        <AritoActionButton title='Tìm kiếm' icon={Plus} onClick={onSearch} variant='primary' shortcut='Alt + N' />
        <AritoMenuActionButton
          title='In ấn'
          icon={Printer}
          items={printedSample.map(item => ({
            title: item.title,
            icon: <AritoIcon icon={247} />,
            onClick: () => {},
            group: 0
          }))}
        />
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} />
        <AritoActionButton title='Cố định cột' icon={Table} onClick={() => {}} />
        <AritoActionButton
          title='Kết xuất dữ liệu'
          icon={FileDown}
          shortcut='Alt + D, Ctrl + D'
          disabled={isEditDisabled}
        />
        <AritoMenuButton
          items={[
            {
              title: 'Chỉnh sửa mẫu in',
              icon: <AritoIcon icon={555} />,
              onClick: onPrintEditClick,
              group: 0
            }
          ]}
        />
      </>
    </AritoActionBar>
  );
}
