import { useState, useCallback } from 'react';
import {
  BangHachToanCCDCItem,
  BangHachToanCCDCResponse,
  BangHachToanCCDCSearchFormValues,
  UseBangHachToanCCDCReturn
} from '@/types/schemas/bang-hach-toan-ccdc.type';
import api from '@/lib/api';

/**
 * Generate mock data for CCDC accounting allocation
 */
const generateMockData = (): BangHachToanCCDCItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_cc: 'CC001',
      ten_cc: '<PERSON><PERSON><PERSON> tính xách tay Dell Latitude',
      tk_kh: '214',
      tk_cp: '642',
      tien: 2500000,
      ma_bp: 'BP001',
      ma_vv: 'VV001',
      ma_phi: 'PH001'
    },
    {
      id: '2',
      stt: 2,
      ma_cc: 'CC002',
      ten_cc: 'M<PERSON>y in laser HP LaserJet',
      tk_kh: '214',
      tk_cp: '642',
      tien: 1800000,
      ma_bp: 'BP002',
      ma_vv: 'VV002',
      ma_phi: 'PH002'
    },
    {
      id: '3',
      stt: 3,
      ma_cc: 'CC003',
      ten_cc: 'Bàn làm việc gỗ công nghiệp',
      tk_kh: '214',
      tk_cp: '642',
      tien: 3200000,
      ma_bp: 'BP001',
      ma_vv: 'VV003',
      ma_phi: 'PH001'
    },
    {
      id: '4',
      stt: 4,
      ma_cc: 'CC004',
      ten_cc: 'Ghế xoay văn phòng',
      tk_kh: '214',
      tk_cp: '642',
      tien: 1500000,
      ma_bp: 'BP003',
      ma_vv: 'VV001',
      ma_phi: 'PH003'
    },
    {
      id: '5',
      stt: 5,
      ma_cc: 'CC005',
      ten_cc: 'Điều hòa không khí Daikin',
      tk_kh: '214',
      tk_cp: '642',
      tien: 8500000,
      ma_bp: 'BP002',
      ma_vv: 'VV004',
      ma_phi: 'PH002'
    },
    {
      id: '6',
      stt: 6,
      ma_cc: 'CC006',
      ten_cc: 'Tủ tài liệu sắt 4 ngăn',
      tk_kh: '214',
      tk_cp: '642',
      tien: 2200000,
      ma_bp: 'BP001',
      ma_vv: 'VV002',
      ma_phi: 'PH001'
    },
    {
      id: '7',
      stt: 7,
      ma_cc: 'CC007',
      ten_cc: 'Máy photocopy Canon',
      tk_kh: '214',
      tk_cp: '642',
      tien: 12000000,
      ma_bp: 'BP003',
      ma_vv: 'VV005',
      ma_phi: 'PH003'
    },
    {
      id: '8',
      stt: 8,
      ma_cc: 'CC008',
      ten_cc: 'Máy chiếu Epson',
      tk_kh: '214',
      tk_cp: '642',
      tien: 4500000,
      ma_bp: 'BP002',
      ma_vv: 'VV003',
      ma_phi: 'PH002'
    },
    {
      id: '9',
      stt: 9,
      ma_cc: 'CC009',
      ten_cc: 'Tủ lạnh mini văn phòng',
      tk_kh: '214',
      tk_cp: '642',
      tien: 3800000,
      ma_bp: 'BP001',
      ma_vv: 'VV004',
      ma_phi: 'PH001'
    },
    {
      id: '10',
      stt: 10,
      ma_cc: 'CC010',
      ten_cc: 'Máy hủy tài liệu',
      tk_kh: '214',
      tk_cp: '642',
      tien: 1200000,
      ma_bp: 'BP003',
      ma_vv: 'VV001',
      ma_phi: 'PH003'
    }
  ];
};

/**
 * Custom hook for managing CCDC accounting allocation data
 */
export function useBangHachToanCCDC(initialSearchParams: BangHachToanCCDCSearchFormValues): UseBangHachToanCCDCReturn {
  const [data, setData] = useState<BangHachToanCCDCItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchParams, setSearchParams] = useState<BangHachToanCCDCSearchFormValues>(initialSearchParams);

  const fetchData = useCallback(async (searchParams: BangHachToanCCDCSearchFormValues) => {
    setIsLoading(true);
    setError(null);
    setSearchParams(searchParams);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangHachToanCCDCResponse>('/cong-cu/bang-hach-toan-phan-bo-ccdc/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
