import { z } from 'zod';

export const searchSchema = z.object({
  // Basic Info fields
  ky: z.string().optional(),
  nam: z.string().optional(),

  // Details Tab fields
  ma_ts: z.string().optional(),
  loai_ts: z.string().optional(),
  ma_bp_ts: z.string().optional(),
  nh_ts1: z.string().optional(),
  nh_ts2: z.string().optional(),
  nh_ts3: z.string().optional(),
  mau_bao_cao: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  // Basic Info fields
  ky: '5',
  nam: '2025',

  // Details Tab fields
  ma_ts: '',
  loai_ts: '',
  ma_bp_ts: '',
  nh_ts1: '',
  nh_ts2: '',
  nh_ts3: '',
  mau_bao_cao: 'TC'
};
