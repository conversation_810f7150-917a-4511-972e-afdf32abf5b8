import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants/search-columns';
import { Label } from '@/components/ui/label';
import { TaiKhoan } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  searchFieldStates: {
    account: TaiKhoan | null;
    setAccount: (account: <PERSON><PERSON><PERSON>an | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Từ ngày/Đến ngày:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản:</Label>
          <SearchField<TaiKhoan>
            type='text'
            columnDisplay={'code'}
            displayRelatedField={'name'}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            value={searchFieldStates.account?.code || ''}
            relatedFieldValue={searchFieldStates.account?.name || ''}
            onRowSelection={searchFieldStates.setAccount}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Kiểu xem:</Label>
          <FormField
            name='kieu_xem'
            type='select'
            options={[
              { value: 'nttkth', label: 'Nhóm theo tài khoản tổng hợp' },
              {
                value: 'ctttkht',
                label: 'Chi tiết theo tài khoản hạch toán'
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
