import React, { useState, useEffect, useMemo } from 'react';
import { GridRowParams } from '@mui/x-data-grid';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useCollectionDetailData } from './useCollectionDetailData';
import { collectionDetailReportColumns } from '../cols-definition';
import { SearchFormValues, CollectionDetailItem } from '../types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData } = useCollectionDetailData(searchParams);

  // Calculate totals and create summary row
  const dataWithTotals = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Calculate totals
    const totals = data.reduce(
      (acc, item) => ({
        totalAmount: acc.totalAmount + (item.totalAmount || 0),
        paidAmount: acc.paidAmount + (item.paidAmount || 0),
        remainingAmount: acc.remainingAmount + (item.remainingAmount || 0),
        paymentAmount: acc.paymentAmount + (item.paymentAmount || 0),
        foreignAmount: acc.foreignAmount + (item.foreignAmount || 0)
      }),
      {
        totalAmount: 0,
        paidAmount: 0,
        remainingAmount: 0,
        paymentAmount: 0,
        foreignAmount: 0
      }
    );

    const summaryRow: CollectionDetailItem = {
      id: 'summary',
      unit: '',
      isSummary: true,
      invoiceDate: '',
      invoiceNumber: '',
      customerCode: '',
      customerName: 'Tổng cộng',
      description: '',
      totalAmount: totals.totalAmount,
      paidAmount: totals.paidAmount,
      remainingAmount: totals.remainingAmount,
      paymentDate: '',
      paymentMethod: '',
      paymentAmount: totals.paymentAmount,
      paymentDescription: '',
      accountCode: '',
      accountName: '',
      employeeName: '',
      department: '',
      project: '',
      contract: '',
      paymentPhase: '',
      agreement: '',
      currency: '',
      exchangeRate: 0,
      foreignAmount: totals.foreignAmount,
      documentCode: ''
    };

    return [summaryRow, ...data];
  }, [data]);

  const handleRowClick = (params: GridRowParams) => {
    if (params.row.id === 'summary') return;

    const collectionDetail = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
    console.log('Row clicked:', collectionDetail);
  };

  const tables = [
    {
      name: 'Bảng kê chi tiết thu tiền theo hóa đơn',
      rows: dataWithTotals,
      columns: collectionDetailReportColumns(
        () => {},
        () => {}
      )
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
