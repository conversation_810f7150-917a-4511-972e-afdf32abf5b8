import { useFormContext } from 'react-hook-form';
import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { accountSearchColumns } from './cols-definition';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const BasicInfoTab: React.FC = () => {
  const { setValue } = useFormContext();
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Date Range Fields */}
        <div className='flex w-[61%] items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_tu' toDateName='ngay_den' />
          </div>
        </div>

        {/* Tài khoản */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Tài khoản đối ứng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản đối ứng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản đối ứng'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan_doi_ung', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan_doi_ung', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
