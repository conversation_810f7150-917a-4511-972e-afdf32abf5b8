import { useFormContext } from 'react-hook-form';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { customerGroupColumns, regionColumns } from '../../cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { customerSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* Khách hàng */}
        <div className='flex items-center'>
          <Label className='w-[140px] text-sm font-medium'>Mã khách hàng:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            searchColumns={customerSearchColumns}
            dialogTitle='Danh mục khách hàng'
            columnDisplay='customerCode'
            displayRelatedField='customerName'
            onValueChange={value => {
              setValue('ma_khach_hang', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('ma_khach_hang', row.customerCode);
                setValue('ten_khach_hang', row.customerName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[470px] max-w-full'
          />
        </div>

        {/* Nhóm khách hàng */}
        <div className='flex w-4/5 items-center'>
          <Label className='w-[140px] text-left'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}`}
                  searchColumns={customerGroupColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customerGroupCode'
                  displayRelatedField='customerGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}`}
                  searchColumns={customerGroupColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customerGroupCode'
                  displayRelatedField='customerGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}`}
                  searchColumns={customerGroupColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customerGroupCode'
                  displayRelatedField='customerGroupName'
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='so_du'
                label='Số dư'
                labelClassName='w-[140px]'
                type='select'
                options={[
                  { value: '0', label: '0. Tất cả' },
                  { value: '1', label: '1. Chỉ có hóa đơn số dư lớn hơn 0' },
                  { value: '2', label: '2. Chỉ những hóa đơn đã tất toán' }
                ]}
                className='w-[400px]'
                defaultValue={'0'}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='chi_tiet_thu_tien'
                label='Chi tiết thu tiền'
                labelClassName='w-[140px]'
                type='select'
                options={[
                  { value: '0', label: '0. Không' },
                  { value: '1', label: '1. Có' }
                ]}
                className='w-[400px]'
                defaultValue={'1'}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='mau_bao_cao'
                label='Mẫu báo cáo'
                labelClassName='w-[140px]'
                type='select'
                options={[
                  { value: 'TC', label: 'Mẫu tiền chuẩn' },
                  { value: 'NT', label: 'Mẫu ngoại tệ' }
                ]}
                className='w-[400px]'
                defaultValue={'TC'}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
