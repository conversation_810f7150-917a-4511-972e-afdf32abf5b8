import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>chHang, Group, KhuVuc } from '@/types/schemas';

/**
 * Hook for managing search field states in the bao-cao-so-du-cong-no feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // Account state (from BasicInfo tab)
  const [account, setAccount] = useState<TaiKhoan | null>(null);

  // Customer state (from DetailsTab)
  const [customer, setCustomer] = useState<KhachHang | null>(null);

  // Customer group states (from DetailsTab)
  const [customerGroup1, setCustomerGroup1] = useState<Group | null>(null);
  const [customerGroup2, setCustomerGroup2] = useState<Group | null>(null);
  const [customerGroup3, setCustomerGroup3] = useState<Group | null>(null);

  // Region state (from DetailsTab)
  const [region, setRegion] = useState<KhuVuc | null>(null);

  return {
    // Account
    account,
    setAccount,

    // Customer
    customer,
    setCustomer,

    // Customer groups
    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,

    // Region
    region,
    setRegion
  };
};
