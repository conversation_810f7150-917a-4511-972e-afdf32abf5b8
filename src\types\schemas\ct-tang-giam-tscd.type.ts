/**
 * TypeScript interfaces for CTTangGiamTSCD (Fixed Asset Increase/Decrease Detail Report)
 *
 * This interface represents the structure of the CTTangGiamTSCD model from the backend.
 * It defines detailed information about fixed asset increase/decrease transactions.
 */

import { ApiResponse } from '../api.type';

export interface CTTangGiamTSCDItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Asset code
   */
  ma_ts: string;

  /**
   * Asset name
   */
  ten_ts: string;

  /**
   * Purchase date
   */
  ngay_mua: string;

  /**
   * Number of depreciation periods
   */
  so_ky_kh: number;

  /**
   * Department name
   */
  ten_bp: string;

  /**
   * Asset type name
   */
  ten_lts: string;

  /**
   * Reason for increase/decrease
   */
  ten_tg_ts: string;

  /**
   * Original value
   */
  nguyen_gia: number;

  /**
   * Depreciated value
   */
  gt_da_kh: number;

  /**
   * Remaining value
   */
  gt_cl: number;
}

/**
 * API response type for CTTangGiamTSCD
 */
export interface CTTangGiamTSCDResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CTTangGiamTSCDItem[];
}

/**
 * Type for CTTangGiamTSCD API response
 */
export type CTTangGiamTSCDApiResponse = ApiResponse<CTTangGiamTSCDItem>;

/**
 * Hook return type for useCTTangGiamTSCD
 */
export interface UseCTTangGiamTSCDReturn {
  data: CTTangGiamTSCDItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
}
