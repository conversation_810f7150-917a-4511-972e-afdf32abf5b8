import { Button } from '@mui/material';
import MultiSelect from '@/components/custom/arito/form/multi-select';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
}
export function BasicInfoTab({ formMode }: BasicInfoProps) {
  return (
    <div className='min-w-[48vw] p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-4 md:space-y-4'>
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Loại giấy</Label>
            <div className='flex w-full gap-2'>
              <FormField
                type='select'
                name='paper_type'
                disabled={formMode === 'view'}
                options={[{ label: 'Thêm mới mẫu', value: 0 }]}
                defaultValue={0}
              />
              <Button
                type='button'
                form='edit-print-template-form'
                variant='outlined'
                size='small'
                sx={{
                  borderRadius: 0,
                  backgroundColor: 'transparent',
                  '&:hover': { backgroundColor: '#dcebfc' },
                  color: 'black',
                  borderColor: 'black'
                }}
              >
                Xóa mẫu
              </Button>
              <Button
                type='button'
                form='edit-print-template-form'
                variant='outlined'
                size='small'
                sx={{
                  borderRadius: 0,
                  backgroundColor: 'transparent',
                  '&:hover': { backgroundColor: '#dcebfc' },
                  color: 'black',
                  borderColor: 'black'
                }}
              >
                Sao chép mẫu
              </Button>
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nhập tên mẫu mới</Label>
            <FormField type='text' name='new_template_name' disabled={formMode === 'view'} />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên khác</Label>
            <FormField type='text' name='other_name' disabled={formMode === 'view'} />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mẫu</Label>
            <div className='flex w-full gap-2'>
              <FormField
                type='select'
                name='template'
                disabled={formMode === 'view'}
                options={[
                  {
                    label: 'Mẫu kế toán trưởng ký',
                    value: 0
                  },
                  {
                    label: 'Mẫu kế toán trưởng/giám đốc ký',
                    value: 1
                  },
                  {
                    label: 'Mẫu người lập ký',
                    value: 2
                  }
                ]}
                defaultValue={0}
              />
              <FormField
                type='select'
                name='paper_orientation'
                disabled={formMode === 'view'}
                options={[
                  {
                    label: 'Giấy in ngang',
                    value: 0
                  },
                  {
                    label: 'Giấy in đứng',
                    value: 1
                  }
                ]}
                defaultValue={0}
              />
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tiêu đề in</Label>
            <FormField type='text' name='print_title' disabled={formMode === 'view'} />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tiêu đề 2</Label>
            <FormField type='text' name='title_2' disabled={formMode === 'view'} />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tiêu đề phụ</Label>
            <FormField type='text' name='subtitle' disabled={formMode === 'view'} />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tiêu đề phụ 2</Label>
            <FormField type='text' name='subtitle_2' disabled={formMode === 'view'} />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mẫu song ngữ</Label>
            <div className='flex w-full items-center gap-4'>
              <FormField
                type='select'
                name='bilingual'
                disabled={formMode === 'view'}
                options={[
                  { label: 'Không', value: 0 },
                  { label: 'Có', value: 1 }
                ]}
                defaultValue={0}
              />
              <Label className='mb-0 mr-2'>In các dòng tổng</Label>
              <FormField
                type='select'
                name='print_totals'
                disabled={formMode === 'view'}
                options={[
                  { label: 'Có in', value: 0 },
                  { label: 'Không in', value: 1 }
                ]}
                defaultValue={0}
              />
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Loại mẫu</Label>
            <FormField
              type='select'
              name='template_type'
              disabled={formMode === 'view'}
              options={[
                {
                  label: 'Mẫu cho người sử dụng hiện tại',
                  value: 0
                },
                {
                  label: 'Mẫu chung cho tất cả người dùng',
                  value: 1
                }
              ]}
              defaultValue={0}
            />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên file được tạo</Label>
            <FormField type='text' name='generated_file_name' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default BasicInfoTab;
