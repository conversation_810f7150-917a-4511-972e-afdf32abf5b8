import { <PERSON>, FileText, <PERSON>cil, Lock, RefreshCcw, Pin } from 'lucide-react';
import { useState } from 'react';
import * as yup from 'yup';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAuthorization: () => void;
  onRefresh: () => void;
  onPinColumn: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export function ActionBar({ className, onAuthorization, onRefresh, onPinColumn, isEditDisabled = true }: Props) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  async function importFile(
    data: { [key: string]: any }[],
    id_column: string,
    doctype: string,
    option: string,
    maxError: number,
    schema: yup.ObjectSchema<any>,
    validation: (
      data: { [key: string]: any }[],
      schema: yup.ObjectSchema<any>
    ) => { type: string; message: string; position: string }[]
  ) {
    let errs = validation(data, schema);
    if (errs.length) {
      console.log(errs);
      return;
    }

    // Placeholder implementation - ERPNext functionality removed
    console.log('Import functionality disabled');
    console.log(errs);
  }

  return (
    <AritoActionBar
      titleComponent={<h1 className='text-xl font-bold'>Khai báo người sử dụng và phân quyền hệ thống</h1>}
    >
      <AritoActionButton title='Phân quyền' icon={Lock} onClick={onAuthorization} />
      <AritoActionButton title='Làm tươi' icon={RefreshCcw} onClick={onRefresh} />
      <AritoActionButton title='Ghim cột' icon={Pin} onClick={onPinColumn} disabled={isEditDisabled} />
    </AritoActionBar>
  );
}
