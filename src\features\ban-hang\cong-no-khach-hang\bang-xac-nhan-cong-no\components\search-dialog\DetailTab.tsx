import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { khachHangSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { DoiTuong } from '@/types/schemas';

interface DetailTabProps {
  khachHang: DoiTuong | null;
  setKhachHang: (khachHang: DoiTuong) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({ khachHang, setKhachHang }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Hoá đơn từ/đến</Label>
          <FormField type='date' name='ngay_hd1' />
          <FormField type='date' name='ngay_hd2' className='ml-2' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã khách hàng</Label>
          <SearchField<DoiTuong>
            name='ma_kh'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            dialogTitle='Danh mục đối tượng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            classNameRelatedField='w-full'
            onRowSelection={setKhachHang}
            value={khachHang?.customer_code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tính số dư</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='so_du'
              options={[
                { label: 'Có', value: '1' },
                { label: 'Không', value: '0' }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Xem chi tiết tài khoản</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='ct_yn'
              options={[
                { label: 'Có - Xem chi tiết tài khoản đối ứng', value: '1' },
                { label: 'Không - Xem tổng chứng từ', value: '0' }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='mau_bc'
              options={[
                { label: 'Mẫu tiền chuẩn', value: 'TC' },
                { label: 'Mẫu ngoại tệ', value: 'NT' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
