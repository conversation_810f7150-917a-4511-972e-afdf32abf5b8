import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BaoCaoSoSanhBanHangHaiKyItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BaoCaoSoSanhBanHangHaiKyItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_khach_hang',
            headerName: 'Mã khách hàng',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ma_khach_hang || '';
            }
          },
          {
            field: 'ten_khach_hang',
            headerName: 'Tên khách hàng',
            width: 200,
            renderCell: (params: any) => {
              return params.row.ten_khach_hang || '';
            }
          },
          {
            field: 'ma_san_pham',
            headerName: 'Mã sản phẩm',
            width: 120,
            renderCell: (params: any) => {
              return params.row.ma_san_pham || '';
            }
          },
          {
            field: 'ten_san_pham',
            headerName: 'Tên sản phẩm',
            width: 180,
            renderCell: (params: any) => {
              return params.row.ten_san_pham || '';
            }
          },
          {
            field: 'so_luong_ky_nay',
            headerName: 'SL kỳ này',
            width: 100,
            renderCell: (params: any) => {
              return params.row.so_luong_ky_nay?.toLocaleString() || '0';
            }
          },
          {
            field: 'so_luong_ky_truoc',
            headerName: 'SL kỳ trước',
            width: 100,
            renderCell: (params: any) => {
              return params.row.so_luong_ky_truoc?.toLocaleString() || '0';
            }
          },
          {
            field: 'chenh_lech_so_luong',
            headerName: 'Chênh lệch SL',
            width: 110,
            renderCell: (params: any) => {
              return params.row.chenh_lech_so_luong?.toLocaleString();
            }
          },
          {
            field: 'ty_le_so_luong',
            headerName: 'Tỷ lệ SL (%)',
            width: 100,
            renderCell: (params: any) => {
              return params.row.ty_le_so_luong?.toFixed(1);
            }
          },
          {
            field: 'doanh_thu_ky_nay',
            headerName: 'DT kỳ này',
            width: 120,
            renderCell: (params: any) => {
              return params.row.doanh_thu_ky_nay?.toLocaleString() || '0';
            }
          },
          {
            field: 'doanh_thu_ky_truoc',
            headerName: 'DT kỳ trước',
            width: 120,
            renderCell: (params: any) => {
              return params.row.doanh_thu_ky_truoc?.toLocaleString() || '0';
            }
          },
          {
            field: 'chenh_lech_doanh_thu',
            headerName: 'Chênh lệch DT',
            width: 120,
            renderCell: (params: any) => {
              return params.row.chenh_lech_so_luong?.toLocaleString() || 0;
            }
          },
          {
            field: 'ty_le_doanh_thu',
            headerName: 'Tỷ lệ DT (%)',
            width: 100,
            renderCell: (params: any) => {
              return params.row.ty_le_doanh_thu?.toFixed(1);
            }
          },
          {
            field: 'lai_ky_nay',
            headerName: 'Lãi kỳ này',
            width: 120,
            renderCell: (params: any) => {
              return params.row.lai_ky_nay?.toLocaleString() || '0';
            }
          },
          {
            field: 'lai_ky_truoc',
            headerName: 'Lãi kỳ trước',
            width: 120,
            renderCell: (params: any) => {
              return params.row.lai_ky_truoc?.toLocaleString() || '0';
            }
          },
          {
            field: 'chenh_lech_lai',
            headerName: 'Chênh lệch lãi',
            width: 120,
            renderCell: (params: any) => {
              return params.row.chenh_lech_lai?.toLocaleString();
            }
          },
          {
            field: 'ty_le_lai',
            headerName: 'Tỷ lệ lãi (%)',
            width: 100,
            renderCell: (params: any) => {
              return params.row.ty_le_lai?.toFixed(1);
            }
          }
        ],
        rows: data.map(item => ({
          id: item.id,
          ma_khach_hang: item.ma_khach_hang,
          ten_khach_hang: item.ten_khach_hang,
          ma_san_pham: item.ma_san_pham,
          ten_san_pham: item.ten_san_pham,
          so_luong_ky_nay: item.so_luong_ky_nay,
          so_luong_ky_truoc: item.so_luong_ky_truoc,
          chenh_lech_so_luong: item.chenh_lech_so_luong,
          ty_le_so_luong: item.ty_le_so_luong,
          doanh_thu_ky_nay: item.doanh_thu_ky_nay,
          doanh_thu_ky_truoc: item.doanh_thu_ky_truoc,
          chenh_lech_doanh_thu: item.chenh_lech_doanh_thu,
          ty_le_doanh_thu: item.ty_le_doanh_thu,
          lai_ky_nay: item.lai_ky_nay,
          lai_ky_truoc: item.lai_ky_truoc,
          chenh_lech_lai: item.chenh_lech_lai,
          ty_le_lai: item.ty_le_lai
        }))
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
