import { useState, useCallback, useEffect } from 'react';
import { BusinessReportItem, BusinessReportResponse, SearchFormValues, UseBusinessReportReturn } from '../schema';
import api from '@/lib/api';

const generateMockData = (): BusinessReportItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      chi_tieu: '<PERSON>anh thu bán hàng và cung cấp dịch vụ',
      thuyet_minh: '01',
      tien: ***********,
      ma_don_vi: 'CN001'
    },
    {
      id: '2',
      stt: 2,
      chi_tieu: '<PERSON><PERSON><PERSON> khoản giảm trừ doanh thu',
      thuyet_minh: '02',
      tien: 300000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '3',
      stt: 3,
      chi_tieu: '<PERSON>anh thu thuần về bán hàng và cung cấp dịch vụ',
      thuyet_minh: '03',
      tien: ***********,
      ma_don_vi: 'CN001'
    },
    {
      id: '4',
      stt: 4,
      chi_tieu: '<PERSON><PERSON><PERSON> vốn hàng bán',
      thuyet_minh: '04',
      tien: 9800000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '5',
      stt: 5,
      chi_tieu: 'Lợi nhuận gộp về bán hàng và cung cấp dịch vụ',
      thuyet_minh: '05',
      tien: 4900000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '6',
      stt: 6,
      chi_tieu: 'Doanh thu hoạt động tài chính',
      thuyet_minh: '06',
      tien: 150000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '7',
      stt: 7,
      chi_tieu: 'Chi phí tài chính',
      thuyet_minh: '07',
      tien: 80000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '8',
      stt: 8,
      chi_tieu: 'Chi phí bán hàng',
      thuyet_minh: '08',
      tien: 1200000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '9',
      stt: 9,
      chi_tieu: 'Chi phí quản lý doanh nghiệp',
      thuyet_minh: '09',
      tien: 800000000,
      ma_don_vi: 'CN001'
    },
    {
      id: '10',
      stt: 10,
      chi_tieu: 'Lợi nhuận thuần từ hoạt động kinh doanh',
      thuyet_minh: '10',
      tien: 2970000000,
      ma_don_vi: 'CN001'
    }
  ];
};

/**
 * Custom hook for managing Business Report by Unit data
 *
 * This hook provides functionality to fetch business report data
 * with mock support for testing and development purposes.
 */
export function useBusinessReportData(searchParams: SearchFormValues): UseBusinessReportReturn {
  const [data, setData] = useState<BusinessReportItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BusinessReportResponse>(
        '/tai-chinh/phan-tich-bao-cao-tai-chinh/bao-cao-ket-qua-hoat-dong-san-xuat-kinh-doanh-theo-don-vi/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
