import { useState } from 'react';
import { SearchFormValues } from '../schema';

export interface PrintTemplateData {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;
  showTable: boolean;
  searchParams: SearchFormValues;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchFormValues) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (data: PrintTemplateData) => void;
  setShowTable: (show: boolean) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues>({});

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchFormValues) => {
    setSearchParams(values);
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    setEditPrintTemplateDialogOpen(true);
  };

  const handleClosePrintTemplateDialog = () => {
    setEditPrintTemplateDialogOpen(false);
  };

  const handleSavePrintTemplate = (data: PrintTemplateData) => {
    setEditPrintTemplateDialogOpen(false);
  };

  return {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,
    setShowTable
  };
}
