'use client';

import { useEffect, useState } from 'react';
import Split from 'react-split';
import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { ActionBar, BasicInfoTab, ConfirmDialog, FormDialog } from './components';
import { AritoSplitPane } from '@/components/custom/arito/split-pane';
import AritoDataTables from '@/components/custom/arito/data-tables';
import SidebarButton from '@/components/arito/sidebar-button';
import { useAccount } from '@/hooks/queries/useAccount';
import { accountGroupColumns } from './cols-definition';
import AritoIcon from '@/components/custom/arito/icon';
import { useFormState, useAccountData } from './hooks';

// Constants
export const groupTypes = [
  {
    label: '1. Tài sản ngắn hạn',
    value: 'Tài sản ngắn hạn',
    shortCode: '1'
  },
  {
    label: '2. Tài sản dài hạn',
    value: 'Tài sản dài hạn',
    shortCode: '2'
  },
  {
    label: '3. Nợ phải trả',
    value: 'Nợ phải trả',
    shortCode: '3'
  },
  {
    label: '4. Vốn chủ sở hữu',
    value: 'Vốn chủ sở hữu',
    shortCode: '4'
  },
  {
    label: '5. Doanh thu',
    value: 'Doanh thu',
    shortCode: '5'
  },
  {
    label: '6. Chi phí sản xuất, kinh doanh',
    value: 'Chi phí sản xuất, kinh doanh',
    shortCode: '6'
  },
  {
    label: '7. Thu nhập khác',
    value: 'Thu nhập khác',
    shortCode: '7'
  },
  {
    label: '8. Chi phí khác',
    value: 'Chi phí khác',
    shortCode: '8'
  },
  {
    label: '9. Xác định kết quả kinh doanh',
    value: 'Xác định kết quả kinh doanh',
    shortCode: '9'
  },
  {
    label: 'N. Tài khoản ngoại bảng',
    value: 'Tài khoản ngoại bảng',
    shortCode: 'N'
  }
];

const DEFAULT_GROUP = groupTypes[0].value;

interface AccountGroupPageProps {
  initialData?: any[];
}

export default function AccountGroupPage({ initialData = [] }: AccountGroupPageProps) {
  // Use the useAccountData hook for real API data
  const { tableState, isLoading, handleRowClick, handleFilter } = useAccountData(DEFAULT_GROUP);

  // Use the useAccount hook for CRUD operations
  const { addAccount, updateAccount, deleteAccount } = useAccount();

  const {
    formState,
    showDelete,
    handleOpenViewForm,
    handleOpenEditForm,
    handleOpenAddForm,
    handleCloseForm,
    handleFormSubmit,
    handleCopyForm,
    handleDeleteForm,
    handleCloseDelete,
    handleDeleteConfirm
  } = useFormState(tableState);

  // Tables state
  const tables = [
    {
      name: '',
      rows: tableState.filteredRows,
      columns: accountGroupColumns,
      key: `${tableState.activeGroup}-${tableState.filteredRows.length}-${Date.now()}`
    }
  ];

  if (isLoading) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
      </div>
    );
  }

  return (
    <div className='flex h-screen w-screen flex-row overflow-hidden bg-white'>
      <AritoSplitPane split='vertical' minSize={200} defaultSize={300}>
        <div className='h-full overflow-y-auto border-r'>
          <div className='p-4'>
            <h2 className='mb-4 flex items-center gap-1 text-lg font-semibold'>
              {' '}
              <AritoIcon icon={539} /> Nhóm tài khoản
            </h2>
            <hr className='mb-4' />
            <ul>
              {groupTypes.map(group => (
                <li key={group.value} className='mb-2 text-sm'>
                  <SidebarButton
                    isActive={tableState.activeGroup === group.value}
                    onClick={() => handleFilter(group.value)}
                  >
                    {group.label}
                  </SidebarButton>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className='h-full overflow-y-auto'>
          <div className='flex h-full flex-col'>
            <ActionBar
              onAddClick={handleOpenAddForm}
              onEditClick={() => tableState.selectedObj && handleOpenEditForm(tableState.selectedObj)}
              isEditDisabled={!tableState.selectedObj}
              onDeleteClick={handleDeleteForm}
              onCopyClick={handleCopyForm}
              onFixedColumnsClick={() => {}}
              onViewClick={() => tableState.selectedObj && handleOpenViewForm(tableState.selectedObj)}
              isViewDisabled={!tableState.selectedObj}
            />
            <div className='flex-1 overflow-hidden'>
              <Split
                className='flex flex-1 flex-col overflow-hidden'
                direction='vertical'
                sizes={[50, 50]}
                minSize={200}
                gutterSize={8}
                gutterAlign='center'
                snapOffset={30}
                dragInterval={1}
                cursor='row-resize'
              >
                <div className='w-full overflow-hidden'>
                  <AritoDataTables
                    key={`${tableState.activeGroup}-${tableState.filteredRows.length}`}
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={tableState.selectedObj?.id}
                  />
                </div>
              </Split>
            </div>
          </div>
        </div>
      </AritoSplitPane>
      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={handleDeleteConfirm}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn muốn xoá không?'
        />
      )}
      {formState.show && (
        <FormDialog
          open={formState.show}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          onAdd={handleOpenAddForm}
          onEdit={() => tableState.selectedObj && handleOpenEditForm(tableState.selectedObj)}
          onDelete={handleDeleteForm}
          onCopy={handleCopyForm}
          formMode={formState.mode}
          currentObj={formState.currentObj}
        />
      )}
    </div>
  );
}
