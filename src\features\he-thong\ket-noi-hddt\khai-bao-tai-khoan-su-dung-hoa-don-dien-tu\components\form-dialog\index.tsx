import { Button } from '@mui/material';
import { useState } from 'react';
import { KhaiBaoSuDungTaiKhoanHoaDonDienTu, KhaiBaoSuDungTaiKhoanHoaDonDienTuInput } from '@/types/schemas';
import { AritoDialog, AritoIcon, AritoForm, BottomBar } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: KhaiBaoSuDungTaiKhoanHoaDonDienTuInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: KhaiBaoSuDungTaiKhoanHoaDonDienTu;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FormDialogProps) => {
  const [error, setError] = useState<string | null>(null);
  const [showValidationDialog, setShowValidationDialog] = useState<boolean>(false);

  const { taiKhoanHDDT, setTaiKhoanHDDT, user, setUser, unit, setUnit } = useSearchFieldStates(initialData);

  const handleSubmit = async (data: any) => {
    try {
      // Kiểm tra trường Mã tài khoản HĐĐT trước khi submit
      if (!taiKhoanHDDT || !taiKhoanHDDT.uuid) {
        setShowValidationDialog(true);
        return; // Không đóng form, chỉ hiển thị dialog thông báo
      }

      const updatedData: KhaiBaoSuDungTaiKhoanHoaDonDienTuInput = {
        ma_tkhddt: taiKhoanHDDT.uuid,
        user_id: user?.uuid || data.user_id,
        ma_unit: unit?.uuid || data.ma_unit,
        username: user?.username, // Use username from selected user
        e_mail: data.e_mail,
        status: data.status,
        unit_id: unit?.unit_id || null
      };
      onSubmit(updatedData);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra');
    }
  };

  const title =
    formMode === 'add'
      ? 'Thêm khai báo sử dụng tài khoản hóa đơn điện tử'
      : formMode === 'edit'
        ? 'Sửa khai báo sử dụng tài khoản hóa đơn điện tử'
        : 'Xem khai báo sử dụng tài khoản hóa đơn điện tử';

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='md'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        onSubmit={handleSubmit}
        initialData={
          formMode === 'add' && initialData
            ? {
                ...initialData,
                uuid: undefined, // Xóa uuid để tạo bản ghi mới
                ma_tkhddt: taiKhoanHDDT?.uuid || '',
                user_id: user?.uuid || null,
                ma_unit: unit?.uuid || null
              }
            : initialData
        }
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <BasicInfo
            formMode={formMode}
            taiKhoanHDDT={taiKhoanHDDT}
            user={user}
            unit={unit}
            setTaiKhoanHDDT={setTaiKhoanHDDT}
            setUser={setUser}
            setUnit={setUnit}
          />
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={formMode}
            onAdd={onAddButtonClick}
            onEdit={onEditButtonClick}
            onDelete={onDeleteButtonClick}
            onCopy={onCopyButtonClick}
            onSubmit={() => {}}
            onClose={onClose}
          />
        }
      />
      {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}

      {/* Dialog thông báo validation */}
      {showValidationDialog && (
        <AritoDialog
          open={showValidationDialog}
          onClose={() => setShowValidationDialog(false)}
          title='Thông báo'
          maxWidth='sm'
          disableBackdropClose={true}
          disableEscapeKeyDown={true}
          titleIcon={<AritoIcon icon={260} />}
          actions={
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              onClick={() => setShowValidationDialog(false)}
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
          }
        >
          <p className='min-w-96 p-4 text-base font-medium'>Mã tài khoản HĐĐT không được để trống</p>
        </AritoDialog>
      )}
    </AritoDialog>
  );
};

export default FormDialog;
