import { useState, useCallback } from 'react';
import {
  BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem,
  BaoCaoSoSanhNvlThucTeSoVoiDinhMucResponse,
  BaoCaoSoSanhNvlThucTeSoVoiDinhMucSearchFormValues,
  UseBaoCaoSoSanhNvlThucTeSoVoiDinhMucReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem[] => {
  return [
    {
      id: '1',
      ma_don_vi: 'CN01',
      ten_don_vi: 'Chi nhánh Hà Nội',
      ma_bo_phan: 'SX01',
      ten_bo_phan: 'Phân xưởng sản xuất 1',
      so_lenh_san_xuat: 'LSX2024001',
      ten_lenh_san_xuat: 'Sản xuất máy tính bảng ABC',
      ma_vat_tu: 'VT001',
      ten_vat_tu: 'Thép tấm dày 2mm',
      don_vi_tinh: 'kg',
      so_luong_dinh_muc: 100.0,
      gia_dinh_muc: 25000,
      tien_dinh_muc: 2500000,
      so_luong_thuc_te: 105.5,
      gia_thuc_te: 26000,
      tien_thuc_te: 2743000,
      so_luong_chenh_lech: 5.5,
      gia_chenh_lech: 1000,
      tien_chenh_lech: 243000,
      ty_le_chenh_lech_sl: 5.5,
      ty_le_chenh_lech_gia: 4.0,
      ty_le_chenh_lech_tien: 9.72,
      ngay_san_xuat: '2024-01-15',
      ngay_hoan_thanh: '2024-01-20',
      trang_thai: 'Hoàn thành',
      ghi_chu: 'Vượt định mức do chất lượng nguyên liệu'
    },
    {
      id: '2',
      ma_don_vi: 'CN01',
      ten_don_vi: 'Chi nhánh Hà Nội',
      ma_bo_phan: 'SX01',
      ten_bo_phan: 'Phân xưởng sản xuất 1',
      so_lenh_san_xuat: 'LSX2024002',
      ten_lenh_san_xuat: 'Sản xuất linh kiện điện tử XYZ',
      ma_vat_tu: 'VT002',
      ten_vat_tu: 'Nhựa ABS cao cấp',
      don_vi_tinh: 'kg',
      so_luong_dinh_muc: 50.0,
      gia_dinh_muc: 45000,
      tien_dinh_muc: 2250000,
      so_luong_thuc_te: 48.2,
      gia_thuc_te: 44000,
      tien_thuc_te: 2120800,
      so_luong_chenh_lech: -1.8,
      gia_chenh_lech: -1000,
      tien_chenh_lech: -129200,
      ty_le_chenh_lech_sl: -3.6,
      ty_le_chenh_lech_gia: -2.22,
      ty_le_chenh_lech_tien: -5.74,
      ngay_san_xuat: '2024-01-16',
      ngay_hoan_thanh: '2024-01-22',
      trang_thai: 'Hoàn thành',
      ghi_chu: 'Tiết kiệm nguyên liệu nhờ cải tiến quy trình'
    },
    {
      id: '3',
      ma_don_vi: 'CN02',
      ten_don_vi: 'Chi nhánh TP.HCM',
      ma_bo_phan: 'SX02',
      ten_bo_phan: 'Phân xưởng sản xuất 2',
      so_lenh_san_xuat: 'LSX2024003',
      ten_lenh_san_xuat: 'Sản xuất thiết bị y tế DEF',
      ma_vat_tu: 'VT003',
      ten_vat_tu: 'Inox 304 tấm mỏng',
      don_vi_tinh: 'kg',
      so_luong_dinh_muc: 75.0,
      gia_dinh_muc: 85000,
      tien_dinh_muc: 6375000,
      so_luong_thuc_te: 78.5,
      gia_thuc_te: 87000,
      tien_thuc_te: 6829500,
      so_luong_chenh_lech: 3.5,
      gia_chenh_lech: 2000,
      tien_chenh_lech: 454500,
      ty_le_chenh_lech_sl: 4.67,
      ty_le_chenh_lech_gia: 2.35,
      ty_le_chenh_lech_tien: 7.13,
      ngay_san_xuat: '2024-01-17',
      ngay_hoan_thanh: '2024-01-25',
      trang_thai: 'Đang sản xuất',
      ghi_chu: 'Tăng giá nguyên liệu do biến động thị trường'
    },
    {
      id: '4',
      ma_don_vi: 'CN02',
      ten_don_vi: 'Chi nhánh TP.HCM',
      ma_bo_phan: 'SX03',
      ten_bo_phan: 'Phân xưởng lắp ráp',
      so_lenh_san_xuat: 'LSX2024004',
      ten_lenh_san_xuat: 'Lắp ráp máy móc công nghiệp GHI',
      ma_vat_tu: 'VT004',
      ten_vat_tu: 'Ốc vít M8x20',
      don_vi_tinh: 'cái',
      so_luong_dinh_muc: 500.0,
      gia_dinh_muc: 2500,
      tien_dinh_muc: 1250000,
      so_luong_thuc_te: 520.0,
      gia_thuc_te: 2600,
      tien_thuc_te: 1352000,
      so_luong_chenh_lech: 20.0,
      gia_chenh_lech: 100,
      tien_chenh_lech: 102000,
      ty_le_chenh_lech_sl: 4.0,
      ty_le_chenh_lech_gia: 4.0,
      ty_le_chenh_lech_tien: 8.16,
      ngay_san_xuat: '2024-01-18',
      ngay_hoan_thanh: '2024-01-28',
      trang_thai: 'Đang sản xuất',
      ghi_chu: 'Tăng số lượng do yêu cầu dự phòng'
    },
    {
      id: '5',
      ma_don_vi: 'CN03',
      ten_don_vi: 'Chi nhánh Đà Nẵng',
      ma_bo_phan: 'SX01',
      ten_bo_phan: 'Phân xưởng sản xuất 1',
      so_lenh_san_xuat: 'LSX2024005',
      ten_lenh_san_xuat: 'Sản xuất đồ gia dụng JKL',
      ma_vat_tu: 'VT005',
      ten_vat_tu: 'Nhôm thanh định hình',
      don_vi_tinh: 'm',
      so_luong_dinh_muc: 200.0,
      gia_dinh_muc: 35000,
      tien_dinh_muc: 7000000,
      so_luong_thuc_te: 195.5,
      gia_thuc_te: 34500,
      tien_thuc_te: 6744750,
      so_luong_chenh_lech: -4.5,
      gia_chenh_lech: -500,
      tien_chenh_lech: -255250,
      ty_le_chenh_lech_sl: -2.25,
      ty_le_chenh_lech_gia: -1.43,
      ty_le_chenh_lech_tien: -3.65,
      ngay_san_xuat: '2024-01-19',
      ngay_hoan_thanh: '2024-01-30',
      trang_thai: 'Hoàn thành',
      ghi_chu: 'Tối ưu hóa cắt gọt, giảm phế liệu'
    },
    {
      id: '6',
      ma_don_vi: 'CN01',
      ten_don_vi: 'Chi nhánh Hà Nội',
      ma_bo_phan: 'SX02',
      ten_bo_phan: 'Phân xưởng sản xuất 2',
      so_lenh_san_xuat: 'LSX2024006',
      ten_lenh_san_xuat: 'Sản xuất linh kiện ô tô MNO',
      ma_vat_tu: 'VT006',
      ten_vat_tu: 'Cao su tổng hợp',
      don_vi_tinh: 'kg',
      so_luong_dinh_muc: 30.0,
      gia_dinh_muc: 120000,
      tien_dinh_muc: 3600000,
      so_luong_thuc_te: 32.5,
      gia_thuc_te: 125000,
      tien_thuc_te: 4062500,
      so_luong_chenh_lech: 2.5,
      gia_chenh_lech: 5000,
      tien_chenh_lech: 462500,
      ty_le_chenh_lech_sl: 8.33,
      ty_le_chenh_lech_gia: 4.17,
      ty_le_chenh_lech_tien: 12.85,
      ngay_san_xuat: '2024-01-20',
      ngay_hoan_thanh: '2024-02-05',
      trang_thai: 'Đang sản xuất',
      ghi_chu: 'Tăng định mức do yêu cầu chất lượng cao'
    },
    {
      id: '7',
      ma_don_vi: 'CN02',
      ten_don_vi: 'Chi nhánh TP.HCM',
      ma_bo_phan: 'SX01',
      ten_bo_phan: 'Phân xưởng sản xuất 1',
      so_lenh_san_xuat: 'LSX2024007',
      ten_lenh_san_xuat: 'Sản xuất thiết bị điện PQR',
      ma_vat_tu: 'VT007',
      ten_vat_tu: 'Dây đồng cách điện',
      don_vi_tinh: 'm',
      so_luong_dinh_muc: 1000.0,
      gia_dinh_muc: 15000,
      tien_dinh_muc: 15000000,
      so_luong_thuc_te: 980.0,
      gia_thuc_te: 14800,
      tien_thuc_te: 14504000,
      so_luong_chenh_lech: -20.0,
      gia_chenh_lech: -200,
      tien_chenh_lech: -496000,
      ty_le_chenh_lech_sl: -2.0,
      ty_le_chenh_lech_gia: -1.33,
      ty_le_chenh_lech_tien: -3.31,
      ngay_san_xuat: '2024-01-21',
      ngay_hoan_thanh: '2024-02-10',
      trang_thai: 'Hoàn thành',
      ghi_chu: 'Giảm hao hụt nhờ cải tiến kỹ thuật'
    },
    {
      id: '8',
      ma_don_vi: 'CN03',
      ten_don_vi: 'Chi nhánh Đà Nẵng',
      ma_bo_phan: 'SX02',
      ten_bo_phan: 'Phân xưởng sản xuất 2',
      so_lenh_san_xuat: 'LSX2024008',
      ten_lenh_san_xuat: 'Sản xuất đồ nội thất STU',
      ma_vat_tu: 'VT008',
      ten_vat_tu: 'Gỗ MDF chống ẩm',
      don_vi_tinh: 'm2',
      so_luong_dinh_muc: 150.0,
      gia_dinh_muc: 180000,
      tien_dinh_muc: 27000000,
      so_luong_thuc_te: 155.0,
      gia_thuc_te: 185000,
      tien_thuc_te: 28675000,
      so_luong_chenh_lech: 5.0,
      gia_chenh_lech: 5000,
      tien_chenh_lech: 1675000,
      ty_le_chenh_lech_sl: 3.33,
      ty_le_chenh_lech_gia: 2.78,
      ty_le_chenh_lech_tien: 6.2,
      ngay_san_xuat: '2024-01-22',
      ngay_hoan_thanh: '2024-02-15',
      trang_thai: 'Đang sản xuất',
      ghi_chu: 'Tăng dự phòng do thiết kế phức tạp'
    },
    {
      id: '9',
      ma_don_vi: 'CN01',
      ten_don_vi: 'Chi nhánh Hà Nội',
      ma_bo_phan: 'SX03',
      ten_bo_phan: 'Phân xưởng lắp ráp',
      so_lenh_san_xuat: 'LSX2024009',
      ten_lenh_san_xuat: 'Lắp ráp máy in 3D VWX',
      ma_vat_tu: 'VT009',
      ten_vat_tu: 'Nhựa PLA filament',
      don_vi_tinh: 'kg',
      so_luong_dinh_muc: 25.0,
      gia_dinh_muc: 350000,
      tien_dinh_muc: 8750000,
      so_luong_thuc_te: 24.2,
      gia_thuc_te: 345000,
      tien_thuc_te: 8349000,
      so_luong_chenh_lech: -0.8,
      gia_chenh_lech: -5000,
      tien_chenh_lech: -401000,
      ty_le_chenh_lech_sl: -3.2,
      ty_le_chenh_lech_gia: -1.43,
      ty_le_chenh_lech_tien: -4.58,
      ngay_san_xuat: '2024-01-23',
      ngay_hoan_thanh: '2024-02-20',
      trang_thai: 'Hoàn thành',
      ghi_chu: 'Tiết kiệm nguyên liệu nhờ tối ưu thiết kế'
    },
    {
      id: '10',
      ma_don_vi: 'CN02',
      ten_don_vi: 'Chi nhánh TP.HCM',
      ma_bo_phan: 'SX02',
      ten_bo_phan: 'Phân xưởng sản xuất 2',
      so_lenh_san_xuat: 'LSX2024010',
      ten_lenh_san_xuat: 'Sản xuất thiết bị âm thanh YZ',
      ma_vat_tu: 'VT010',
      ten_vat_tu: 'Mạch in PCB',
      don_vi_tinh: 'cái',
      so_luong_dinh_muc: 100.0,
      gia_dinh_muc: 75000,
      tien_dinh_muc: 7500000,
      so_luong_thuc_te: 102.0,
      gia_thuc_te: 76000,
      tien_thuc_te: 7752000,
      so_luong_chenh_lech: 2.0,
      gia_chenh_lech: 1000,
      tien_chenh_lech: 252000,
      ty_le_chenh_lech_sl: 2.0,
      ty_le_chenh_lech_gia: 1.33,
      ty_le_chenh_lech_tien: 3.36,
      ngay_san_xuat: '2024-01-24',
      ngay_hoan_thanh: '2024-02-25',
      trang_thai: 'Đang sản xuất',
      ghi_chu: 'Tăng dự phòng do tỷ lệ lỗi cao'
    }
  ];
};

/**
 * Custom hook for managing BaoCaoSoSanhNvlThucTeSoVoiDinhMuc (Material Actual vs Standard Comparison Report) data
 *
 * This hook provides functionality to fetch material comparison report data
 * with mock support for testing and development purposes.
 */
export function useBaoCaoSoSanhNvlThucTeSoVoiDinhMuc(
  searchParams: BaoCaoSoSanhNvlThucTeSoVoiDinhMucSearchFormValues
): UseBaoCaoSoSanhNvlThucTeSoVoiDinhMucReturn {
  const [data, setData] = useState<BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BaoCaoSoSanhNvlThucTeSoVoiDinhMucSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoSoSanhNvlThucTeSoVoiDinhMucResponse>(
        '/gia-thanh/bao-cao/bao-cao-so-sanh-nvl-thuc-te-so-voi-dinh-muc/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
