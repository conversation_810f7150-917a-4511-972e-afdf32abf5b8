import { useState, useCallback } from 'react';
import {
  BangKeChiTietTraTienChoHoaDonItem,
  BangKeChiTietTraTienChoHoaDonResponse,
  BangKeChiTietTraTienChoHoaDonSearchFormValues,
  UseBangKeChiTietTraTienChoHoaDonReturn
} from '@/types/schemas/bang-ke-chi-tiet-tra-tien-cho-cac-hoa-don.type';
import api from '@/lib/api';

const generateMockData = (): BangKeChiTietTraTienChoHoaDonItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      id_tt: 'TT001',
      id_ct: 'CT001',
      loai_tt: 'TM',
      unit_id: 'CN',
      ma_ct: 'PT',
      ngay_ct: '2024-01-15',
      so_ct: 'PT2024001',
      ngay_ct0: '2024-01-10',
      so_ct0: 'HD2024001',
      ma_kh: 'NCC001',
      ten_kh: 'Công ty TNHH ABC Technology',
      dien_giai: 'Thanh toán hóa đơn mua thiết bị máy tính',
      t_tt: 50000000,
      tt: 30000000,
      cl: 20000000,
      tt_yn: '0',
      ngay_tt: '2024-01-25',
      ma_tt: 'TM',
      han_tt: '2024-02-10',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 50000000,
      t_tt0: 50000000,
      ma_unit: 'CN'
    },
    {
      id: '2',
      stt: 2,
      id_tt: 'TT002',
      id_ct: 'CT002',
      loai_tt: 'CK',
      unit_id: 'CN',
      ma_ct: 'PC',
      ngay_ct: '2024-01-16',
      so_ct: 'PC2024001',
      ngay_ct0: '2024-01-11',
      so_ct0: 'HD2024002',
      ma_kh: 'NCC002',
      ten_kh: 'Công ty Cổ phần XYZ Solutions',
      dien_giai: 'Thanh toán hóa đơn mua phần mềm',
      t_tt: 25000000,
      tt: 25000000,
      cl: 0,
      tt_yn: '1',
      ngay_tt: '2024-01-20',
      ma_tt: 'CK',
      han_tt: '2024-02-11',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 25000000,
      t_tt0: 25000000,
      ma_unit: 'CN'
    },
    {
      id: '3',
      stt: 3,
      id_tt: 'TT003',
      id_ct: 'CT003',
      loai_tt: 'TT',
      unit_id: 'BN',
      ma_ct: 'UNC',
      ngay_ct: '2024-01-17',
      so_ct: 'UNC2024001',
      ngay_ct0: '2024-01-12',
      so_ct0: 'HD2024003',
      ma_kh: 'NCC003',
      ten_kh: 'Doanh nghiệp DEF Industries',
      dien_giai: 'Thanh toán hóa đơn mua nguyên vật liệu',
      t_tt: 75000000,
      tt: 45000000,
      cl: 30000000,
      tt_yn: '0',
      ngay_tt: '2024-03-17',
      ma_tt: 'TT',
      han_tt: '2024-02-12',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 75000000,
      t_tt0: 75000000,
      ma_unit: 'BN'
    },
    {
      id: '4',
      stt: 4,
      id_tt: 'TT004',
      id_ct: 'CT004',
      loai_tt: 'CK',
      unit_id: 'DN',
      ma_ct: 'PT',
      ngay_ct: '2024-01-18',
      so_ct: 'PT2024002',
      ngay_ct0: '2024-01-13',
      so_ct0: 'HD2024004',
      ma_kh: 'NCC004',
      ten_kh: 'Công ty TNHH GHI Manufacturing',
      dien_giai: 'Thanh toán hóa đơn mua thiết bị sản xuất',
      t_tt: 120000000,
      tt: 72000000,
      cl: 48000000,
      tt_yn: '0',
      ngay_tt: '2024-02-18',
      ma_tt: 'CK',
      han_tt: '2024-02-13',
      ma_nt: 'USD',
      ty_gia: 24500,
      t_tt_nt0: 4898,
      t_tt0: 120000000,
      ma_unit: 'DN'
    },
    {
      id: '5',
      stt: 5,
      id_tt: 'TT005',
      id_ct: 'CT005',
      loai_tt: 'TM',
      unit_id: 'CN',
      ma_ct: 'PC',
      ngay_ct: '2024-01-19',
      so_ct: 'PC2024002',
      ngay_ct0: '2024-01-14',
      so_ct0: 'HD2024005',
      ma_kh: 'NCC005',
      ten_kh: 'Tập đoàn JKL Group',
      dien_giai: 'Thanh toán hóa đơn mua dịch vụ tư vấn',
      t_tt: 80000000,
      tt: 80000000,
      cl: 0,
      tt_yn: '1',
      ngay_tt: '2024-01-24',
      ma_tt: 'TM',
      han_tt: '2024-02-14',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 80000000,
      t_tt0: 80000000,
      ma_unit: 'CN'
    },
    {
      id: '6',
      stt: 6,
      id_tt: 'TT006',
      id_ct: 'CT006',
      loai_tt: 'CK',
      unit_id: 'BN',
      ma_ct: 'UNC',
      ngay_ct: '2024-01-20',
      so_ct: 'UNC2024002',
      ngay_ct0: '2024-01-15',
      so_ct0: 'HD2024006',
      ma_kh: 'NCC006',
      ten_kh: 'Công ty MNO Trading',
      dien_giai: 'Thanh toán hóa đơn mua hàng hóa',
      t_tt: 35000000,
      tt: 21000000,
      cl: 14000000,
      tt_yn: '0',
      ngay_tt: '2024-02-20',
      ma_tt: 'CK',
      han_tt: '2024-02-15',
      ma_nt: 'EUR',
      ty_gia: 27000,
      t_tt_nt0: 1296,
      t_tt0: 35000000,
      ma_unit: 'BN'
    },
    {
      id: '7',
      stt: 7,
      id_tt: 'TT007',
      id_ct: 'CT007',
      loai_tt: 'TT',
      unit_id: 'DN',
      ma_ct: 'PT',
      ngay_ct: '2024-01-21',
      so_ct: 'PT2024003',
      ngay_ct0: '2024-01-16',
      so_ct0: 'HD2024007',
      ma_kh: 'NCC007',
      ten_kh: 'Doanh nghiệp PQR Services',
      dien_giai: 'Thanh toán hóa đơn mua dịch vụ bảo trì',
      t_tt: 15000000,
      tt: 15000000,
      cl: 0,
      tt_yn: '1',
      ngay_tt: '2024-01-26',
      ma_tt: 'TT',
      han_tt: '2024-02-16',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 15000000,
      t_tt0: 15000000,
      ma_unit: 'DN'
    },
    {
      id: '8',
      stt: 8,
      id_tt: 'TT008',
      id_ct: 'CT008',
      loai_tt: 'CK',
      unit_id: 'CN',
      ma_ct: 'PC',
      ngay_ct: '2024-01-22',
      so_ct: 'PC2024003',
      ngay_ct0: '2024-01-17',
      so_ct0: 'HD2024008',
      ma_kh: 'NCC008',
      ten_kh: 'Công ty STU Logistics',
      dien_giai: 'Thanh toán hóa đơn dịch vụ vận chuyển',
      t_tt: 12000000,
      tt: 7200000,
      cl: 4800000,
      tt_yn: '0',
      ngay_tt: '2024-02-22',
      ma_tt: 'CK',
      han_tt: '2024-02-17',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 12000000,
      t_tt0: 12000000,
      ma_unit: 'CN'
    },
    {
      id: '9',
      stt: 9,
      id_tt: 'TT009',
      id_ct: 'CT009',
      loai_tt: 'TM',
      unit_id: 'BN',
      ma_ct: 'UNC',
      ngay_ct: '2024-01-23',
      so_ct: 'UNC2024003',
      ngay_ct0: '2024-01-18',
      so_ct0: 'HD2024009',
      ma_kh: 'NCC009',
      ten_kh: 'Tổng công ty VWX Corporation',
      dien_giai: 'Thanh toán hóa đơn mua máy móc thiết bị',
      t_tt: 200000000,
      tt: 100000000,
      cl: 100000000,
      tt_yn: '0',
      ngay_tt: '2024-04-23',
      ma_tt: 'TM',
      han_tt: '2024-02-18',
      ma_nt: 'USD',
      ty_gia: 24500,
      t_tt_nt0: 8163,
      t_tt0: 200000000,
      ma_unit: 'BN'
    },
    {
      id: '10',
      stt: 10,
      id_tt: 'TT010',
      id_ct: 'CT010',
      loai_tt: 'CK',
      unit_id: 'DN',
      ma_ct: 'PT',
      ngay_ct: '2024-01-24',
      so_ct: 'PT2024004',
      ngay_ct0: '2024-01-19',
      so_ct0: 'HD2024010',
      ma_kh: 'NCC010',
      ten_kh: 'Công ty YZ Global',
      dien_giai: 'Thanh toán hóa đơn mua văn phòng phẩm',
      t_tt: 8000000,
      tt: 8000000,
      cl: 0,
      tt_yn: '1',
      ngay_tt: '2024-01-29',
      ma_tt: 'CK',
      han_tt: '2024-02-19',
      ma_nt: 'VND',
      ty_gia: 1,
      t_tt_nt0: 8000000,
      t_tt0: 8000000,
      ma_unit: 'DN'
    }
  ];
};

/**
 * Custom hook for managing BangKeChiTietTraTienChoHoaDon (Detailed Payment Report for Invoices) data
 *
 * This hook provides functionality to fetch detailed payment data for invoices
 * with mock support for testing and development purposes.
 */
export function useBangKeChiTietTraTienChoHoaDon(
  searchParams: BangKeChiTietTraTienChoHoaDonSearchFormValues
): UseBangKeChiTietTraTienChoHoaDonReturn {
  const [data, setData] = useState<BangKeChiTietTraTienChoHoaDonItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangKeChiTietTraTienChoHoaDonSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangKeChiTietTraTienChoHoaDonResponse>(
        '/mua-hang/cong-no-hoa-don-mua/bang-ke-chi-tiet-tra-tien-cho-cac-hoa-don/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
