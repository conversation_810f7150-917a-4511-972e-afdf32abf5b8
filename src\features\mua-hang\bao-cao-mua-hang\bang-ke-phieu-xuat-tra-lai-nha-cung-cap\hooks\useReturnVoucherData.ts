import { useState, useCallback, useEffect } from 'react';
import { ReturnVoucherItem, SearchFormValues, UseReturnVoucherReturn, ReturnVoucherResponse } from '../types';
import api from '@/lib/api';

export type { UseReturnVoucherReturn };

const generateMockData = (): ReturnVoucherItem[] => {
  // Regular data rows
  const mockData: ReturnVoucherItem[] = [
    {
      // System fields
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 1,
      stt: 1,
      id: '1',
      unit_id: 'UNIT001',

      // Document fields
      ma_ct: 'PXT2024001',
      ngay_ct: '2024-01-15',
      so_ct: 'PXT2024001',

      // Entity codes
      ma_kh: 'NCC001',
      ma_vt: 'VT001',
      ma_kho: 'KH001',
      ma_bp: 'BP001',
      ma_vv: 'VV001',
      ma_hd: 'HD2024001',
      ma_ku: 'KU2024001',
      ma_phi: 'PHI001',
      ma_sp: 'SP001',
      ma_lsx: 'LSX2024001',
      ma_dtt: 'DTT001',
      ma_cp0: 'CP001',
      ma_unit: 'CN001',

      // Transaction fields
      sl_xuat: 5,
      gia: 25000000,
      tien_xuat: 125000000,
      thue: 12500000,
      dien_giai: 'Trả lại thép tấm không đúng độ dày',
      tk_du: '3311',

      // Display names
      ten_kh: 'Công ty TNHH Thép Việt Nam',
      ten_vt: 'Thép tấm A36 dày 10mm',
      dvt: 'Tấm',

      // Mapped fields for backward compatibility
      don_vi: 'CN001',
      ngay_ctu: '2024-01-15',
      so_luong: 5,
      don_gia: 25000000,
      thanh_tien: 125000000,
      tk_no: '3311',
      bo_phan: 'Phòng sản xuất',
      vu_viec: 'Dự án máy ép thủy lực',
      hop_dong: 'HD2024001',
      dot_thanh_toan: 'Đợt 1',
      khe_uoc: 'KU2024001',
      phi: 2500000,
      sanh_pham: 'Máy ép thủy lực 100T',
      lenh_san_xuat: 'LSX2024001',
      'c/p_khong_hop_le': 'CP001',
      ma_ctu: 'GD001',

      isSummary: false
    },
    {
      // System fields
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 2,
      stt: 2,
      id: '2',
      unit_id: 'UNIT001',

      // Document fields
      ma_ct: 'PXT2024002',
      ngay_ct: '2024-01-16',
      so_ct: 'PXT2024002',

      // Entity codes
      ma_kh: 'NCC002',
      ma_vt: 'VT002',
      ma_kho: 'KH002',
      ma_bp: 'BP002',
      ma_vv: 'VV001',
      ma_hd: 'HD2024001',
      ma_ku: 'KU2024001',
      ma_phi: 'PHI002',
      ma_sp: 'SP001',
      ma_lsx: 'LSX2024001',
      ma_dtt: 'DTT001',
      ma_cp0: 'CP002',
      ma_unit: 'CN001',

      // Transaction fields
      sl_xuat: 500,
      gia: 500,
      tien_xuat: 250000,
      thue: 25000,
      dien_giai: 'Trả lại ốc vít thừa',
      tk_du: '3312',

      // Display names
      ten_kh: 'Công ty CP Ốc vít Hà Nội',
      ten_vt: 'Ốc vít M8x20',
      dvt: 'Cái',

      // Mapped fields for backward compatibility
      don_vi: 'CN001',
      ngay_ctu: '2024-01-16',
      so_luong: 500,
      don_gia: 500,
      thanh_tien: 250000,
      tk_no: '3312',
      bo_phan: 'Phòng lắp ráp',
      vu_viec: 'Dự án máy ép thủy lực',
      hop_dong: 'HD2024001',
      dot_thanh_toan: 'Đợt 1',
      khe_uoc: 'KU2024001',
      phi: 50000,
      sanh_pham: 'Máy ép thủy lực 100T',
      lenh_san_xuat: 'LSX2024001',
      'c/p_khong_hop_le': 'CP002',
      ma_ctu: 'GD002',

      isSummary: false
    },
    {
      // System fields
      syspivot: '',
      systotal: '',
      sysprint: '',
      sysorder: 3,
      stt: 3,
      id: '3',
      unit_id: 'UNIT002',

      // Document fields
      ma_ct: 'PXT2024003',
      ngay_ct: '2024-01-17',
      so_ct: 'PXT2024003',

      // Entity codes
      ma_kh: 'NCC003',
      ma_vt: 'VT003',
      ma_kho: 'KH003',
      ma_bp: 'BP003',
      ma_vv: 'VV002',
      ma_hd: 'HD2024002',
      ma_ku: 'KU2024002',
      ma_phi: 'PHI003',
      ma_sp: 'SP002',
      ma_lsx: 'LSX2024002',
      ma_dtt: 'DTT001',
      ma_cp0: 'CP003',
      ma_unit: 'CN002',

      // Transaction fields
      sl_xuat: 100,
      gia: 45000,
      tien_xuat: 4500000,
      thue: 450000,
      dien_giai: 'Trả lại dây điện bị đứt',
      tk_du: '3313',

      // Display names
      ten_kh: 'Công ty TNHH Điện Miền Bắc',
      ten_vt: 'Dây điện đồng 2.5mm²',
      dvt: 'Mét',

      // Mapped fields for backward compatibility
      don_vi: 'CN002',
      ngay_ctu: '2024-01-17',
      so_luong: 100,
      don_gia: 45000,
      thanh_tien: 4500000,
      tk_no: '3313',
      bo_phan: 'Phòng điện',
      vu_viec: 'Dự án tủ điều khiển',
      hop_dong: 'HD2024002',
      dot_thanh_toan: 'Đợt 1',
      khe_uoc: 'KU2024002',
      phi: 225000,
      sanh_pham: 'Tủ điều khiển PLC',
      lenh_san_xuat: 'LSX2024002',
      'c/p_khong_hop_le': 'CP003',
      ma_ctu: 'GD003',

      isSummary: false
    }
  ];

  // Calculate totals for summary row
  const totalSoLuong = mockData.reduce((sum, item) => sum + (item.sl_xuat || 0), 0);
  const totalThanhTien = mockData.reduce((sum, item) => sum + (item.tien_xuat || 0), 0);
  const totalThue = mockData.reduce((sum, item) => sum + (item.thue || 0), 0);
  const totalPhi = mockData.reduce((sum, item) => sum + (item.phi || 0), 0);

  // Summary row as first row
  const summaryRow: ReturnVoucherItem = {
    // System fields
    syspivot: '',
    systotal: 'true',
    sysprint: '',
    sysorder: 0,
    stt: 0,
    id: 'summary',
    unit_id: '',

    // Document fields
    ma_ct: '',
    ngay_ct: '',
    so_ct: '',

    // Entity codes
    ma_kh: '',
    ma_vt: '',
    ma_kho: '',
    ma_bp: '',
    ma_vv: '',
    ma_hd: '',
    ma_ku: '',
    ma_phi: '',
    ma_sp: '',
    ma_lsx: '',
    ma_dtt: '',
    ma_cp0: '',
    ma_unit: '',

    // Transaction fields
    sl_xuat: totalSoLuong,
    gia: 0,
    tien_xuat: totalThanhTien,
    thue: totalThue,
    dien_giai: '',
    tk_du: '',

    // Display names
    ten_kh: 'Tổng cộng',
    ten_vt: '',
    dvt: '',

    // Mapped fields for backward compatibility
    don_vi: '',
    ngay_ctu: '',
    so_luong: totalSoLuong,
    don_gia: 0,
    thanh_tien: totalThanhTien,
    tk_no: '',
    bo_phan: '',
    vu_viec: '',
    hop_dong: '',
    dot_thanh_toan: '',
    khe_uoc: '',
    phi: totalPhi,
    sanh_pham: '',
    lenh_san_xuat: '',
    'c/p_khong_hop_le': '',
    ma_ctu: '',

    isSummary: true
  };

  // Return summary row first, then regular data
  return [summaryRow, ...mockData];
};

/**
 * Custom hook for managing Return Voucher Report data
 *
 * This hook provides functionality to fetch return voucher data
 * with mock support for testing and development purposes.
 */
export function useReturnVoucherData(searchParams: SearchFormValues): UseReturnVoucherReturn {
  const [data, setData] = useState<ReturnVoucherItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<ReturnVoucherResponse>(
        '/mua-hang/bao-cao-mua-hang/bang-ke-phieu-xuat-tra-lai-nha-cung-cap/',
        {
          mock: true,
          mockData: mockData,
          params: searchParams
        }
      );

      // Handle both direct array and wrapped response
      const results = Array.isArray(response.data) ? response.data : response.data.results;
      setData(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
