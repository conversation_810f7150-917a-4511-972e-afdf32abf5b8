import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import SaveTemplateDialog from '../SaveTemplateDialog';
import { Label } from '@/components/ui/label';

const DetailTab: React.FC = () => {
  const [saveTemplateDialogOpen, setSaveTemplateDialogOpen] = useState(false);

  const handleSaveNewTemplate = () => {
    setSaveTemplateDialogOpen(true);
  };

  const handleSaveTemplateClose = () => {
    setSaveTemplateDialogOpen(false);
  };

  const handleSaveTemplate = (data: { ten_bao_cao: string; ten_khac?: string }) => {
    console.log('Saving new template:', data);
    // Here you would typically save the template to your backend
    // Example API call:
    // saveTemplateToBackend(data)
    //   .then(() => {
    //     // Show success message
    //     // Refresh template list
    //   })
    //   .catch(error => {
    //     // Handle error
    //   });

    setSaveTemplateDialogOpen(false);
  };

  return (
    <div className='p-6'>
      <div className='grid grid-cols-1 gap-x-6 gap-y-6'>
        {/* Báo cáo selection with dropdown */}
        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Chọn báo cáo</label>
          <div className='flex w-full items-center gap-2'>
            <div className='w-2/3'>
              <FormField
                name='id_maubc'
                type='select'
                className='w-full'
                options={[
                  { value: 'BCNS04', label: 'Báo cáo ngân sách' },
                  { value: 'BCNS05', label: 'Báo cáo chi tiết ngân sách' },
                  { value: 'BCNS06', label: 'Báo cáo tổng hợp ngân sách' }
                ]}
              />
            </div>
            <div className='flex h-9 w-9 flex-shrink-0 items-center justify-center'>
              <RadixHoverDropdown
                iconNumber={'...'}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mẫu báo cáo mới',
                    icon: 7,
                    onClick: handleSaveNewTemplate
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Sửa mẫu đang chọn',
                    icon: 9,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Mẫu báo cáo selection */}
        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mẫu báo cáo</label>
          <div className='w-2/3'>
            <FormField
              name='mau_bc'
              type='select'
              className='w-full'
              options={[
                {
                  value: '20',
                  label: 'Mẫu tiêu chuẩn'
                },
                {
                  value: '21',
                  label: 'Mẫu chi tiết'
                }
              ]}
            />
          </div>
        </div>
      </div>

      {/* Save Template Dialog */}
      <SaveTemplateDialog open={saveTemplateDialogOpen} onClose={handleSaveTemplateClose} onSave={handleSaveTemplate} />
    </div>
  );
};

export default DetailTab;
