import { FileText, Lock, RefreshCw, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onViewClick: () => void;
  isViewDisabled?: boolean;
}

export const ActionBar = ({ onViewClick, isViewDisabled = true }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON><PERSON> cáo tổng hợp chi phí so sánh giữa 2 kỳ</h1>}>
    <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => {}} disabled={isViewDisabled} />
    <AritoActionButton title='Cố định cột' icon={Lock} onClick={() => {}} disabled={isViewDisabled} />
    <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={() => {}} disabled={isViewDisabled} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: () => {},
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
