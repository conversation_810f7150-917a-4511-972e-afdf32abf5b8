// TypeScript interfaces for Detailed Collection Report

export interface CollectionDetailItem {
  id: string;
  unit: string;
  invoiceDate: string;
  invoiceNumber: string;
  customerCode: string;
  customerName: string;
  description: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentDate: string;
  paymentMethod: string;
  paymentAmount: number;
  paymentDescription: string;
  accountCode: string;
  accountName: string;
  employeeName: string;
  department: string;
  project: string;
  contract: string;
  paymentPhase: string;
  agreement: string;
  currency: string;
  exchangeRate: number;
  foreignAmount: number;
  documentCode: string;
  isSummary: boolean;
}

export interface CollectionDetailResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CollectionDetailItem[];
}

export interface SearchFormValues {
  ngay_h_toan_tu?: string;
  ngay_h_toan_den?: string;
  duoc_thanh_toan_de?: string;
  tai_khoan?: string;
  ma_khach_hang?: string;
  nhom_khach_hang_1?: string;
  nhom_khach_hang_2?: string;
  nhom_khach_hang_3?: string;
  khu_vuc?: string;
  so_du?: string;
  chi_tiet_thu_tien?: string;
  mau_bao_cao?: string;
  so_c_tu_tu?: string;
  so_c_tu_den?: string;
  mau_loc_bao_cao?: string;
  mau_phan_tich_dl?: string;
  nguoi_lap?: string;
  [key: string]: any;
}

export interface UseCollectionDetailReturn {
  data: CollectionDetailItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
