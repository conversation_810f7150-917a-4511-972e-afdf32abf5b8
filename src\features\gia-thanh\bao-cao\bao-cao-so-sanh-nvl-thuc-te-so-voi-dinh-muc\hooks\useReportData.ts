import { useEffect, useState } from 'react';
import { useBaoCaoSoSanhNvlThucTeSoVoiDinhMuc } from './useBaoCaoSoSanhNvlThucTeSoVoiDinhMuc';
import { useActionHandlers } from './useActionHandlers';
import { useDialogState } from './useDialogState';
import { useTableData } from './useTableData';
import { SearchFormValues } from '../schema';

export function useReportData() {
  const [searchParams, setSearchParams] = useState<SearchFormValues>({});
  const dialogState = useDialogState();
  const { data, isLoading, error, fetchData } = useBaoCaoSoSanhNvlThucTeSoVoiDinhMuc(searchParams);
  const tableData = useTableData(data);
  const actionHandlers = useActionHandlers();

  // Initialize table data when the component mounts
  useEffect(() => {
    // Fetch initial data with empty search params to show sample data
    fetchData({});
  }, [fetchData]);

  const handleSearch = (params: SearchFormValues) => {
    setSearchParams(params);
    fetchData(params);
  };

  return {
    initialSearchDialogOpen: dialogState.initialSearchDialogOpen,
    showTable: dialogState.showTable,

    handleInitialSearchClose: dialogState.handleInitialSearchClose,
    handleInitialSearch: (values: SearchFormValues) => {
      handleSearch(values);
      dialogState.handleInitialSearch(values);
    },
    handleSearchClick: dialogState.handleSearchClick,
    handleEditPrintTemplateClick: dialogState.handleEditPrintTemplateClick,

    tables: tableData.tables,
    handleRowClick: tableData.handleRowClick,
    isLoading,
    error,

    handleRefreshClick: () => {
      fetchData(searchParams);
      actionHandlers.handleRefreshClick();
    },
    handleFixedColumnsClick: actionHandlers.handleFixedColumnsClick,
    handleExportDataClick: actionHandlers.handleExportDataClick
  };
}
