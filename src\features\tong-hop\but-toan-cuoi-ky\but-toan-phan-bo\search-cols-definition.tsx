import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'object_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'debt', headerName: 'Công nợ p/thu', flex: 2 },
  { field: 'credit', headerName: 'Công nợ p/trả', flex: 2 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 2 },
  { field: 'email', headerName: 'Email', flex: 2 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 2 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'unit_name', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'id', headerName: 'ID', flex: 2 }
];
