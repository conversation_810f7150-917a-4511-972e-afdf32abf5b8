import { But<PERSON> } from '@mui/material';
import React from 'react';
import { searchFormSchema, SearchFormValues } from '../../schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { BasicInfoTab } from './BasicInfoTab';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: SearchFormValues) => void;
}

function SearchDialog({ open, onClose, onSearch }: SearchDialogProps) {
  const handleSubmit = (data: SearchFormValues) => {
    const processedData = {
      ...data,
      period: Number(data.period)
    };

    onSearch(processedData);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='<PERSON><PERSON>o cáo doanh thu chi phí'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchFormSchema}
        onSubmit={handleSubmit}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab formMode='add' />
                }
              ]}
              className='border-b border-b-gray-200'
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
}

export default SearchDialog;
