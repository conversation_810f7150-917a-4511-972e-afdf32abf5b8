import { Checkbox } from '@mui/material';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const toolSubjectSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    align: 'center',
    headerAlign: 'center',
    renderHeader: params => (
      <div style={{ display: 'flex', width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}>
        <Checkbox size='small' onChange={e => {}} />
      </div>
    )
  },
  { field: 'Mã đối tượng', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'Tên đối tượng', headerName: 'Tên đối tượng', flex: 1 },
  { field: 'Công nợ p/thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: '<PERSON>ông nợ p/trả', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'Mã số thuế', headerName: '<PERSON>ã số thuế', flex: 1 },
  { field: 'Email', headerName: 'Email', flex: 1 },
  { field: 'Số điện thoại', headerName: 'Số điện thoại', flex: 1 }
];
