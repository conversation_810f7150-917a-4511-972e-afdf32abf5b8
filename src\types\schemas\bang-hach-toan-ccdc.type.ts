/**
 * TypeScript interfaces for BangHachToanPhanBoCCDC (CCDC Accounting Allocation Report)
 *
 * This interface represents the structure for tool and equipment accounting allocation data
 * used in the BangHachToanPhanBoCCDC feature.
 */

import { ApiResponse } from '../api.type';

/**
 * Interface for individual CCDC accounting allocation item
 */
export interface BangHachToanCCDCItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Serial number
   */
  stt: number;

  /**
   * Tool code
   */
  ma_cc: string;

  /**
   * Tool name
   */
  ten_cc: string;

  /**
   * Allocation account
   */
  tk_kh: string;

  /**
   * Expense account
   */
  tk_cp: string;

  /**
   * Amount
   */
  tien: number;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * Task code
   */
  ma_vv: string;

  /**
   * Fee code
   */
  ma_phi: string;
}

/**
 * Interface for API response
 */
export interface BangHachToanCCDCResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangHachToanCCDCItem[];
}

/**
 * Interface for search form values
 */
export interface BangHachToanCCDCSearchFormValues {
  /**
   * From period
   */
  tu_ky?: string;

  /**
   * From year
   */
  tu_nam?: string;

  /**
   * To period
   */
  den_ky?: string;

  /**
   * To year
   */
  den_nam?: string;

  /**
   * Report template
   */
  mau_bc?: number;

  /**
   * Data analysis structure
   */
  data_analysis_struct?: number;

  /**
   * Tool code filter
   */
  ma_cc?: string;

  /**
   * Tool type filter
   */
  ma_lcc?: string;

  /**
   * Department filter
   */
  ma_bp?: string;

  /**
   * Tool group 1
   */
  nh_cc1?: string;

  /**
   * Tool group 2
   */
  nh_cc2?: string;

  /**
   * Tool group 3
   */
  nh_cc3?: string;

  /**
   * Unit code
   */
  ma_unit?: string;

  [key: string]: any;
}

/**
 * Interface for hook return type
 */
export interface UseBangHachToanCCDCReturn {
  data: BangHachToanCCDCItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangHachToanCCDCSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * Type for API response
 */
export type BangHachToanCCDCApiResponse = ApiResponse<BangHachToanCCDCItem>;
