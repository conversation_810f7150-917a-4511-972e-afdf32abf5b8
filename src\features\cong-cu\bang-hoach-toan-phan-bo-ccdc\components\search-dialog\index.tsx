import React from 'react';
import { initialValues, SearchFormValues, searchSchema } from '../../schemas';
import { AritoHeaderTabs, BottomBar } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { useSearchFieldStates } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

function SearchDialog({ open, onClose, onSearch }: SearchDialogProps) {
  const searchFieldStates = useSearchFieldStates();

  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo doanh thu chi phí'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        classNameBottomBar='relative flex w-full justify-end gap-2'
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab formMode='add' />
                }
              ]}
              className='border-b border-b-gray-200'
            />
          </div>
        }
      />
    </AritoDialog>
  );
}

export default SearchDialog;
