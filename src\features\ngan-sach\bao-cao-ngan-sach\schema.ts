import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  ky: z.string().min(1, '<PERSON><PERSON> không được để trống'),
  nam: z.string().min(1, 'Năm không được để trống'),
  id_maubc: z.string().min(1, '<PERSON><PERSON><PERSON> báo cáo không được để trống'),
  mau_bc: z.string().min(1, 'Mẫu báo cáo không được để trống')
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ky: '3',
  nam: '2025',
  id_maubc: 'BCNS04',
  mau_bc: '20'
};
