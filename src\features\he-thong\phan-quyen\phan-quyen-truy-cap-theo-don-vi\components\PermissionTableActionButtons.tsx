import { Divider, IconButton, Tooltip } from '@mui/material';
import { GridRowSelectionModel } from '@mui/x-data-grid';
import AritoIcon from '@/components/custom/arito/icon';

const buttonSx = {
  color: 'white',
  padding: 0,
  minWidth: '24px',
  width: '24px',
  height: '24px',
  border: '1px solid transparent',
  borderRadius: 0,
  cursor: 'default',
  '&:hover': {
    border: '1.3px solid rgb(171, 211, 239)',
    backgroundColor: 'rgba(25, 118, 210, 0.15)',
    color: '#007acc',
    borderRadius: 0
  },
  '&.Mui-disabled': {
    color: 'rgba(0, 0, 0, 0.26)'
  }
};

interface AritoInputTableActionButtonsProps {
  errorMessage?: string;
  rowSelectionModel: GridRowSelectionModel;
  onAddRow?: () => void;
  onDeleteRow?: () => void;
  onCopyRow?: () => void;
  onPasteRow?: () => void;
  onMoveRow?: (direction: 'up' | 'down') => void;
}

export default function PermissionTableActionButtons({
  rowSelectionModel,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onMoveRow,
  errorMessage
}: AritoInputTableActionButtonsProps) {
  return (
    <div className='flex h-8 flex-1 items-center justify-between'>
      <div className='flex items-center'>
        <Tooltip title='Thêm dòng mới' arrow>
          <IconButton size='small' onClick={onAddRow} sx={buttonSx}>
            <AritoIcon icon={7} />
          </IconButton>
        </Tooltip>

        <Tooltip title='Xóa dòng đã chọn' arrow>
          <IconButton size='small' onClick={onDeleteRow} disabled={rowSelectionModel.length === 0} sx={buttonSx}>
            <AritoIcon icon={8} />
          </IconButton>
        </Tooltip>

        <div className='flex items-center'>
          <Tooltip title='Sao chép dòng' arrow>
            <span>
              <IconButton size='small' onClick={onCopyRow} disabled={rowSelectionModel.length !== 1} sx={buttonSx}>
                <AritoIcon icon={11} />
              </IconButton>
            </span>
          </Tooltip>
        </div>

        <div className='flex items-center'>
          <Tooltip title='Di chuyển lên' arrow>
            <span>
              <IconButton
                size='small'
                onClick={() => onMoveRow?.('up')}
                disabled={rowSelectionModel.length !== 1}
                sx={buttonSx}
              >
                <AritoIcon icon={3} />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title='Di chuyển xuống' arrow>
            <span>
              <IconButton
                size='small'
                onClick={() => onMoveRow?.('down')}
                disabled={rowSelectionModel.length !== 1}
                sx={buttonSx}
              >
                <AritoIcon icon={30} />
              </IconButton>
            </span>
          </Tooltip>
        </div>

        <Divider orientation='vertical' flexItem sx={{ mx: 1 }} />

        <Tooltip title='Cố định cột' arrow>
          <span>
            <IconButton size='small' onClick={() => {}} disabled={rowSelectionModel.length === 0} sx={buttonSx}>
              <AritoIcon icon={16} />
            </IconButton>
          </span>
        </Tooltip>
      </div>

      {errorMessage && <div className='ml-2 text-xs font-medium text-red-500'>{errorMessage}</div>}
    </div>
  );
}
