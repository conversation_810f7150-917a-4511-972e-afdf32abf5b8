import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { printFormSchema, PrintFormData } from '../schemas/print-form-schema';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import AritoNotifyModal from '@/components/arito/arito-notify-modal';
import { ChungTuBiXoaState } from '../hooks/use-chung-tu-bi-xoa';
import { generalJournalFilterSchema } from '../schemas';
import AritoIcon from '@/components/custom/arito/icon';
import { useModal } from '../contexts/modal-context';
import { AddingPrintForm } from './AddingPrintForm';
import { MainTab } from './filter-tabs/MainTab';

interface ModalsProps {
  state: ChungTuBiXoaState;
  initialFormData: any;
  actions: {
    handleCloseFormPrint: () => void;
    handleCloseInitialForm: () => void;
    handleInitialFormSubmit: (data: any) => void;
  };
}

export const Modals = ({ state, initialFormData, actions }: ModalsProps) => {
  const { confirmModal, notifyModal, setConfirmModal, setNotifyModal } = useModal();
  console.log('Confirm Modal:', confirmModal);
  return (
    <>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal({ open: false })}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoNotifyModal
        open={notifyModal.open}
        onClose={() => setNotifyModal({ open: false })}
        onConfirm={notifyModal.onConfirm}
        title={notifyModal.title}
        message={notifyModal.message}
      />

      {/* Initial Filter Form - Must be completed before seeing data */}
      <AritoPopupForm<any>
        key='initial-filter-form'
        open={state.showInitialForm}
        onClose={actions.handleCloseInitialForm}
        mode='add'
        title='Các chứng từ bị xóa'
        titleIcon={<AritoIcon icon={12} />}
        initialData={initialFormData}
        onSubmit={actions.handleInitialFormSubmit}
        schema={generalJournalFilterSchema}
        headerFields={
          <div className='p-4'>
            <MainTab />
          </div>
        }
        maxWidth='md'
        fullWidth={true}
      />

      {/* View print form */}
      <AritoPopupForm<PrintFormData>
        open={state.formPrint}
        onClose={actions.handleCloseFormPrint}
        title='Mẫu in'
        titleIcon={<AritoIcon icon={625} />}
        mode='add'
        schema={printFormSchema}
        maxWidth='lg'
        fullWidth={true}
        initialData={{
          paperType: '',
          newPrintSampleName: '',
          otherName: '',
          period: '0',
          period_year: '0',
          printTitle: '',
          printTitle2: '',
          subTitle: '',
          subTitle2: '',
          bilingualMode: '0',
          printTotalLines: '0',
          printStatus: '0',
          templateType: '0',
          generatedFileName: ''
        }}
        tabs={<AddingPrintForm />}
        onSubmit={(data: PrintFormData) => {
          try {
            console.log('Form submitted', data);
            actions.handleCloseFormPrint();
          } catch (error) {
            console.error('Error submitting form:', error);
          }
        }}
      />
    </>
  );
};
