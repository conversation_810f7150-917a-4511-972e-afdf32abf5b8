import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Basic fields
  tu_thang: z.string().min(1, 'Từ tháng không được để trống'),
  den_thang: z.string().min(1, '<PERSON>ến tháng không được để trống'),
  nam: z.string().min(1, 'Năm không được để trống'),
  thue_suat: z.string().optional(),
  tai_khoan_thue: z.string().optional(),
  tai_khoan_doi_ung: z.string().optional(),
  ma_doi_tuong: z.string().optional(),
  cuc_thue: z.string().optional(),
  loai_bao_cao: z.string().optional(),
  tinh_chat_thue: z.string().optional(),
  sap_xep: z.string().optional(),
  phan_loai: z.string().optional(),
  mau_bao_cao: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

// Tax report data types
export interface TaxReportItem {
  id: string;
  sysorder?: number | string;
  sysprint?: number | string;
  systotal?: number | string;
  excel?: number | string;
  stt: number | string;
  line?: number | string;
  so_ct2?: string;
  ngay_ct: Date | string | null;
  ngay_lct?: Date | string | null;
  ma_ct?: string;
  so_ct: string;
  ma_nt?: string;
  ma_so_thue: string;
  ten_vt_thue: string;
  t_tien2: number | string;
  t_thue: number | string;
  thue_suat: string;
  ma_thue?: string;
  nhom_thue?: string;
  ten_unit?: string;
  ten_unit2?: string;
  ma_ct0?: string;
  ten_kh: string;
  ghi_chu: string;
  ten_thue?: string;
  ten_thue2?: string;
  ten_nh?: string;
  ten_nh2?: string;
  ma_mau_ct?: string;
  stt2?: number | string;
  ten_kh2?: string;
  xten_kh?: string;
  xten_kh2?: string;
  xid?: string;
  isSummary?: boolean;
}

export interface TaxReportResponse {
  results: TaxReportItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UseTaxReportReturn {
  data: TaxReportItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export const initialValues: SearchFormValues = {
  tu_thang: '1',
  den_thang: '12',
  nam: new Date().getFullYear().toString(),
  thue_suat: '',
  tai_khoan_thue: '',
  tai_khoan_doi_ung: '',
  ma_doi_tuong: '',
  cuc_thue: '',
  loai_bao_cao: '',
  tinh_chat_thue: '',
  sap_xep: 'Ngày',
  phan_loai: 'Nhóm theo ký hiệu, số hóa đơn, thuế suất',
  mau_bao_cao: 'Mẫu tiêu chuẩn'
};
