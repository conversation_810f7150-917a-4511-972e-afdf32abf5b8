import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { Checkbox } from '@/components/ui/checkbox';

export const CCDCInformationColumns: GridColDef[] = [
  { field: 'ma_cong_cu', headerName: 'Mã công cụ', width: 150 },
  { field: 'ten_cong_cu', headerName: 'Tên công cụ', width: 300 },
  { field: 'loai_cong_cu', headerName: 'Loại công cụ', width: 100 },
  { field: 'dvt', headerName: 'Đvt', width: 100 },
  { field: 'so_luong', headerName: 'Số lượng', width: 100 },
  { field: 'ngay_tang', headerName: 'Ngày tăng', width: 100 },
  { field: 'ngay_tinh_kh', headerName: 'Ngày tính k/h', width: 100 },
  { field: 'so_ky_khau_hao', headerName: '<PERSON><PERSON> kỳ khấu hao', width: 100 },
  { field: 'ngay_ket_thuc_k/h', headerName: '<PERSON><PERSON><PERSON> kết thúc k/h', width: 100 },
  { field: 'nguyen_gia', headerName: 'Nguyên giá', width: 100 },
  { field: 'gia_tri_da_kh', headerName: 'Giá trị đã k/h', width: 100 },
  { field: 'gia_tri_con_lai', headerName: 'Giá trị còn lại', width: 100 },
  { field: 'gia_tri_kh_1_ky', headerName: 'Giá trị k/h 1 kỳ', width: 100 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 100 },
  { field: 'tk_cong_cu', headerName: 'Tk công cụ', width: 100 },
  { field: 'tk_phan_bo', headerName: 'Tk phân bổ', width: 100 },
  { field: 'tk_chi_phi', headerName: 'Tk chi phí', width: 100 },
  { field: 'ma_phi', headerName: 'Mã phí', width: 100 },
  { field: 'ma_vu_viec', headerName: 'Mã vụ việc', width: 100 }
];

export const AccountingObjectColumns: GridColDef[] = [
  { field: 'bo_phan', headerName: 'Bộ phận', width: 100 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 100 },
  { field: 'tk_phan_bo', headerName: 'Tk phân bổ', width: 100 },
  { field: 'tk_chi_phi', headerName: 'Tk chi phí', width: 100 },
  { field: 'he_so', headerName: 'Hệ số', width: 100 },
  { field: 'hieu_luc_tu', headerName: 'Hiệu lực từ', width: 100 },
  { field: 'hieu_luc_den', headerName: 'Hiệu lực đến', width: 100 }
];

export const AttachedAccessoriesColumns: GridColDef[] = [
  { field: 'ten_phu_tung', headerName: 'Tên phụ tùng', width: 300 },
  { field: 'don_vi_tinh', headerName: 'Đvt', width: 100 },
  { field: 'so_luong', headerName: 'Số lượng', width: 100 },
  { field: 'gia_tri', headerName: 'Giá trị VND', width: 100 }
];

export const ConstraintColumns: GridColDef[] = [
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 100 },
  {
    field: 'bat_buoc_nhap',
    headerName: 'Bắt buộc nhập',
    width: 120,
    renderCell: params => <Checkbox checked={params.value} />
  }
];

export const toolTypeSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Loại công cụ', flex: 1 },
  { field: 'name', headerName: 'Tên loại công cụ', flex: 1 }
];

export const sectionSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'name', headerName: 'Tên bộ phận', flex: 1 }
];

export const toolCodeSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã công cụ', flex: 1 },
  { field: 'name', headerName: 'Tên công cụ', flex: 1 }
];

export const toolGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã nhóm', flex: 1 },
  { field: 'name', headerName: 'Tên nhóm', flex: 1 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account', headerName: 'Tên tài khoản', flex: 1 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 1 },
  {
    field: 'ledger_account',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'detail_account',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'level', headerName: 'Bậc tk', flex: 1 }
];

export const companySearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    align: 'center',
    headerAlign: 'center',
    renderHeader: params => (
      <div className='flex h-full w-full items-center justify-center'>
        <Checkbox onCheckedChange={e => {}} />
      </div>
    )
  },
  { field: 'unit_id', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'name', headerName: 'Tên đơn vị', flex: 1 },
  { field: 'id', headerName: 'ID', flex: 1 }
];
