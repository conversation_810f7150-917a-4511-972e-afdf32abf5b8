'use client';

import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { accountSearchColumns, QUERY_KEYS } from '@/constants';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  taiKhoan: AccountModel | null;
  setTaiKhoan: (taiKhoan: AccountModel) => void;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode, taiKhoan, setTaiKhoan }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON><PERSON> hạch toán từ/đến</Label>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản</Label>
          <SearchField<AccountModel>
            type='text'
            name='tk'
            searchColumns={accountSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            classNameRelatedField='w-full'
            onRowSelection={setTaiKhoan}
            value={taiKhoan?.code || ''}
          />
        </div>
      </div>
    </div>
  );
};
