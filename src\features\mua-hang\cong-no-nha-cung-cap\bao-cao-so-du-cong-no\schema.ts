import { z } from 'zod';

// Zod schemas for validation
export const baoCaoSoDuCongNoItemSchema = z.object({
  uuid: z.string().optional(),
  customer_code: z.string(),
  customer_name: z.string(),
  du_no: z.number(),
  du_co: z.number()
});

export const baoCaoSoDuCongNoResponseSchema = z.object({
  count: z.number(),
  next: z.string().nullable(),
  previous: z.string().nullable(),
  results: z.array(baoCaoSoDuCongNoItemSchema)
});

export const baoCaoSoDuCongNoSearchFormValuesSchema = z
  .object({
    date: z.string().optional(),
    accountId: z.string().optional(),
    dataType: z.enum(['begin', 'end']).optional(),
    customerCode: z.string().optional(),
    customerGroup1: z.string().optional(),
    customerGroup2: z.string().optional(),
    customerGroup3: z.string().optional(),
    region: z.string().optional(),
    groupBy: z.array(z.string()).optional(),
    reportTemplate: z.enum(['quantity', 'quantityAndValue', 'quantityAndForeignValue']).optional()
  })
  .catchall(z.any());

export const searchSchema = z.object({
  // Basic fields - matching required output format
  ngay: z.string().optional(),
  ma_tk: z.string().optional(),
  loai_so_du: z.enum(['begin', 'end']).optional(),

  // Detail fields
  ma_kh: z.string().optional(),
  nh_kh1: z.string().optional(),
  nh_kh2: z.string().optional(),
  nh_kh3: z.string().optional(),
  ma_khu_vuc: z.string().optional(),

  // MultiSelect field
  nhom_theo: z.array(z.string()).optional(),

  // Report template
  mau_bao_cao: z.enum(['quantity', 'quantityAndValue', 'quantityAndForeignValue']).optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay: new Date().toISOString().split('T')[0],
  ma_tk: '',
  loai_so_du: 'begin',
  ma_kh: '',
  nh_kh1: '',
  nh_kh2: '',
  nh_kh3: '',
  ma_khu_vuc: '',
  nhom_theo: [],
  mau_bao_cao: 'quantity'
};
