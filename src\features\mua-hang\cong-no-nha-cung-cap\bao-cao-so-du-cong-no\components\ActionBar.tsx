import { <PERSON><PERSON>, Printer, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import { format } from 'date-fns';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface SearchParams {
  ngay?: string;
  ma_tk?: string;
  account?: {
    code?: string;
    name?: string;
  };
}

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  searchParams?: SearchParams;
}

// Helper function to format dates from YYYY-MM-DD to DD/MM/YYYY
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  try {
    return format(new Date(dateString), 'dd/MM/yyyy');
  } catch (error) {
    return dateString;
  }
};

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Báo cáo số dư công nợ nhà cung cấp</h1>
          {searchParams && (searchParams.ngay || searchParams.account) && (
            <div className='text-[10px] text-gray-500'>
              <p>
                <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
                {searchParams.account && <span className='font-semibold'>Tài khoản: {searchParams.account.code},</span>}
                {searchParams.ngay && <span className='font-semibold'> Ngày {formatDate(searchParams.ngay)}</span>}
              </p>
            </div>
          )}
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      {onSearchClick && <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} variant='primary' />}
      {/* No print button */}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
