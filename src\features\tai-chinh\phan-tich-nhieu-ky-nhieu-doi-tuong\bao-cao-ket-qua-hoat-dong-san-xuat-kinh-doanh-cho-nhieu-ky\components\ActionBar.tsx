import { Pin, RefreshCw, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { formatDateForDisplay } from '../utils/dateUtils';
import AritoIcon from '@/components/custom/arito/icon';
import { SearchFormValues } from '../types';

interface ActionBarProps {
  onSearchClick?: () => void;
  onPrintClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  searchParams?: SearchFormValues;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}) => {
  const formatDateRange = () => {
    // Use reportDate first, fallback to legacy tu_ngay
    const dateField = searchParams?.reportDate || searchParams?.tu_ngay;
    if (dateField) {
      const formattedDate = formatDateForDisplay(dateField);
      // Use numberOfTtDays first, fallback to legacy so_ky
      const numberOfPeriods = Number(searchParams.numberOfTtDays) || searchParams.so_ky || 12;
      // Use balance as period type, fallback to legacy loai_thoi_gian
      const periodType = searchParams.balance || searchParams.loai_thoi_gian || 'month';

      const periodTypeLabels: Record<string, string> = {
        day: 'ngày',
        week: 'tuần',
        month: 'tháng',
        quarter: 'quý',
        halfYear: 'nửa năm',
        year: 'năm'
      };

      const periodLabel = periodTypeLabels[periodType] || 'kỳ';
      return `Từ ngày ${formattedDate} - ${numberOfPeriods} ${periodLabel}`;
    }
    return `Từ ngày ${new Date().toLocaleDateString('vi-VN')}`;
  };

  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Báo cáo kết quả hoạt động sản xuất kinh doanh cho nhiều kỳ</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>{formatDateRange()}</span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}

      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
