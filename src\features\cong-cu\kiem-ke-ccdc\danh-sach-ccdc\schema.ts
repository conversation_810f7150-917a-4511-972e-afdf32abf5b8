import { z } from 'zod';

export const CCDCSchema = z.object({
  den_ngay: z.coerce.date(),
  loai: z.string(),

  // Detail tab
  ma_bp: z.string().optional(),
  mau_bc: z.string().optional(),

  // Other tab
  report_filter_template: z.string(),
  data_analysis_struct: z.string()
});
export type CCDCSchema = z.infer<typeof CCDCSchema>;

export const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

export const initialValues: CCDCSchema = {
  den_ngay: new Date(),
  loai: '*',
  ma_bp: '111',

  mau_bc: '20',
  report_filter_template: 'user_filter',
  data_analysis_struct: ''
};
