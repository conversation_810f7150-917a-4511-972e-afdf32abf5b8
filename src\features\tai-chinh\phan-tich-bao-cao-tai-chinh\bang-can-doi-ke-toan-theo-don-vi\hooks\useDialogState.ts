import { useState } from 'react';
import { SearchFormValues } from '../schema';

interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showTable: boolean;
  showEditPrintTemplateDialog: boolean;
  showSaveTemplateDialog: boolean;
  searchParams: SearchFormValues | null;

  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchFormValues) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleCloseEditPrintTemplate: () => void;
  handleSaveTemplateClick: () => void;
  handleRefreshClick: () => void;
  handleFixedColumnsClick: () => void;
  handleExportClick: () => void;
  openSaveTemplateDialog: () => void;
  closeSaveTemplateDialog: () => void;
}

const useDialogState = (
  clearSelection?: () => void,
  fetchData?: (params: SearchFormValues) => Promise<void>,
  refreshData?: () => Promise<void>
): UseDialogStateReturn => {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);
  const [showSaveTemplateDialog, setShowSaveTemplateDialog] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues | null>(null);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = async (values: SearchFormValues) => {
    if (clearSelection) clearSelection();

    setSearchParams(values);

    if (fetchData) {
      try {
        await fetchData(values);
      } catch (error) {
        console.error('❌ Error fetching data:', error);
      }
    }

    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    setShowEditPrintTemplateDialog(true);
  };

  const handleCloseEditPrintTemplate = () => {
    setShowEditPrintTemplateDialog(false);
  };

  const handleSaveTemplateClick = () => {
    setShowSaveTemplateDialog(false);
    // TODO: Implement save template
  };

  const handleRefreshClick = async () => {
    if (refreshData) {
      try {
        await refreshData();
      } catch (error) {
        console.error('❌ Error refreshing data:', error);
      }
    }
  };

  const handleFixedColumnsClick = () => {
    // TODO: Implement fixed columns
  };

  const handleExportClick = () => {
    // TODO: Implement export
  };

  const openSaveTemplateDialog = () => setShowSaveTemplateDialog(true);
  const closeSaveTemplateDialog = () => setShowSaveTemplateDialog(false);

  return {
    initialSearchDialogOpen,
    showTable,
    showEditPrintTemplateDialog,
    showSaveTemplateDialog,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleCloseEditPrintTemplate,
    handleSaveTemplateClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportClick,
    openSaveTemplateDialog,
    closeSaveTemplateDialog
  };
};

export default useDialogState;
