import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Main fields
  ngay_h_toan_tu: dateLike,
  ngay_h_toan_den: dateLike,
  duoc_thanh_toan_de: dateLike,
  tai_khoan: z.string().optional(),

  // Detail tab fields
  ma_khach_hang: z.string().optional(),
  nhom_khach_hang_1: z.string().optional(),
  nhom_khach_hang_2: z.string().optional(),
  nhom_khach_hang_3: z.string().optional(),
  khu_vuc: z.string().optional(),
  so_du: z.string().optional(),
  chi_tiet_thu_tien: z.string().optional(),
  mau_bao_cao: z.string().optional(),

  // Other tab fields
  so_c_tu_tu: z.string().optional(),
  so_c_tu_den: z.string().optional(),
  mau_loc_bao_cao: z.string().optional(),
  mau_phan_tich_dl: z.string().optional(),
  nguoi_lap: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_h_toan_tu: '2024-01-01',
  ngay_h_toan_den: '2024-01-31',
  duoc_thanh_toan_de: '2024-01-31',
  tai_khoan: '',
  ma_khach_hang: '',
  nhom_khach_hang_1: '',
  nhom_khach_hang_2: '',
  nhom_khach_hang_3: '',
  khu_vuc: '',
  so_du: '0',
  chi_tiet_thu_tien: '1',
  mau_bao_cao: 'TC',
  so_c_tu_tu: '',
  so_c_tu_den: '',
  mau_loc_bao_cao: 'user_filter',
  mau_phan_tich_dl: 'no_analysis',
  nguoi_lap: ''
};
