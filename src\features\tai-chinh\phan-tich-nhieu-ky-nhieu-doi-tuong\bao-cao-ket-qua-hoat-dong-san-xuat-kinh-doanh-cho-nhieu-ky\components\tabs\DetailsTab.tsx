import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Chọn báo cáo:</Label>
          <div className='w-1/2'>
            <FormField
              name='detail'
              label=''
              type='select'
              options={[
                { value: '0', label: 'Báo cáo kết quả sản xuất kinh doanh- Thông tư 200' },
                { value: '1', label: 'PL 03-1A/TNDN: Kết quả hoạt động sản xuất kinh doanh' }
              ]}
              defaultValue='0'
              className='w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-48'>
            <FormField
              name='reportTemplate'
              label=''
              type='select'
              options={[
                { value: 'mtc', label: 'Mẫu tiền chuẩn' },
                { value: 'mtnt', label: 'Mẫu tiền ngoại tệ' }
              ]}
              defaultValue='mtc'
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
