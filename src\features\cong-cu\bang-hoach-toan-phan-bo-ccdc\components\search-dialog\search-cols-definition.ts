import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const toolCodeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cc', headerName: 'Mã công cụ', width: 200 },
  { field: 'ten_cc', headerName: 'Tên công cụ', width: 200 }
];

export const toolTypeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_loai_cc', headerName: 'Loại công cụ', width: 200 },
  { field: 'ten_loai_cc', headerName: 'Tên loại công cụ', width: 200 }
];
export const departmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 200 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', width: 200 }
];
export const toolGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 200 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
];
