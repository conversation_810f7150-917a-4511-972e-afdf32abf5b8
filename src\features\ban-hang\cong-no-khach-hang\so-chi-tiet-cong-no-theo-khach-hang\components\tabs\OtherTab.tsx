import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const OtherTab: React.FC = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving analysis template:', data);
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='ma_unit'
              label=''
              type='select'
              options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              className='w-96'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className=''>
              <FormField
                name='data_analysis_struct'
                label=''
                type='select'
                className='w-96'
                options={[{ value: 'no_analysis', label: 'Không phân tích' }]}
              />
            </div>
            <div className='flex-1'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Người lập:</Label>
          <div className='flex-1'>
            <FormField name='nguoi_lap' label='' type='text' className='w-96' />
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
    </div>
  );
};

export default OtherTab;
