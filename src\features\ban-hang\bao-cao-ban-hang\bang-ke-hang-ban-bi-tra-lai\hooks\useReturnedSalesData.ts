import { useState, useCallback, useEffect } from 'react';
import { ReturnedSalesItem, ReturnedSalesResponse, SearchFormValues, UseReturnedSalesReturn } from '../types';
import api from '@/lib/api';

const generateMockData = (): ReturnedSalesItem[] => {
  return [
    {
      id: '1',
      unit: 'CN001',
      documentDate: '2024-01-15',
      documentNumber: 'TL2024001',
      customerCode: 'KH001',
      customerName: 'Công ty TNHH ABC',
      employeeName: 'Nguyễn Văn A',
      description: '<PERSON>àng bị lỗi kỹ thuật',
      productCode: 'SP001',
      productName: '<PERSON><PERSON>y tính xách tay Dell Inspiron',
      uom: 'Cái',
      quantity: 2,
      price: 15000000,
      returnAmount: 30000000,
      tax: 3000000,
      discount: 500000,
      totalReturn: 32500000,
      costPrice: 12000000,
      costAmount: 24000000,
      warehouseCode: 'KHO01',
      department: '<PERSON><PERSON><PERSON> bán hàng',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024001',
      paymentPhase: 'Đợt 1',
      agreement: 'KU2024001',
      fee: 650000,
      product: 'Laptop',
      productionOrder: 'LSX2024001',
      invalidExpense: 0,
      documentCode: 'TL'
    },
    {
      id: '2',
      unit: 'CN001',
      documentDate: '2024-01-16',
      documentNumber: 'TL2024002',
      customerCode: 'KH002',
      customerName: 'Công ty CP XYZ',
      employeeName: 'Trần Thị B',
      description: 'Hàng không đúng mẫu',
      productCode: 'SP002',
      productName: 'Điện thoại Samsung Galaxy S24',
      uom: 'Cái',
      quantity: 1,
      price: 25000000,
      returnAmount: 25000000,
      tax: 2500000,
      discount: 0,
      totalReturn: 27500000,
      costPrice: 20000000,
      costAmount: 20000000,
      warehouseCode: 'KHO01',
      department: 'Phòng bán hàng',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024002',
      paymentPhase: 'Đợt 1',
      agreement: 'KU2024002',
      fee: 550000,
      product: 'Smartphone',
      productionOrder: 'LSX2024002',
      invalidExpense: 0,
      documentCode: 'TL'
    },
    {
      id: '3',
      unit: 'CN002',
      documentDate: '2024-01-17',
      documentNumber: 'TL2024003',
      customerCode: 'KH003',
      customerName: 'Công ty TNHH DEF',
      employeeName: 'Lê Văn C',
      description: 'Khách hàng đổi ý',
      productCode: 'SP003',
      productName: 'Máy in Canon Pixma',
      uom: 'Cái',
      quantity: 3,
      price: 3500000,
      returnAmount: 10500000,
      tax: 1050000,
      discount: 200000,
      totalReturn: 11350000,
      costPrice: 2800000,
      costAmount: 8400000,
      warehouseCode: 'KHO02',
      department: 'Phòng bán hàng',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024003',
      paymentPhase: 'Đợt 2',
      agreement: 'KU2024003',
      fee: 227000,
      product: 'Printer',
      productionOrder: 'LSX2024003',
      invalidExpense: 0,
      documentCode: 'TL'
    },
    {
      id: '4',
      unit: 'CN001',
      documentDate: '2024-01-18',
      documentNumber: 'TL2024004',
      customerCode: 'KH004',
      customerName: 'Công ty CP GHI',
      employeeName: 'Phạm Văn D',
      description: 'Hàng bị hỏng trong vận chuyển',
      productCode: 'SP004',
      productName: 'Màn hình LG UltraWide 34 inch',
      uom: 'Cái',
      quantity: 1,
      price: 8500000,
      returnAmount: 8500000,
      tax: 850000,
      discount: 0,
      totalReturn: 9350000,
      costPrice: 7000000,
      costAmount: 7000000,
      warehouseCode: 'KHO01',
      department: 'Phòng bán hàng',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024004',
      paymentPhase: 'Đợt 1',
      agreement: 'KU2024004',
      fee: 187000,
      product: 'Monitor',
      productionOrder: 'LSX2024004',
      invalidExpense: 0,
      documentCode: 'TL'
    },
    {
      id: '5',
      unit: 'CN003',
      documentDate: '2024-01-19',
      documentNumber: 'TL2024005',
      customerCode: 'KH005',
      customerName: 'Công ty TNHH JKL',
      employeeName: 'Hoàng Thị E',
      description: 'Sản phẩm không đạt chất lượng',
      productCode: 'SP005',
      productName: 'Bàn phím cơ Logitech MX',
      uom: 'Cái',
      quantity: 5,
      price: 2200000,
      returnAmount: 11000000,
      tax: 1100000,
      discount: 300000,
      totalReturn: 11800000,
      costPrice: 1800000,
      costAmount: 9000000,
      warehouseCode: 'KHO03',
      department: 'Phòng bán hàng',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024005',
      paymentPhase: 'Đợt 1',
      agreement: 'KU2024005',
      fee: 236000,
      product: 'Keyboard',
      productionOrder: 'LSX2024005',
      invalidExpense: 0,
      documentCode: 'TL'
    },
    {
      id: '6',
      unit: 'CN002',
      documentDate: '2024-01-20',
      documentNumber: 'TL2024006',
      customerCode: 'KH006',
      customerName: 'Công ty CP MNO',
      employeeName: 'Nguyễn Văn F',
      description: 'Hàng giao sai địa chỉ',
      productCode: 'SP006',
      productName: 'Chuột không dây Logitech MX Master',
      uom: 'Cái',
      quantity: 10,
      price: 1800000,
      returnAmount: 18000000,
      tax: 1800000,
      discount: 500000,
      totalReturn: 19300000,
      costPrice: 1400000,
      costAmount: 14000000,
      warehouseCode: 'KHO02',
      department: 'Phòng bán hàng',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024006',
      paymentPhase: 'Đợt 2',
      agreement: 'KU2024006',
      fee: 386000,
      product: 'Mouse',
      productionOrder: 'LSX2024006',
      invalidExpense: 0,
      documentCode: 'TL'
    }
  ];
};

/**
 * Custom hook for managing Returned Sales Report data
 *
 * This hook provides functionality to fetch returned sales data
 * with mock support for testing and development purposes.
 */
export function useReturnedSalesData(searchParams: SearchFormValues): UseReturnedSalesReturn {
  const [data, setData] = useState<ReturnedSalesItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<ReturnedSalesResponse>('/ban-hang/bao-cao-ban-hang/bang-ke-hang-ban-bi-tra-lai/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
