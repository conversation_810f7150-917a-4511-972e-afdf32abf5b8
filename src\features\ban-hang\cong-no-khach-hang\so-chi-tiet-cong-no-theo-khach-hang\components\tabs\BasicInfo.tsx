import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountListSearchColumns } from '@/constants/search-columns';
import { Label } from '@/components/ui/label';
import { TaiKhoan } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  searchFieldStates: {
    account: TaiKhoan | null;
    setAccount: (account: <PERSON><PERSON>hoan | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON><PERSON> từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản:</Label>
          <SearchField<TaiKhoan>
            type='text'
            columnDisplay={'code'}
            displayRelatedField={'name'}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountListSearchColumns}
            value={searchFieldStates.account?.code || ''}
            relatedFieldValue={searchFieldStates.account?.name || ''}
            onRowSelection={searchFieldStates.setAccount}
          />
          <span className='text-sm'>Phải thu khách hàng tại Store</span>
        </div>
        <div className='ml-40 flex items-center'>
          <FormField name='ct_vt' type='checkbox' />
          <Label className=''>Chi tiết theo vật tư, hàng hóa, dịch vụ</Label>
        </div>
        <div className='ml-40 flex items-center'>
          <FormField name='so_du' type='checkbox' />
          <Label>Tính số dư từng ngày</Label>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
