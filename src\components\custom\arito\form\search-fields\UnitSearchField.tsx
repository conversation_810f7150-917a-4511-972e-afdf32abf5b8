import React from 'react';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface UnitSearchFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

// Define the search columns for units
const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'unit_name', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'id', headerName: 'ID', flex: 1 }
];

export const UnitSearchField: React.FC<UnitSearchFieldProps> = ({
  name = 'unit',
  label = 'Đơn vị',
  labelClassName = 'text-sm font-normal text-[13px] pr-2 sm:mb-0 text-left flex items-center min-w-[150px]',
  className = 'flex items-center',
  inputClassName = 'w-[114px]',
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <div className={className}>
      <Label className={labelClassName}>{label}</Label>
      <FormField
        name={name}
        type='text'
        className={inputClassName}
        label=''
        searchEndpoint='accounting/units'
        searchColumns={unitSearchColumns}
        defaultSearchColumn='name'
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default UnitSearchField;
