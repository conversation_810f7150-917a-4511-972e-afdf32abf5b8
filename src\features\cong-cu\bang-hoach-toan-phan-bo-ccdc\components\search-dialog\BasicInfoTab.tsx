import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { toolCodeSearchColumns } from './search-cols-definition';
import { KhaiBaoThongTinCCDC } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
}

export const BasicInfoTab = ({ formMode }: BasicInfoProps) => {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-4 md:space-y-6'>
          <div className='flex flex-col gap-4 sm:flex-row sm:gap-2'>
            <div className='flex w-full flex-col sm:w-auto sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Từ kỳ/năm</Label>
              <FormField
                type='text'
                name='tu_ky'
                disabled={formMode === 'view'}
                defaultValue={`${new Date().getMonth() + 1}`}
              />
            </div>
            <div className='flex w-full items-center sm:w-auto'>
              <FormField
                type='text'
                name='tu_nam'
                disabled={formMode === 'view'}
                defaultValue={`${new Date().getFullYear()}`}
              />
            </div>
          </div>

          <div className='flex flex-col gap-4 sm:flex-row sm:gap-2'>
            <div className='flex w-full flex-col sm:w-auto sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Đến kỳ/năm</Label>
              <FormField
                type='text'
                name='den_ky'
                disabled={formMode === 'view'}
                defaultValue={`${new Date().getMonth() + 1}`}
              />
            </div>
            <div className='flex w-full items-center sm:w-auto'>
              <FormField
                type='text'
                name='den_nam'
                disabled={formMode === 'view'}
                defaultValue={`${new Date().getFullYear()}`}
              />
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã công cụ</Label>
            <SearchField<KhaiBaoThongTinCCDC>
              type='text'
              disabled={formMode === 'view'}
              columnDisplay='ma_cc'
              displayRelatedField='ten_cc'
              searchEndpoint={`/${QUERY_KEYS.CONG_CU_DUNG_CU}`}
              searchColumns={toolCodeSearchColumns}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
