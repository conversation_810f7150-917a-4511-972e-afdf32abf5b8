import { GridColDef, GridAlignment } from '@mui/x-data-grid';
import { Checkbox } from '@mui/material';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const ValueAdjustmentColumns: GridColDef[] = [
  { field: 'ma_cong_cu', headerName: 'Mã công cụ', width: 150 },
  { field: 'ten_cong_cu', headerName: 'Tên công cụ', width: 300 },
  { field: 'ly_do', headerName: 'Lý do', width: 100 },
  { field: 'ky', headerName: 'Kỳ', width: 100 },
  { field: 'nam', headerName: 'Năm', width: 100 },
  { field: 'so_ct', headerName: 'Số c/từ', width: 100 },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 100 },
  { field: 'nguyen_gia', headerName: 'Nguyên giá', width: 100 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 }
];

export const toolTypeSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Loại công cụ', flex: 1 },
  { field: 'name', headerName: 'Tên loại công cụ', flex: 1 }
];

export const toolCodeSearchColumns = (checkbox = false): ExtendedGridColDef[] =>
  [
    checkbox && {
      field: 'checkbox',
      headerName: '',
      width: 50,
      sortable: false,
      disableColumnMenu: true,
      align: 'center' as GridAlignment,
      headerAlign: 'center' as GridAlignment,
      renderHeader: (params: any) => (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox size='small' onChange={e => {}} />
        </div>
      )
    },
    { field: 'id', headerName: 'Mã công cụ', flex: 1 },
    { field: 'name', headerName: 'Tên công cụ', flex: 1 }
  ].filter(Boolean) as ExtendedGridColDef[];

export const reasonSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã tăng giảm', flex: 1 },
  { field: 'name', headerName: 'Tên tăng giảm', flex: 1 }
];
