import { z } from 'zod';

export const searchSchema = z.object({
  // Basic Info fields
  tu_ky: z.string().optional(),
  tu_nam: z.string().optional(),
  den_ky: z.string().optional(),
  den_nam: z.string().optional(),

  // Details Tab fields
  ma_loai_ts: z.string().optional(), // Loại tài sản
  ma_bp_ts: z.string().optional(), // Bộ phận sử dụng
  nh_ts1: z.string().optional(), // Nhóm tài sản 1
  nh_ts2: z.string().optional(), // Nhóm tài sản 2
  nh_ts3: z.string().optional(), // Nhóm tài sản 3
  loai_tg_ts: z.string().optional(), // Loại (Tăng/Giảm)
  mau_bc: z.string().optional(),

  // Other Tab fields
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  // Basic Info fields
  tu_ky: '5',
  tu_nam: '2025',
  den_ky: '5',
  den_nam: '2025',

  // Details Tab fields
  ma_loai_ts: '',
  ma_bp_ts: '',
  nh_ts1: '',
  nh_ts2: '',
  nh_ts3: '',
  loai_tg_ts: 'tang',
  mau_bc: 'TC',

  // Other Tab fields
  report_filtering: 'user_filter',
  data_analysis_struct: 'no_analysis'
};
