import { useMemo } from 'react';
import type { SoDoiChieuCongNoItem, SoDoiChieuCongNoSearchFormValues } from '@/types/schemas';
import { TableData } from '@/components/custom/arito/data-tables/types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(
  data: SoDoiChieuCongNoItem[],
  searchParams?: SoDoiChieuCongNoSearchFormValues
): UseTableDataReturn {
  const tables = useMemo(() => {
    const calculateIncurredTotals = (items: SoDoiChieuCongNoItem[]) => {
      return items.reduce(
        (totals, item) => {
          return {
            ps_no: totals.ps_no + (item.ps_no || 0),
            ps_co: totals.ps_co + (item.ps_co || 0)
          };
        },
        {
          ps_no: 0,
          ps_co: 0
        }
      );
    };

    const calculateEndingBalances = (items: SoDoiChieuCongNoItem[]) => {
      return items.reduce(
        (totals, item) => {
          return {
            du_no: totals.du_no + (item.du_no || 0),
            du_co: totals.du_co + (item.du_co || 0)
          };
        },
        {
          du_no: 0,
          du_co: 0
        }
      );
    };

    const calculateDateRange = (items: SoDoiChieuCongNoItem[]) => {
      if (items.length === 0) {
        return {
          dateRange: '01/01 - 31/12',
          endDate: '31/12/2024'
        };
      }

      const dates = items
        .map(item => item.ngay_ct)
        .filter(date => date && date !== '')
        .sort();

      if (dates.length === 0) {
        return {
          dateRange: '01/01 - 31/12',
          endDate: '31/12/2024'
        };
      }

      const firstDate = dates[0];
      const lastDate = dates[dates.length - 1];

      const formatDate = (dateStr: string) => {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
          const parts = dateStr.split(/[-/]/);
          if (parts.length >= 2) {
            return `${parts[0].padStart(2, '0')}/${parts[1].padStart(2, '0')}`;
          }
          return dateStr;
        }
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${day}/${month}`;
      };

      const formattedFirstDate = formatDate(firstDate);
      const formattedLastDate = formatDate(lastDate);

      return {
        dateRange: `${formattedFirstDate} - ${formattedLastDate}`,
        endDate: lastDate
      };
    };

    const incurredTotals = calculateIncurredTotals(data);
    const endingBalances = calculateEndingBalances(data);
    const { dateRange, endDate } = calculateDateRange(data);

    const so_du = searchParams?.so_du;
    const showBalanceColumns = so_du === '1';

    console.log('Column visibility:', { so_du, showBalanceColumns });

    // Build columns array conditionally
    const baseColumns = [
      {
        field: 'ma_unit',
        headerName: 'Đơn vị',
        width: 90,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'ngay_ct',
        headerName: 'Ngày c/từ',
        width: 100,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'so_ct',
        headerName: 'Số c/từ',
        width: 140,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'tk',
        headerName: 'Tài khoản',
        width: 100,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'tk_du',
        headerName: 'Tk đối ứng',
        width: 100,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'dien_giai',
        headerName: 'Diễn giải',
        width: 280,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'ps_no',
        headerName: 'Ps nợ',
        width: 120,
        type: 'number',
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      },
      {
        field: 'ps_co',
        headerName: 'Ps có',
        width: 120,
        type: 'number',
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      }
    ];

    const balanceColumns = showBalanceColumns
      ? [
          {
            field: 'du_no',
            headerName: 'Dư nợ',
            width: 120,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'du_co',
            headerName: 'Dư có',
            width: 120,
            type: 'number',
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          }
        ]
      : [];

    const endColumns = [
      {
        field: 'ma_ct',
        headerName: 'Mã c/từ',
        width: 70,
        cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
      }
    ];

    // Build final columns array based on conditions
    const finalColumns = [...baseColumns, ...balanceColumns, ...endColumns];

    const tableData: TableData[] = [
      {
        name: '',
        columns: finalColumns,
        rows: [
          {
            id: 'total-phat-sinh',
            ma_unit: '',
            ngay_ct: dateRange,
            so_ct: '',
            tk: '',
            tk_du: '',
            dien_giai: 'Tổng phát sinh',
            ps_no: incurredTotals.ps_no,
            ps_co: incurredTotals.ps_co,
            du_no: 0, // Tổng phát sinh không hiển thị dư
            du_co: 0, // Tổng phát sinh không hiển thị dư
            ma_ct: '',
            isTotal: true // Đánh dấu đây là dòng tổng
          },
          {
            id: 'total-cuoi-ky',
            ma_unit: '',
            ngay_ct: endDate,
            so_ct: '',
            tk: '',
            tk_du: '',
            dien_giai: 'Cuối kỳ',
            ps_no: 0, // Cuối kỳ không hiển thị phát sinh
            ps_co: 0, // Cuối kỳ không hiển thị phát sinh
            du_no: endingBalances.du_no,
            du_co: endingBalances.du_co,
            ma_ct: '',
            isTotal: true // Đánh dấu đây là dòng tổng
          },
          ...data
        ]
      }
    ];

    return tableData;
  }, [data, searchParams?.so_du]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
