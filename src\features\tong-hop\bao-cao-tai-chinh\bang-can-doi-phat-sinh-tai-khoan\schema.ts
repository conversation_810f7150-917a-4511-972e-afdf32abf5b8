import { z } from 'zod';

export const searchFormSchema = z.object({
  date_from: z.union([z.date(), z.string()]).transform(val => {
    if (typeof val === 'string') {
      // Handle DD/MM/YYYY format
      const parts = val.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      return new Date(val);
    }
    return val;
  }),
  date_to: z.union([z.date(), z.string()]).transform(val => {
    if (typeof val === 'string') {
      // Handle DD/MM/YYYY format
      const parts = val.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      return new Date(val);
    }
    return val;
  }),
  open_date: z.union([z.date(), z.string()]).transform(val => {
    if (typeof val === 'string') {
      // Handle DD/MM/YYYY format
      const parts = val.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      return new Date(val);
    }
    return val;
  }),
  is_only_with_transaction: z.boolean(),
  account: z.string(),
  account_level: z.number(),
  account_type: z.string(),
  deduct_balance: z.string(),
  creator: z.string(),
  report_template: z.string()
});
export type SearchFormValues = z.infer<typeof searchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  date_from: new Date(),
  date_to: new Date(),
  open_date: new Date(),
  is_only_with_transaction: false,
  account: '',
  account_level: 0,
  account_type: '1',
  deduct_balance: '1',
  creator: '',
  report_template: '1'
};

// Trial Balance Data Types
export interface TrialBalanceItem {
  id: string;
  sysorder?: number;
  systotal?: boolean;
  sysprint?: boolean;
  syspivot?: boolean;
  stt?: number | string;
  tk: string; // tai_khoan
  no_dk: number | string; // du_no_dau
  co_dk: number | string; // du_co_dau
  ps_no: number | string; // ps_no
  ps_co: number | string; // ps_co
  no_ck: number | string; // du_no_cuoi
  co_ck: number | string; // du_co_cuoi
  bac_tk?: number;
  ten_tk: string; // ten_tai_khoan
  xten_tk?: string;
  xten_tk2?: string;
  ct_yn?: boolean;
  bold_yn?: boolean;
  isSummary?: boolean;
}

export interface TrialBalanceResponse {
  results: TrialBalanceItem[];
  count: number;
}

export interface UseTrialBalanceReturn {
  data: TrialBalanceItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
