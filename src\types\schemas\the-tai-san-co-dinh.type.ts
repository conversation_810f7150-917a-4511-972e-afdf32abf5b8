/**
 * TypeScript interfaces for TheTaiSanCoDinh (Fixed Asset Card)
 *
 * This interface represents the structure of fixed asset card data.
 * It defines asset information, depreciation details, and accounting data for fixed assets.
 */

import { ApiResponse } from '../api.type';

export interface TheTaiSanCoDinhItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Serial number for display
   */
  stt: number;

  /**
   * Asset code
   */
  ma_ts: string;

  /**
   * Asset name
   */
  ten_ts: string;

  /**
   * Increase date (date when asset was added)
   */
  ngay_tang: string;

  /**
   * Depreciation start date
   */
  ngay_kh: string;

  /**
   * Number of depreciation periods
   */
  so_ky_kh: number;

  /**
   * Asset account code
   */
  tk_ts: string;

  /**
   * Depreciation account code
   */
  tk_kh: string;

  /**
   * Expense account code
   */
  tk_cp: string;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * Original document number
   */
  so_ct: string;
}

export interface TheTaiSanCoDinhResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: TheTaiSanCoDinhItem[];
}

/**
 * Search form values for fixed asset card filtering
 */
export interface TheTaiSanCoDinhSearchFormValues {
  // Basic Info fields
  ky?: string;
  nam?: string;

  // Details Tab fields
  ma_ts?: string;
  loai_ts?: string;
  ma_bp_ts?: string;
  nh_ts1?: string;
  nh_ts2?: string;
  nh_ts3?: string;
  mau_bao_cao?: string;

  [key: string]: any;
}

/**
 * Hook return type for fixed asset card data management
 */
export interface UseTheTaiSanCoDinhReturn {
  data: TheTaiSanCoDinhItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: TheTaiSanCoDinhSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
