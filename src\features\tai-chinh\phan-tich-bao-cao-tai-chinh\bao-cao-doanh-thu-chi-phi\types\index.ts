import { SearchFormValues } from '../schemas';

export interface RevenueExpenseItem {
  id: number;
  indicator: string;
  highlight?: boolean;
  [key: string]: any; // For dynamic period fields
}

export interface RevenueExpenseResponse {
  results: RevenueExpenseItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UseRevenueExpenseReturn {
  data: RevenueExpenseItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export type { SearchFormValues };
