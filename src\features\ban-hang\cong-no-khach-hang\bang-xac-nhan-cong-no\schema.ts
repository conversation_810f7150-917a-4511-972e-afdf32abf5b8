import { z } from 'zod';

export const SearchFormSchema = z.object({
  ngay_ct1: z.date().optional(),
  ngay_ct2: z.date().optional(),
  tk: z.string().optional(),
  ngay_hd1: z.date().optional(),
  ngay_hd2: z.date().optional(),
  ma_kh: z.string().optional(),
  so_du: z.string().optional(),
  ct_yn: z.string().optional(),
  mau_bc: z.string().optional(),
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  tk: '',
  ngay_hd1: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_hd2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  ma_kh: '',
  so_du: '0',
  ct_yn: '1',
  mau_bc: 'TC',
  report_filtering: '0',
  data_analysis_struct: '0'
};
