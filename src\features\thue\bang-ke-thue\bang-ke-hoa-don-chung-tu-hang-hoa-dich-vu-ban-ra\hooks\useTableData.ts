import { GridRowParams } from '@mui/x-data-grid';
import React, { useState, useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { SearchFormValues, initialValues } from '../schema';
import { getDataTableColumns } from '../cols-definition';
import { useTaxReportData } from './useTaxReportData';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = initialValues): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  // Use the new tax report data hook
  const { data, isLoading, error, refreshData } = useTaxReportData(searchParams);

  const handleRowClick = (params: GridRowParams) => {
    const report = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
  };

  const tables = useMemo(
    () => [
      {
        name: 'Bảng kê hóa đơn, chứng từ hàng hóa, dịch vụ bán ra',
        rows: data,
        columns: getDataTableColumns()
      }
    ],
    [data]
  );

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
