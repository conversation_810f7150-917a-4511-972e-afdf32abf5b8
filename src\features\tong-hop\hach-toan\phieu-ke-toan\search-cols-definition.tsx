import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'object_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'debt', headerName: 'Công nợ p/thu', flex: 2 },
  { field: 'credit', headerName: 'Công nợ p/trả', flex: 2 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 2 },
  { field: 'email', headerName: 'Email', flex: 2 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 2 }
];

export const bookSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã quyển', flex: 1 },
  { field: 'unit_name', headerName: 'Tên quyển sổ', flex: 2 },
  { field: 'id', headerName: 'Số khai báo', flex: 2 }
];

export const currencySearchColumns: ExtendedGridColDef[] = [
  { field: 'currency_code', headerName: 'Mã ngoại tệ', flex: 1 },
  { field: 'currency_name', headerName: 'Tên ngoại tệ', flex: 2 }
];
