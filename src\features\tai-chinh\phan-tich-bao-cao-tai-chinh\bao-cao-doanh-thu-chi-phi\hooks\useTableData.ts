import { GridRowParams } from '@mui/x-data-grid';
import { useMemo, useState } from 'react';
import { generatePeriodColumns, generateColumnGroupingModel } from '../utils/dateUtils';
import { useRevenueExpenseData } from './useRevenueExpenseData';
import { SearchFormValues } from '../types';

export function useTableData(searchParams: SearchFormValues) {
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);

  // Use the revenue expense data hook
  const { data: revenueExpenseData, isLoading, error, refreshData } = useRevenueExpenseData(searchParams);

  // Generate dynamic columns based on search parameters
  const dynamicColumns = useMemo(() => {
    // Ensure period is a number
    const period = Number(searchParams.period) || 4;

    const periodColumns = generatePeriodColumns(searchParams.from_date, period, searchParams.time_type);

    return [
      {
        field: 'indicator',
        width: 300,
        sortable: false
      },
      ...periodColumns,
      {
        field: 'total_amount',
        headerName: 'Tiền',
        width: 120,
        sortable: false,
        headerAlign: 'center' as const,
        align: 'right' as const,
        type: 'number' as const
      },
      {
        field: 'total_percent',
        headerName: '%',
        width: 80,
        sortable: false,
        headerAlign: 'center' as const,
        align: 'right' as const,
        type: 'number' as const
      }
    ];
  }, [searchParams.from_date, searchParams.period, searchParams.time_type]);

  // Generate dynamic column grouping model
  const columnGroupingModel = useMemo(() => {
    const period = Number(searchParams.period) || 4;
    return generateColumnGroupingModel(searchParams.from_date, period, searchParams.time_type);
  }, [searchParams.from_date, searchParams.period, searchParams.time_type]);

  const tables = useMemo(
    () => [
      {
        name: 'Báo cáo doanh thu chi phí',
        rows: [
          {}, // Empty row for spacing
          {}, // Empty row for spacing
          ...revenueExpenseData
        ],
        columns: dynamicColumns,
        columnGroupingModel
      }
    ],
    [revenueExpenseData, dynamicColumns, columnGroupingModel]
  );

  const handleRowClick = (params: GridRowParams) => {
    const reportItem = params.row;
    setSelectedRowId(params.row.id?.toString() || null);
    console.log('Row clicked:', reportItem);
  };

  return {
    tables,
    isLoading,
    error,
    selectedRowId,
    handleRowClick,
    refreshData
  };
}
