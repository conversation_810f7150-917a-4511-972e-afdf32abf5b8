import React, { useState } from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { LoaiTSCDCCDC, BoPhanSuDungCCDC, Group } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { nhomColumns } from '@/constants';
interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
  searchFieldStates?: {
    selectedToolType: LoaiTSCDCCDC | null;
    setSelectedToolType: (value: LoaiTSCDCCDC | null) => void;
    selectedDepartment: BoPhanSuDungCCDC | null;
    setSelectedDepartment: (value: BoPhanSuDungCCDC | null) => void;
    selectedToolGroup1: Group | null;
    setSelectedToolGroup1: (value: Group | null) => void;
    selectedToolGroup2: Group | null;
    setSelectedToolGroup2: (value: Group | null) => void;
    selectedToolGroup3: Group | null;
    setSelectedToolGroup3: (value: Group | null) => void;
  };
}
const DetailsTab: React.FC<DetailTabProps> = ({ formMode, searchFieldStates }) => {
  // Use search field states from props if provided, otherwise use local state
  const [localSelectedToolType, setLocalSelectedToolType] = useState<LoaiTSCDCCDC | null>(null);
  const [localSelectedDepartment, setLocalSelectedDepartment] = useState<BoPhanSuDungCCDC | null>(null);
  const [localSelectedToolGroup1, setLocalSelectedToolGroup1] = useState<Group | null>(null);
  const [localSelectedToolGroup2, setLocalSelectedToolGroup2] = useState<Group | null>(null);
  const [localSelectedToolGroup3, setLocalSelectedToolGroup3] = useState<Group | null>(null);

  // Use props if available, otherwise use local state
  const selectedToolType = searchFieldStates?.selectedToolType ?? localSelectedToolType;
  const setSelectedToolType = searchFieldStates?.setSelectedToolType ?? setLocalSelectedToolType;
  const selectedDepartment = searchFieldStates?.selectedDepartment ?? localSelectedDepartment;
  const setSelectedDepartment = searchFieldStates?.setSelectedDepartment ?? setLocalSelectedDepartment;
  const selectedToolGroup1 = searchFieldStates?.selectedToolGroup1 ?? localSelectedToolGroup1;
  const setSelectedToolGroup1 = searchFieldStates?.setSelectedToolGroup1 ?? setLocalSelectedToolGroup1;
  const selectedToolGroup2 = searchFieldStates?.selectedToolGroup2 ?? localSelectedToolGroup2;
  const setSelectedToolGroup2 = searchFieldStates?.setSelectedToolGroup2 ?? setLocalSelectedToolGroup2;
  const selectedToolGroup3 = searchFieldStates?.selectedToolGroup3 ?? localSelectedToolGroup3;
  const setSelectedToolGroup3 = searchFieldStates?.setSelectedToolGroup3 ?? setLocalSelectedToolGroup3;

  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* 1. Loại công cụ */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại công cụ</Label>
          <div className='w-64'>
            <SearchField<LoaiTSCDCCDC>
              type='text'
              displayRelatedField='ten_lts'
              columnDisplay='ten_lts'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TSCD_CCDC}`}
              searchColumns={[
                {
                  field: 'ma_lts',
                  headerName: 'Mã loại',
                  width: 120
                },
                {
                  field: 'ten_lts',
                  headerName: 'Tên loại',
                  width: 200
                }
              ]}
              value={selectedToolType?.ten_lts || ''}
              relatedFieldValue={selectedToolType?.ten_lts || ''}
              onRowSelection={setSelectedToolType}
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        {/* 2. Bộ phận sử dụng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bộ phận sử dụng</Label>
          <div className='w-64'>
            <SearchField<BoPhanSuDungCCDC>
              type='text'
              displayRelatedField='ten_bp'
              columnDisplay='ten_bp'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
              searchColumns={[
                {
                  field: 'ma_bp',
                  headerName: 'Mã bộ phận',
                  width: 150
                },
                {
                  field: 'ten_bp',
                  headerName: 'Tên bộ phận',
                  width: 250
                }
              ]}
              value={selectedDepartment?.ten_bp || ''}
              relatedFieldValue={selectedDepartment?.ten_bp || ''}
              onRowSelection={setSelectedDepartment}
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        {/* 3. Nhóm công cụ 1 */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm công cụ 1, 2, 3</Label>
          <div className='w-64'>
            <SearchField<Group>
              type='text'
              displayRelatedField='ten_phan_nhom'
              columnDisplay='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC1`}
              searchColumns={nhomColumns}
              value={selectedToolGroup1?.ten_phan_nhom || ''}
              relatedFieldValue={selectedToolGroup1?.ten_phan_nhom || ''}
              onRowSelection={setSelectedToolGroup1}
              disabled={formMode === 'view'}
            />
          </div>
          <div className='w-64'>
            <SearchField<Group>
              type='text'
              displayRelatedField='ten_phan_nhom'
              columnDisplay='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC2`}
              searchColumns={nhomColumns}
              value={selectedToolGroup2?.ten_phan_nhom || ''}
              relatedFieldValue={selectedToolGroup2?.ten_phan_nhom || ''}
              onRowSelection={setSelectedToolGroup2}
              disabled={formMode === 'view'}
            />
          </div>
          <div className='w-64'>
            <SearchField<Group>
              type='text'
              displayRelatedField='ten_phan_nhom'
              columnDisplay='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC3`}
              searchColumns={nhomColumns}
              value={selectedToolGroup3?.ten_phan_nhom || ''}
              relatedFieldValue={selectedToolGroup3?.ten_phan_nhom || ''}
              onRowSelection={setSelectedToolGroup3}
              disabled={formMode === 'view'}
            />
          </div>
        </div>
        {/* 4. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-48'>
            <FormField
              name='mau_bc'
              label=''
              type='select'
              options={[
                { value: '0', label: 'Mẫu tiền chuẩn' },
                { value: '1', label: 'Mẫu ngoại tệ' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
