import React, { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useBankDepositData } from './useBankDepositData';
import { SearchFormValues } from '../schema';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues): UseTableDataReturn {
  const { data, isLoading, error, refreshData } = useBankDepositData(searchParams);

  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: [
        { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
        { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
        { field: 'ma_khach_hang', headerName: 'Mã khách hàng', width: 150 },
        { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
        { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
        { field: 'tk_doi_ung', headerName: 'Tk đối ứng', width: 120 },
        { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 120 },
        { field: 'ty_gia', headerName: 'Tỷ giá', width: 120 },
        { field: 'ps_no', headerName: 'Ps nợ', width: 150, type: 'number' },
        { field: 'ps_co', headerName: 'Ps có', width: 150, type: 'number' },
        { field: 'so_du', headerName: 'Số dư', width: 150, type: 'number' },
        { field: 'ma_chung_tu', headerName: 'Mã c/từ', width: 120 },
        { field: 'tai_khoan_ngan_hang', headerName: 'Tài khoản ngân hàng', width: 200 }
      ],
      rows: data
    }
  ]);

  React.useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables];
      updatedTables[0].rows = data;
      return updatedTables;
    });
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
