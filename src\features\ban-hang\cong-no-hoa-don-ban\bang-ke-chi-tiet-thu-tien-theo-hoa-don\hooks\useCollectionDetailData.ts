import { useState, useCallback, useEffect } from 'react';
import { CollectionDetailItem, CollectionDetailResponse, SearchFormValues, UseCollectionDetailReturn } from '../types';
import api from '@/lib/api';

const generateMockData = (): CollectionDetailItem[] => {
  return [
    {
      id: '1',
      unit: 'CN001',
      invoiceDate: '2024-01-15',
      isSummary: false,
      invoiceNumber: 'HD2024001',
      customerCode: 'KH001',
      customerName: 'Công ty TNHH ABC',
      description: 'Thanh toán hóa đơn bán hàng',
      totalAmount: ********,
      paidAmount: ********,
      remainingAmount: ********,
      paymentDate: '2024-01-20',
      paymentMethod: 'Chuyển khoản',
      paymentAmount: ********,
      paymentDescription: 'Thu tiền đợt 1',
      accountCode: '111',
      accountName: 'Tiền mặt',
      employeeName: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
      department: '<PERSON><PERSON>ng kế toán',
      project: 'Dự án bán hàng Q1',
      contract: 'HD2024001',
      paymentPhase: 'Đợt 1',
      agreement: 'KU2024001',
      currency: 'VND',
      exchangeRate: 1,
      foreignAmount: ********,
      documentCode: 'PT'
    },
    {
      id: '2',
      unit: 'CN001',
      invoiceDate: '2024-01-16',
      isSummary: false,
      invoiceNumber: 'HD2024002',
      customerCode: 'KH002',
      customerName: 'Công ty CP XYZ',
      description: 'Thanh toán hóa đơn dịch vụ',
      totalAmount: ********,
      paidAmount: ********,
      remainingAmount: ********,
      paymentDate: '2024-01-22',
      paymentMethod: 'Tiền mặt',
      paymentAmount: ********,
      paymentDescription: 'Thu tiền đợt 2',
      accountCode: '112',
      accountName: 'Tiền gửi ngân hàng',
      employeeName: 'Trần Thị B',
      department: 'Phòng kế toán',
      project: 'Dự án dịch vụ Q1',
      contract: 'HD2024002',
      paymentPhase: 'Đợt 2',
      agreement: 'KU2024002',
      currency: 'VND',
      exchangeRate: 1,
      foreignAmount: ********,
      documentCode: 'PT'
    },
    {
      id: '3',
      unit: 'CN002',
      invoiceDate: '2024-01-17',
      invoiceNumber: 'HD2024003',
      isSummary: false,
      customerCode: 'KH003',
      customerName: 'Công ty TNHH DEF',
      description: 'Thanh toán hóa đơn xuất khẩu',
      totalAmount: *********,
      paidAmount: ********,
      remainingAmount: ********,
      paymentDate: '2024-01-25',
      paymentMethod: 'L/C',
      paymentAmount: ********,
      paymentDescription: 'Thu tiền xuất khẩu',
      accountCode: '113',
      accountName: 'Tiền gửi ngoại tệ',
      employeeName: 'Lê Văn C',
      department: 'Phòng xuất nhập khẩu',
      project: 'Dự án xuất khẩu Q1',
      contract: 'HD2024003',
      paymentPhase: 'Đợt 3',
      agreement: 'KU2024003',
      currency: 'USD',
      exchangeRate: 24000,
      foreignAmount: 1667,
      documentCode: 'PT'
    },
    {
      id: '4',
      unit: 'CN001',
      invoiceDate: '2024-01-18',
      isSummary: false,
      invoiceNumber: 'HD2024004',
      customerCode: 'KH004',
      customerName: 'Công ty CP GHI',
      description: 'Thanh toán hóa đơn bán lẻ',
      totalAmount: ********,
      paidAmount: ********,
      remainingAmount: 0,
      paymentDate: '2024-01-18',
      paymentMethod: 'Thẻ tín dụng',
      paymentAmount: ********,
      paymentDescription: 'Thu tiền ngay',
      accountCode: '111',
      accountName: 'Tiền mặt',
      employeeName: 'Phạm Văn D',
      department: 'Phòng bán hàng',
      project: 'Dự án bán lẻ Q1',
      contract: 'HD2024004',
      paymentPhase: 'Đợt 1',
      agreement: 'KU2024004',
      currency: 'VND',
      exchangeRate: 1,
      foreignAmount: ********,
      documentCode: 'PT'
    },
    {
      id: '5',
      unit: 'CN003',
      invoiceDate: '2024-01-19',
      isSummary: false,
      invoiceNumber: 'HD2024005',
      customerCode: 'KH005',
      customerName: 'Công ty TNHH JKL',
      description: 'Thanh toán hóa đơn dự án',
      totalAmount: 1********,
      paidAmount: ********,
      remainingAmount: ********,
      paymentDate: '2024-01-30',
      paymentMethod: 'Chuyển khoản',
      paymentAmount: ********,
      paymentDescription: 'Thu tiền cuối kỳ',
      accountCode: '112',
      accountName: 'Tiền gửi ngân hàng',
      employeeName: 'Hoàng Thị E',
      department: 'Phòng dự án',
      project: 'Dự án xây dựng Q1',
      contract: 'HD2024005',
      paymentPhase: 'Đợt 4',
      agreement: 'KU2024005',
      currency: 'VND',
      exchangeRate: 1,
      foreignAmount: ********,
      documentCode: 'PT'
    },
    {
      id: '6',
      unit: 'CN002',
      invoiceDate: '2024-01-20',
      isSummary: false,
      invoiceNumber: 'HD2024006',
      customerCode: 'KH006',
      customerName: 'Công ty CP MNO',
      description: 'Thanh toán hóa đơn vận tải',
      totalAmount: ********,
      paidAmount: ********,
      remainingAmount: ********,
      paymentDate: '2024-02-01',
      paymentMethod: 'Séc',
      paymentAmount: ********,
      paymentDescription: 'Thu tiền vận tải',
      accountCode: '111',
      accountName: 'Tiền mặt',
      employeeName: 'Nguyễn Văn F',
      department: 'Phòng vận tải',
      project: 'Dự án logistics Q1',
      contract: 'HD2024006',
      paymentPhase: 'Đợt 2',
      agreement: 'KU2024006',
      currency: 'VND',
      exchangeRate: 1,
      foreignAmount: ********,
      documentCode: 'PT'
    }
  ];
};

/**
 * Custom hook for managing Collection Detail Report data
 *
 * This hook provides functionality to fetch collection detail data
 * with mock support for testing and development purposes.
 */
export function useCollectionDetailData(searchParams: SearchFormValues): UseCollectionDetailReturn {
  const [data, setData] = useState<CollectionDetailItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setData(mockData);

      // Real API call would be:
      // const response = await api.get<CollectionDetailResponse>(
      //   '/ban-hang/cong-no-hoa-don-ban/bang-ke-chi-tiet-thu-tien-theo-hoa-don/',
      //   {
      //     params: searchParams
      //   }
      // );
      // setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
