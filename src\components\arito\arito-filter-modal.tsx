import {
  Dialog,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Divider,
  useMediaQuery,
  useTheme,
  Tabs,
  Tab,
  styled
} from '@mui/material';
import React, { useRef, useEffect, useCallback } from 'react';
import CropSquareIcon from '@mui/icons-material/CropSquare';
import MinimizeIcon from '@mui/icons-material/Minimize';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import CloseIcon from '@mui/icons-material/Close';
import { z } from 'zod';
import { renderFieldsGrid, FieldConfig } from './arito-form-fields';
import AritoIcon from '@/components/custom/arito/icon';

// Add TabConfig interface
export interface TabConfig {
  key: string;
  title: string;
  fields: FieldConfig[];
  columns?: number;
}

interface AritoFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (data: any) => void;
  title: string;
  fields?: FieldConfig[];
  tabs?: TabConfig[]; // Add tabs property
  initialValues?: Record<string, any>;
  validationSchema?: z.ZodSchema<any>;
  columns?: number;
}

// Custom tab panel
const TabPanel = ({ children, value, index, ...props }: any) => {
  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      style={{ height: '100%', overflow: 'auto', padding: '0' }}
      {...props}
    >
      {value === index && <div>{children}</div>}
    </div>
  );
};

// Custom tabs container
const TabsContainer = styled('div')({
  borderBottom: '1px solid #e0e0e0',
  '& .MuiTab-root': {
    textTransform: 'none',
    minHeight: '32px',
    fontSize: '0.85rem'
  }
});

// Component for modal action buttons
interface ModalActionButtonProps {
  label: string;
  icon: number;
  onClick?: () => void;
  type?: 'button' | 'submit';
  primary?: boolean;
}

const ModalActionButton: React.FC<ModalActionButtonProps> = ({
  label,
  icon,
  onClick,
  type = 'button',
  primary = true
}) => {
  const baseStyle = {
    padding: '6px 6px',
    paddingRight: '12px',
    cursor: 'default',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px',
    fontSize: '14px',
    width: '100px',
    transition: 'all 0.2s',
    whiteSpace: 'nowrap' as const,
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  };

  const primaryStyle = {
    ...baseStyle,
    backgroundColor: '#53a3a3',
    color: 'white',
    border: 'none'
  };

  const secondaryStyle = {
    ...baseStyle,
    backgroundColor: 'white',
    color: '#53a3a3',
    border: '1px solid #53a3a3'
  };

  return (
    <button
      type={type}
      onClick={onClick}
      style={primary ? primaryStyle : secondaryStyle}
      onMouseOver={e => {
        if (primary) {
          e.currentTarget.style.backgroundColor = '#438585';
        } else {
          e.currentTarget.style.backgroundColor = '#53a3a3';
          e.currentTarget.style.color = 'white';
        }
      }}
      onMouseOut={e => {
        if (primary) {
          e.currentTarget.style.backgroundColor = '#53a3a3';
        } else {
          e.currentTarget.style.backgroundColor = 'white';
          e.currentTarget.style.color = '#53a3a3';
        }
      }}
    >
      <AritoIcon icon={icon} marginX='4px' />
      {label}
    </button>
  );
};

const AritoFilterModal: React.FC<AritoFilterModalProps> = ({
  visible,
  onClose,
  onApply,
  title,
  fields = [],
  tabs = [],
  initialValues = {},
  validationSchema,
  columns
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isFullScreen, setIsFullScreen] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState(0);

  // Store initialValues in a ref to avoid recreating the form
  const initialValuesRef = useRef(initialValues);

  // Set up react-hook-form with Zod validation
  const form = useForm({
    resolver: validationSchema ? zodResolver(validationSchema) : undefined,
    defaultValues: initialValuesRef.current,
    mode: 'onSubmit'
  });

  // Update form values when initialValues changes or visibility changes
  useEffect(() => {
    if (visible) {
      form.reset(initialValues);
    }
  }, [initialValues, visible, form]);

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleCancel = () => {
    form.reset();
    onClose();
  };

  const handleSubmit = (data: any) => {
    onApply(data);
    onClose();
  };

  // Handle tab change
  const handleTabChange = useCallback((_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  }, []);

  // Check if a tab has validation errors
  const hasTabErrors = useCallback(
    (tabFields: FieldConfig[]) => {
      const formErrors = form.formState.errors;
      if (!formErrors || Object.keys(formErrors).length === 0) return false;

      return tabFields.some(field => {
        if (field.type === 'compound' && field.inputs) {
          return field.inputs.some(input => formErrors[input.name]);
        }
        return formErrors[field.name];
      });
    },
    [form.formState.errors]
  );

  // Calculate the maximum height needed for any tab
  const getMaxTabHeight = useCallback(() => {
    if (!tabs || tabs.length === 0) return 'auto';

    // Estimate height based on number of fields (you may need to adjust this calculation)
    const maxFieldCount = Math.max(...tabs.map(tab => tab.fields.length));
    // Approximate height per field (adjust as needed)
    const heightPerField = 36;

    return `${maxFieldCount * heightPerField}px`;
  }, [tabs]);

  const actionButtons = [
    { label: 'Đồng ý', icon: 884, type: 'submit', primary: true },
    { label: 'Huỷ', icon: 885, onClick: handleCancel, primary: false }
  ];

  if (!visible) return null;

  return (
    <Dialog
      open={visible}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick') {
          handleCancel();
        }
      }}
      maxWidth={isFullScreen ? false : 'xs'}
      fullWidth={isMobile || isFullScreen}
      fullScreen={isFullScreen}
      BackdropProps={{
        style: { backgroundColor: 'transparent' }
      }}
      PaperProps={{
        style: {
          backgroundColor: '#f8fcfd',
          overflow: 'hidden',
          borderRadius: 0,
          minWidth: isMobile ? '100%' : isFullScreen ? '100%' : '850px',
          height: isFullScreen ? '100%' : 'auto'
        }
      }}
    >
      <FormProvider {...form}>
        <Box
          sx={{
            backgroundColor: '#f0f7fc',
            padding: 0,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '1px solid #e0e0e0',
            height: '36px'
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              paddingLeft: '8px'
            }}
          >
            <AritoIcon icon={12} marginX='4px' />
            <Typography color='#444' variant='body2'>
              {title}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
            <IconButton
              sx={{
                color: 'black',
                borderRadius: 0,
                height: '100%',
                paddingX: 2.5,
                '&:hover': {
                  backgroundColor: '#d9e6ed'
                }
              }}
              size='small'
              onClick={toggleFullScreen}
            >
              {isFullScreen ? (
                <MinimizeIcon fontSize='small' sx={{ fontSize: '12px' }} />
              ) : (
                <CropSquareIcon fontSize='small' sx={{ fontSize: '12px' }} />
              )}
            </IconButton>
            <IconButton
              sx={{
                color: 'black',
                paddingX: 2.5,
                borderRadius: 0,
                height: '100%',
                '&:hover': {
                  backgroundColor: '#d9e6ed'
                }
              }}
              size='small'
              onClick={handleCancel}
            >
              <CloseIcon sx={{ fontSize: '12px' }} />
            </IconButton>
          </Box>
        </Box>

        <DialogContent sx={{ padding: '0px' }}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            {/* Always render fields if they exist */}
            {fields.length > 0 && (
              <Box sx={{ padding: '10px 20px' }}>{renderFieldsGrid(fields, false, isMobile, columns)}</Box>
            )}

            {/* Render tabs with fixed height container */}
            {tabs.length > 0 && (
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <TabsContainer>
                  <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    variant='standard'
                    sx={{
                      minHeight: '32px',
                      borderTop: 'none !important',
                      '& .MuiTabs-flexContainer': {
                        borderTop: 'none !important',
                        borderBottom: '1px solid #e5e7eb'
                      },
                      '& .MuiTabs-indicator': {
                        backgroundColor: '#0f766e',
                        height: 2
                      },
                      '& .MuiTab-root': {
                        textTransform: 'none',
                        fontSize: '0.7rem',
                        fontWeight: '1000',
                        color: '#6B7280',
                        whiteSpace: 'nowrap',
                        borderTop: 'none !important',
                        transition: 'background-color 0.3s ease, color 0.3s ease',
                        '&.Mui-selected': {
                          color: '#0f766e',
                          backgroundColor: 'rgba(0, 0, 0, 0.05)'
                        },
                        '&:hover': {
                          color: '#0f766e',
                          backgroundColor: 'rgba(0, 0, 0, 0.05)'
                        }
                      }
                    }}
                  >
                    {tabs.map((tab, index) => (
                      <Tab
                        key={tab.key}
                        id={`tab-${index}`}
                        label={
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              gap: '0px'
                            }}
                          >
                            <span
                              style={{
                                display: 'inline-block',
                                verticalAlign: 'middle',
                                lineHeight: 1
                              }}
                            >
                              {tab.title}
                              {hasTabErrors(tab.fields) && (
                                <span
                                  style={{
                                    color: 'red',
                                    marginLeft: '2px'
                                  }}
                                >
                                  *
                                </span>
                              )}
                            </span>
                          </div>
                        }
                      />
                    ))}
                  </Tabs>
                </TabsContainer>

                {/* Fixed height container for tab panels */}
                <Box
                  sx={{
                    padding: '10px 20px',
                    height: getMaxTabHeight(),
                    minHeight: '150px', // Set a minimum height
                    overflow: 'auto'
                  }}
                >
                  {tabs.map((tab, index) => (
                    <TabPanel key={tab.key} value={activeTab} index={index}>
                      {renderFieldsGrid(tab.fields, false, isMobile, tab.columns)}
                    </TabPanel>
                  ))}
                </Box>
              </Box>
            )}

            <Divider />

            <Box
              sx={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '8px',
                padding: '6px'
              }}
            >
              {actionButtons.map(button => (
                <ModalActionButton
                  key={button.label}
                  label={button.label}
                  icon={button.icon}
                  type={button.type as 'button' | 'submit'}
                  onClick={button.onClick}
                  primary={button.primary}
                />
              ))}
            </Box>
          </form>
        </DialogContent>
      </FormProvider>
    </Dialog>
  );
};

export default AritoFilterModal;
