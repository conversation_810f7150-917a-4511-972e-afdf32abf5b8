import { GridColDef } from '@mui/x-data-grid';

export const IncomeStatementColumns: GridColDef[] = [
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 300, flex: 1 },
  { field: 'ma_so', headerName: 'Mã số', width: 80 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 120 },
  { field: 'tai_khoan', headerName: 'Tài khoản', width: 100 },
  {
    field: 'ky_nay',
    headerName: 'Kỳ này',
    width: 150,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  },
  {
    field: 'ky_truoc',
    headerName: 'Kỳ trước',
    width: 150,
    type: 'number',
    valueFormatter: (value: any) => {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('vi-VN').format(value);
      }
      return value;
    }
  }
];

export const printColumns: GridColDef[] = [
  { field: 'columnName', headerName: 'Tên cột', width: 150, editable: true },
  {
    field: 'englishName',
    headerName: 'Tên tiếng Anh',
    width: 150,
    editable: true
  },
  {
    field: 'width',
    headerName: 'Độ rộng',
    width: 100,
    editable: true,
    type: 'number'
  },
  {
    field: 'format',
    headerName: 'Định dạng',
    width: 120,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Text', 'Number', 'Currency', 'Date', 'Percentage']
  },
  {
    field: 'alignment',
    headerName: 'Căn chỉnh',
    width: 100,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Left', 'Center', 'Right']
  },
  {
    field: 'bold',
    headerName: 'In đậm',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'italic',
    headerName: 'Nghiêng',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'total',
    headerName: 'Tổng',
    width: 80,
    editable: true,
    type: 'boolean'
  }
];

export const availableFields = [];
