import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const accountSearchColumns: GridColDef[] = [
  {
    field: 'code',
    headerName: 'Mã tài khoản',
    width: 150,
    editable: false
  },
  {
    field: 'name',
    headerName: 'Tên tài khoản',
    width: 200,
    flex: 1,
    editable: false
  },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    width: 150,
    editable: false,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.parent_account_code_data?.code || '';
    }
  },
  {
    field: 'is_parent_account',
    headerName: 'TK sổ cái',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'TK chi tiết',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'bac_tk',
    headerName: 'Bậc TK',
    width: 100,
    editable: false,
    type: 'number'
  }
];

export const customerSearchColumns: GridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 200 },
  { field: 'CongNoPhaiThu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'CongNoPhaiTra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 }
];

export const customerGroupSearchColumns: GridColDef[] = [
  { field: 'customer_group_code', headerName: 'Mã nhóm', width: 150 },
  { field: 'customer_group_name', headerName: 'Tên nhóm', width: 250 }
];

export const regionSearchColumns: GridColDef[] = [
  { field: 'rg_code', headerName: 'Mã khu vực', width: 150 },
  { field: 'rgname', headerName: 'Tên khu vực', width: 250 }
];
