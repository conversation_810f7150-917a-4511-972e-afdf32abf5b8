import { useState, useCallback } from 'react';
import api from '@/lib/api';

// TypeScript interfaces for Invoice Debt Report
export interface InvoiceDebtItem {
  id: string;
  unit_id: string;
  ma_ngv: string;
  ngay_ct: string;
  so_ct: string;
  ma_kh: string;
  ten_kh: string;
  ma_hd: string;
  ngay_hd: string;
  so_hd: string;
  gia_tri_hd: number;
  da_thu: number;
  con_lai: number;
  qua_han: number;
  ngay_den_han: string;
  so_ngay_qua_han: number;
  ma_nt: string;
  ty_gia: number;
  gia_tri_hd_nt: number;
  da_thu_nt: number;
  con_lai_nt: number;
  qua_han_nt: number;
  dien_giai: string;
  ma_bp: string;
  ma_vv: string;
  ma_ku: string;
}

export interface InvoiceDebtResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: InvoiceDebtItem[];
}

export interface SearchFormValues {
  reportDate?: string;
  payDate?: string;
  fromDate?: string;
  toDate?: string;
  accountId?: string;
  customerCode?: string;
  customerGroup1?: string;
  customerGroup2?: string;
  customerGroup3?: string;
  region?: string;
  detail?: string;
  balance?: string;
  numberOfTtDays?: number;
  numberOfWarningDays?: number;
  [key: string]: any;
}

export interface UseInvoiceDebtReturn {
  data: InvoiceDebtItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): InvoiceDebtItem[] => {
  return [
    {
      id: '1',
      unit_id: 'CN001',
      ma_ngv: 'Hóa đơn bán hàng',
      ngay_ct: '2024-01-15',
      so_ct: 'HDBH2024001',
      ma_kh: 'KH001',
      ten_kh: 'Công ty TNHH Xây dựng Minh Phát',
      ma_hd: 'HD2024001',
      ngay_hd: '2024-01-15',
      so_hd: 'HD001',
      gia_tri_hd: **********,
      da_thu: *********,
      con_lai: *********,
      qua_han: *********,
      ngay_den_han: '2024-02-15',
      so_ngay_qua_han: 15,
      ma_nt: 'VND',
      ty_gia: 1,
      gia_tri_hd_nt: **********,
      da_thu_nt: *********,
      con_lai_nt: *********,
      qua_han_nt: *********,
      dien_giai: 'Bán máy ép thủy lực',
      ma_bp: 'Phòng kinh doanh',
      ma_vv: 'Dự án nhà máy sản xuất',
      ma_ku: 'KU2024001'
    },
    {
      id: '2',
      unit_id: 'CN001',
      ma_ngv: 'Hóa đơn bán hàng',
      ngay_ct: '2024-01-20',
      so_ct: 'HDBH2024002',
      ma_kh: 'KH002',
      ten_kh: 'Công ty CP Cơ khí Hà Nội',
      ma_hd: 'HD2024002',
      ngay_hd: '2024-01-20',
      so_hd: 'HD002',
      gia_tri_hd: 750000000,
      da_thu: 750000000,
      con_lai: 0,
      qua_han: 0,
      ngay_den_han: '2024-02-20',
      so_ngay_qua_han: 0,
      ma_nt: 'VND',
      ty_gia: 1,
      gia_tri_hd_nt: 750000000,
      da_thu_nt: 750000000,
      con_lai_nt: 0,
      qua_han_nt: 0,
      dien_giai: 'Bán thiết bị cơ khí',
      ma_bp: 'Phòng kinh doanh',
      ma_vv: 'Dự án cơ khí',
      ma_ku: 'KU2024002'
    },
    {
      id: '3',
      unit_id: 'CN002',
      ma_ngv: 'Hóa đơn bán hàng',
      ngay_ct: '2024-01-25',
      so_ct: 'HDBH2024003',
      ma_kh: 'KH003',
      ten_kh: 'Công ty TNHH Thép Việt Nam',
      ma_hd: 'HD2024003',
      ngay_hd: '2024-01-25',
      so_hd: 'HD003',
      gia_tri_hd: 1*********,
      da_thu: 400000000,
      con_lai: 800000000,
      qua_han: 800000000,
      ngay_den_han: '2024-02-25',
      so_ngay_qua_han: 30,
      ma_nt: 'VND',
      ty_gia: 1,
      gia_tri_hd_nt: 1*********,
      da_thu_nt: 400000000,
      con_lai_nt: 800000000,
      qua_han_nt: 800000000,
      dien_giai: 'Bán thép xây dựng',
      ma_bp: 'Phòng kinh doanh',
      ma_vv: 'Dự án xây dựng',
      ma_ku: 'KU2024003'
    },
    {
      id: '4',
      unit_id: 'CN001',
      ma_ngv: 'Hóa đơn bán hàng',
      ngay_ct: '2024-02-01',
      so_ct: 'HDBH2024004',
      ma_kh: 'KH004',
      ten_kh: 'Công ty CP Điện tử ABC',
      ma_hd: 'HD2024004',
      ngay_hd: '2024-02-01',
      so_hd: 'HD004',
      gia_tri_hd: 300000000,
      da_thu: 150000000,
      con_lai: 150000000,
      qua_han: 0,
      ngay_den_han: '2024-03-01',
      so_ngay_qua_han: 0,
      ma_nt: 'VND',
      ty_gia: 1,
      gia_tri_hd_nt: 300000000,
      da_thu_nt: 150000000,
      con_lai_nt: 150000000,
      qua_han_nt: 0,
      dien_giai: 'Bán linh kiện điện tử',
      ma_bp: 'Phòng kinh doanh',
      ma_vv: 'Dự án điện tử',
      ma_ku: 'KU2024004'
    },
    {
      id: '5',
      unit_id: 'CN003',
      ma_ngv: 'Hóa đơn bán hàng',
      ngay_ct: '2024-02-05',
      so_ct: 'HDBH2024005',
      ma_kh: 'KH005',
      ten_kh: 'Công ty TNHH Dệt may Hà Nội',
      ma_hd: 'HD2024005',
      ngay_hd: '2024-02-05',
      so_hd: 'HD005',
      gia_tri_hd: 450000000,
      da_thu: 0,
      con_lai: 450000000,
      qua_han: 100000000,
      ngay_den_han: '2024-03-05',
      so_ngay_qua_han: 5,
      ma_nt: 'VND',
      ty_gia: 1,
      gia_tri_hd_nt: 450000000,
      da_thu_nt: 0,
      con_lai_nt: 450000000,
      qua_han_nt: 100000000,
      dien_giai: 'Bán máy dệt',
      ma_bp: 'Phòng kinh doanh',
      ma_vv: 'Dự án dệt may',
      ma_ku: 'KU2024005'
    }
  ];
};

/**
 * Custom hook for managing Invoice Debt Report data
 *
 * This hook provides functionality to fetch invoice debt data
 * with mock support for testing and development purposes.
 */
export function useInvoiceDebtData(searchParams: SearchFormValues): UseInvoiceDebtReturn {
  const [data, setData] = useState<InvoiceDebtItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<InvoiceDebtResponse>(
        '/ban-hang/cong-no-hoa-don-ban/bang-ke-cong-no-theo-hoa-don/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
