import { useState } from 'react';

interface UseDialogStateReturn {
  showData: boolean;
  showSearchDialog: boolean;
  showEditPrintTemplateDialog: boolean;
  showSaveTemplateDialog: boolean;

  openSearchDialog: () => void;
  closeSearchDialog: () => void;
  openEditPrintTemplateDialog: () => void;
  closeEditPrintTemplateDialog: () => void;
  openSaveTemplateDialog: () => void;
  closeSaveTemplateDialog: () => void;

  handleSearchButtonClick: () => void;
  handleSearchCompleted: () => void;
  handleEditPrintTemplateButtonClick: () => void;
  handleSaveTemplateButtonClick: () => void;
  handleRefreshButtonClick: () => void;
  handleFixedColumnsButtonClick: () => void;
  handleExportButtonClick: () => void;
}

const useDialogState = (clearSelection?: () => void): UseDialogStateReturn => {
  const [showData, setShowData] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);
  const [showSaveTemplateDialog, setShowSaveTemplateDialog] = useState(false);

  const openSearchDialog = () => setShowSearchDialog(true);
  const closeSearchDialog = () => setShowSearchDialog(false);
  const openEditPrintTemplateDialog = () => setShowEditPrintTemplateDialog(true);
  const closeEditPrintTemplateDialog = () => setShowEditPrintTemplateDialog(false);
  const openSaveTemplateDialog = () => setShowSaveTemplateDialog(true);
  const closeSaveTemplateDialog = () => setShowSaveTemplateDialog(false);

  const handleSearchButtonClick = () => {
    if (clearSelection) clearSelection();
    openSearchDialog();
    // Don't set showData here - wait for search completion
  };

  const handleSearchCompleted = () => {
    closeSearchDialog();
    setShowData(true);
  };

  const handleEditPrintTemplateButtonClick = () => {
    openEditPrintTemplateDialog();
    // TODO: Implement edit print template
  };

  const handleSaveTemplateButtonClick = () => {
    closeSaveTemplateDialog();
    // TODO: Implement save template
  };

  const handleRefreshButtonClick = () => {
    // TODO: Implement refresh
  };

  const handleFixedColumnsButtonClick = () => {
    // TODO: Implement fixed columns
  };

  const handleExportButtonClick = () => {
    // TODO: Implement export
  };

  return {
    showData,
    showSearchDialog,
    showEditPrintTemplateDialog,
    showSaveTemplateDialog,

    openSearchDialog,
    closeSearchDialog,
    openEditPrintTemplateDialog,
    closeEditPrintTemplateDialog,
    openSaveTemplateDialog,
    closeSaveTemplateDialog,

    handleSearchButtonClick,
    handleSearchCompleted,
    handleEditPrintTemplateButtonClick,
    handleSaveTemplateButtonClick,
    handleRefreshButtonClick,
    handleFixedColumnsButtonClick,
    handleExportButtonClick
  };
};

export default useDialogState;
