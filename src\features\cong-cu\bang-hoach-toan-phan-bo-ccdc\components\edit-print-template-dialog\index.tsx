import { But<PERSON> } from '@mui/material';
import React from 'react';
import { EditPrintTemplateFormValues, editPrintTemPlateSchema } from '../../schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import ColumnPrintTab from './ColumnPrintTab';
import BasicInfoTab from './BasicInfoTab';

interface EditPrintTemplateDialogProps {
  open: boolean;
  onClose: () => void;
}

function EditPrintTemplateDialog({ open, onClose }: EditPrintTemplateDialogProps) {
  const handleSubmit = (data: EditPrintTemplateFormValues) => {
    onClose();
  };
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo doanh thu chi phí'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            type='submit'
            form='search-form'
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={editPrintTemPlateSchema}
        onSubmit={handleSubmit}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
          </div>
        }
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <ColumnPrintTab />
          }
        ]}
      />
    </AritoDialog>
  );
}

export default EditPrintTemplateDialog;
