import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash,
  Logs
} from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onCloseClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPinClick: () => void;
}

export const ActionBar = ({ onCloseClick, onSearchClick, onRefreshClick, onPinClick }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Đóng đơn hàng mua</h1>}>
    <AritoActionButton title='Đóng' icon={Logs} onClick={onCloseClick} variant='primary' />
    <AritoActionButton title='Tìm kiếm' icon={Binoculars} onClick={onSearchClick} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: onPinClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
