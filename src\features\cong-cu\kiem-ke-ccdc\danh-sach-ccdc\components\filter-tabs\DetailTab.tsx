'use client';

import { useState } from 'react';
import { toolTypeSearchColumns, bodyPartSearchColumns, toolGroupSearchColumns } from '../../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { LoaiTaiSanCongCu, BoPhanSuDungCCDC, Group } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
  searchFieldStates: {
    toolType: LoaiTaiSanCongCu | null;
    setToolType: (toolType: LoaiTaiSanCongCu | null) => void;
    bodyPart: BoPhanSuDungCCDC | null;
    setBodyPart: (bodyPart: BoPhanSuDungCCDC | null) => void;
    toolGroup1: Group | null;
    setToolGroup1: (group: Group | null) => void;
    toolGroup2: Group | null;
    setToolGroup2: (group: Group | null) => void;
    toolGroup3: Group | null;
    setToolGroup3: (group: Group | null) => void;
  };
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode, searchFieldStates }) => {
  return (
    <div className='p-4'>
      <div className='flex flex-col space-y-4'>
        <div className='space-y-4'>
          {/* Tool Type Search Field */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Loại công cụ</Label>
            <SearchField<LoaiTaiSanCongCu>
              type='text'
              columnDisplay='ma_lts'
              displayRelatedField='ten_lts'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TSCD_CCDC}`}
              searchColumns={toolTypeSearchColumns}
              value={searchFieldStates.toolType?.ma_lts || ''}
              relatedFieldValue={searchFieldStates.toolType?.ten_lts || ''}
              onRowSelection={searchFieldStates.setToolType}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục loại công cụ'
              className='w-full min-w-[300px]'
            />
          </div>

          {/* Body Part Search Field */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Bộ phận sử dụng</Label>
            <SearchField<BoPhanSuDungCCDC>
              type='text'
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN_SU_DUNG_CCDC}`}
              searchColumns={bodyPartSearchColumns}
              value={searchFieldStates.bodyPart?.ma_bp || ''}
              relatedFieldValue={searchFieldStates.bodyPart?.ten_bp || ''}
              onRowSelection={searchFieldStates.setBodyPart}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục bộ phận sử dụng'
              className='w-full min-w-[300px]'
            />
          </div>

          {/* Tool Group Search Fields */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Nhóm công cụ 1, 2, 3</Label>
            <SearchField<Group>
              type='text'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC1`}
              searchColumns={toolGroupSearchColumns}
              value={searchFieldStates.toolGroup1?.ma_nhom || ''}
              relatedFieldValue={searchFieldStates.toolGroup1?.ten_phan_nhom || ''}
              onRowSelection={searchFieldStates.setToolGroup1}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục nhóm công cụ 1'
              className='w-[150px]'
            />

            <SearchField<Group>
              type='text'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC2`}
              searchColumns={toolGroupSearchColumns}
              value={searchFieldStates.toolGroup2?.ma_nhom || ''}
              relatedFieldValue={searchFieldStates.toolGroup2?.ten_phan_nhom || ''}
              onRowSelection={searchFieldStates.setToolGroup2}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục nhóm công cụ 2'
              className='w-[150px]'
            />

            <SearchField<Group>
              type='text'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC3`}
              searchColumns={toolGroupSearchColumns}
              value={searchFieldStates.toolGroup3?.ma_nhom || ''}
              relatedFieldValue={searchFieldStates.toolGroup3?.ten_phan_nhom || ''}
              onRowSelection={searchFieldStates.setToolGroup3}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục nhóm công cụ 3'
              className='w-[150px]'
            />
          </div>

          {/* Report Template Field */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
            <FormField
              className='w-32 min-w-[300px]'
              type='select'
              label=''
              name='report_template'
              options={[
                { label: 'Mẫu tiền chuẩn', value: 'standard_currency' },
                { label: 'Mẫu ngoại tệ', value: 'foreign_currency' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
