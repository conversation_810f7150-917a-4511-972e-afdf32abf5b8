'use client';

import { useState } from 'react';
import Split from 'react-split';
import {
  useData,
  useFormState,
  useSearch,
  useBulkOperations,
  usePagination,
  useFormValidation,
  useFormPersistence
} from './hooks';
import { ActionBar, ConfirmDialog, FormDialog, SearchDialog, BulkActionDialog } from './components';
import { AritoSplitPane } from '@/components/custom/arito/split-pane';
import AritoDataTables from '@/components/custom/arito/data-tables';
import SidebarButton from '@/components/arito/sidebar-button';
import { warehouseGroupColumns } from './cols-definition';
import AritoIcon from '@/components/custom/arito/icon';
import { GroupType } from '@/types/schemas';
import { useGroup } from '@/hooks/queries';

export const groupTypes = [
  {
    label: 'Nhóm kho hàng 1 (KHOHANG1)',
    value: GroupType.KHO_HANG1,
    shortCode: 'KHOHANG1'
  },
  {
    label: 'Nhóm kho hàng 2 (KHOHANG2)',
    value: GroupType.KHO_HANG2,
    shortCode: 'KHOHANG2'
  },
  {
    label: 'Nhóm kho hàng 3 (KHOHANG3)',
    value: GroupType.KHO_HANG3,
    shortCode: 'KHOHANG3'
  }
];
const DEFAULT_GROUP = groupTypes[0].value;

export default function WarehouseGroupPage() {
  const {
    groups: groupsKHOHANG1,
    isLoading: isLoadingKHOHANG1,
    refreshGroups: refreshGroupsKHOHANG1,
    fetchGroupsByType: fetchGroupsByTypeKHOHANG1,
    addGroup: addGroupKHOHANG1,
    updateGroup: updateGroupKHOHANG1,
    deleteGroup: deleteGroupKHOHANG1
  } = useGroup(GroupType.KHO_HANG1);

  const {
    groups: groupsKHOHANG2,
    isLoading: isLoadingKHOHANG2,
    refreshGroups: refreshGroupsKHOHANG2,
    fetchGroupsByType: fetchGroupsByTypeKHOHANG2,
    addGroup: addGroupKHOHANG2,
    updateGroup: updateGroupKHOHANG2,
    deleteGroup: deleteGroupKHOHANG2
  } = useGroup(GroupType.KHO_HANG2);

  const {
    groups: groupsKHOHANG3,
    isLoading: isLoadingKHOHANG3,
    refreshGroups: refreshGroupsKHOHANG3,
    fetchGroupsByType: fetchGroupsByTypeKHOHANG3,
    addGroup: addGroupKHOHANG3,
    updateGroup: updateGroupKHOHANG3,
    deleteGroup: deleteGroupKHOHANG3
  } = useGroup(GroupType.KHO_HANG3);

  const getGroupFunctions = (groupType: GroupType) => {
    switch (groupType) {
      case GroupType.KHO_HANG1:
        return {
          groups: groupsKHOHANG1,
          refreshGroups: refreshGroupsKHOHANG1,
          fetchGroupsByType: fetchGroupsByTypeKHOHANG1,
          addGroup: addGroupKHOHANG1,
          updateGroup: updateGroupKHOHANG1,
          deleteGroup: deleteGroupKHOHANG1,
          isLoading: isLoadingKHOHANG1
        };
      case GroupType.KHO_HANG2:
        return {
          groups: groupsKHOHANG2,
          refreshGroups: refreshGroupsKHOHANG2,
          fetchGroupsByType: fetchGroupsByTypeKHOHANG2,
          addGroup: addGroupKHOHANG2,
          updateGroup: updateGroupKHOHANG2,
          deleteGroup: deleteGroupKHOHANG2,
          isLoading: isLoadingKHOHANG2
        };
      case GroupType.KHO_HANG3:
        return {
          groups: groupsKHOHANG3,
          refreshGroups: refreshGroupsKHOHANG3,
          fetchGroupsByType: fetchGroupsByTypeKHOHANG3,
          addGroup: addGroupKHOHANG3,
          updateGroup: updateGroupKHOHANG3,
          deleteGroup: deleteGroupKHOHANG3,
          isLoading: isLoadingKHOHANG3
        };
      default:
        return {
          groups: groupsKHOHANG1,
          refreshGroups: refreshGroupsKHOHANG1,
          fetchGroupsByType: fetchGroupsByTypeKHOHANG1,
          addGroup: addGroupKHOHANG1,
          updateGroup: updateGroupKHOHANG1,
          deleteGroup: deleteGroupKHOHANG1,
          isLoading: isLoadingKHOHANG1
        };
    }
  };

  const { activeGroup, handleFilter } = useData(DEFAULT_GROUP);

  const { isLoading, addGroup, updateGroup, deleteGroup, groups, refreshGroups } = getGroupFunctions(activeGroup);

  const { searchTerm, filterGroups, handleSearchChange } = useSearch();
  const { selectedItems, handleSelectionChange, bulkDelete, bulkUpdateStatus } = useBulkOperations(activeGroup);
  const { page, pageSize, paginationProps, getPaginatedData } = usePagination();
  const { validateForm, clearErrors } = useFormValidation();
  const { hasDraft, saveDraft, loadDraft, clearDraft } = useFormPersistence(activeGroup);

  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [showBulkDialog, setShowBulkDialog] = useState(false);

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState(activeGroup);

  const filteredGroups = searchTerm ? filterGroups(groups) : groups;

  const paginatedGroups = getPaginatedData(filteredGroups);

  const handleSearch = (term: string) => {
    handleSearchChange(term);
    setShowSearchDialog(false);
  };

  const handleAddWithDraft = () => {
    handleAddClick();

    if (hasDraft) {
      loadDraft();
    }
  };

  const handleFormSubmit = async (data: any) => {
    if (formMode === 'add') {
      const formData = {
        ...data,
        loai_nhom: activeGroup
      };

      if (formData.ma_nhom || formData.ten_phan_nhom) {
        saveDraft(formData);
      }
    }

    if (!validateForm(data)) {
      console.error('Vui lòng kiểm tra lại thông tin nhập liệu');
      return;
    }

    try {
      const formData = {
        ...data,
        loai_nhom: activeGroup
      };

      if (formMode === 'add') {
        saveDraft(formData);

        await addGroup(formData);
        await refreshGroups();

        clearDraft();
      } else if (formMode === 'edit' && selectedObj) {
        await updateGroup(selectedObj.uuid, formData);
        await refreshGroups();
      }

      clearErrors();
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleBulkAction = () => {
    if (selectedItems.length === 0) {
      console.log('Vui lòng chọn ít nhất một nhóm kho hàng');
      return;
    }
    setShowBulkDialog(true);
  };

  const handleBulkDelete = async () => {
    try {
      await bulkDelete(refreshGroups);
      setShowBulkDialog(false);
    } catch (error) {
      console.error('Error deleting groups:', error);
    }
  };

  const handleBulkUpdateStatus = async (status: string) => {
    try {
      await bulkUpdateStatus(status, groups, updateGroup, refreshGroups);
      setShowBulkDialog(false);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const tables = [
    {
      name: '',
      rows: paginatedGroups,
      columns: warehouseGroupColumns,
      key: `${activeGroup}-${Date.now()}`,
      checkboxSelection: true,
      onSelectionModelChange: handleSelectionChange,
      selectionModel: selectedItems,
      pagination: true,
      paginationModel: { page, pageSize },
      onPaginationModelChange: paginationProps,
      rowCount: filteredGroups.length
    }
  ];

  return (
    <div className='flex h-screen w-screen flex-row overflow-hidden bg-white'>
      <AritoSplitPane split='vertical' minSize={200} defaultSize={300}>
        <div className='h-full overflow-y-auto border-r'>
          <div className='p-4'>
            <h2 className='mb-4 flex items-center gap-1 text-lg font-semibold'>
              <AritoIcon icon={539} /> Danh mục phân nhóm
            </h2>
            <hr className='mb-4' />
            <ul>
              {groupTypes.map(group => (
                <li key={group.value} className='mb-2 text-sm'>
                  <SidebarButton isActive={activeGroup === group.value} onClick={() => handleFilter(group.value)}>
                    {group.label}
                  </SidebarButton>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className='h-full overflow-y-auto'>
          <div className='flex h-full flex-col'>
            <ActionBar
              onAddClick={handleAddWithDraft}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onViewClick={() => selectedObj && handleViewClick()}
              isViewDisabled={!selectedObj}
              activeGroup={activeGroup}
              onRefresh={() => refreshGroups()}
              onSearch={() => setShowSearchDialog(true)}
              onBulkAction={handleBulkAction}
              selectedItemsCount={selectedItems.length}
            />

            <div className='flex-1 overflow-hidden'>
              <Split
                className='flex flex-1 flex-col overflow-hidden'
                direction='vertical'
                sizes={[50, 50]}
                minSize={200}
                gutterSize={8}
                gutterAlign='center'
                snapOffset={30}
                dragInterval={1}
                cursor='row-resize'
              >
                <div className='w-full overflow-hidden'>
                  {isLoading && (
                    <div className='flex h-64 items-center justify-center'>
                      <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500' />
                    </div>
                  )}

                  {!isLoading && (
                    <AritoDataTables
                      key={activeGroup}
                      tables={tables}
                      onRowClick={handleRowClick}
                      selectedRowId={selectedRowIndex || undefined}
                      getRowId={row => row.uuid}
                    />
                  )}
                </div>
              </Split>
            </div>
          </div>
        </div>
      </AritoSplitPane>

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onDeleteButtonClick={handleDeleteClick}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
          initialData={
            formMode === 'add' && !isCopyMode && hasDraft
              ? loadDraft()?.formData
              : selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
                ? selectedObj
                : undefined
          }
          activeGroup={activeGroup}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          selectedObj={selectedObj}
          onClose={handleCloseDelete}
          deleteGroup={deleteGroup}
          clearSelection={clearSelection}
        />
      )}

      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={() => setShowSearchDialog(false)}
          onSearch={handleSearch}
          initialSearchTerm={searchTerm}
        />
      )}

      {showBulkDialog && (
        <BulkActionDialog
          open={showBulkDialog}
          onClose={() => setShowBulkDialog(false)}
          selectedItems={selectedItems}
          groups={groups}
          onBulkDelete={handleBulkDelete}
          onBulkUpdateStatus={handleBulkUpdateStatus}
        />
      )}
    </div>
  );
}
