'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoModal } from '@/components/custom/arito/modal';
import { AritoForm } from '@/components/custom/arito/form';
import { PermissionTab } from './components/PermissionTab';
import { BasicInfoTab } from './components/BasicInfoTab';
import AritoIcon from '@/components/custom/arito/icon';
import { ActionBar } from './components/ActionBar';
import { getUserColumns } from './cols-definition';

export default function GrantAccessByUnit({ initialRows }: { initialRows: any[] }) {
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [userRows, setUserRows] = useState<any[]>(initialRows);

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleCreate = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRefresh = () => {
    setUserRows([...userRows]);
  };

  const handlePinColumn = () => {};

  const handleSubmit = (data: any) => {
    setShowForm(false);
  };

  const handleRowClick = (params: GridRowParams) => {
    setSelectedObj(params.row);
  };

  const tables = [
    {
      name: 'Phân quyền truy cập theo đơn vị',
      rows: userRows.filter(row => row.disabled === '1'),
      columns: getUserColumns(handleOpenViewForm, handleOpenEditForm),
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] flex-col lg:overflow-hidden'>
        <ActionBar
          onCreate={handleCreate}
          onRefresh={handleRefresh}
          onPinColumns={handlePinColumn}
          isEditDisabled={!selectedObj}
        />

        <AritoDataTables tables={tables} onRowClick={handleRowClick} />
      </div>

      <AritoModal
        open={showForm}
        onClose={() => setShowForm(false)}
        title={'Mới'}
        titleIcon={<AritoIcon icon={699} />}
        maxWidth='md'
      >
        <AritoForm<any>
          mode={formMode}
          initialData={currentObj || { permissions: [] }}
          hasAritoActionBar={false}
          onSubmit={handleSubmit}
          onClose={() => setShowForm(false)}
          headerFields={<BasicInfoTab formMode={formMode} />}
          tabs={[
            {
              id: 'phan-quyen',
              label: 'Phân Quyền',
              component: <PermissionTab formMode={formMode} />
            }
          ]}
        />

        <BottomBar mode={formMode} onSubmit={() => {}} onClose={() => setShowForm(false)} />
      </AritoModal>
    </>
  );
}
