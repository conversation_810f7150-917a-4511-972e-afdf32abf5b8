import { useFormContext } from 'react-hook-form';
import { useState } from 'react';
import {
  supplierGroupSearchColumns,
  productSearchColumns,
  itemTypeSearchColumns,
  productGroupSearchColumns,
  warehouseSearchColumns,
  supplierSearchColumns
} from './cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const { setValue, getValues } = useFormContext();
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã nhà cung cấp:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
              searchColumns={supplierSearchColumns}
              dialogTitle='Danh mục nhà cung cấp'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              onValueChange={value => {
                setValue('ma_nha_cung_cap', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nha_cung_cap', row.customer_code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm NCC:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                  searchColumns={supplierGroupSearchColumns}
                  dialogTitle='Danh mục nhóm khách hàng 1'
                  columnDisplay='supplierGroupCode'
                  displayRelatedField='supplierGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                  searchColumns={supplierGroupSearchColumns}
                  dialogTitle='Danh mục nhóm khách hàng 2'
                  columnDisplay='supplierGroupCode'
                  displayRelatedField='supplierGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                  searchColumns={supplierGroupSearchColumns}
                  dialogTitle='Danh mục nhóm khách hàng 3'
                  columnDisplay='supplierGroupCode'
                  displayRelatedField='supplierGroupName'
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={productSearchColumns}
              dialogTitle='Vật tư'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              onValueChange={value => {
                setValue('ma_vat_tu', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vat_tu', row.ma_vt);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LOAI_VAT_TU}/`}
              searchColumns={itemTypeSearchColumns}
              dialogTitle='Vật tư'
              columnDisplay='loai_vat_tu'
              displayRelatedField='ten_loai'
              value={getValues('loai_vat_tu') || ''}
              onValueChange={value => {
                setValue('loai_vat_tu', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('loai_vat_tu', row.loai_vat_tu);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                  searchColumns={productGroupSearchColumns}
                  dialogTitle='Nhóm vật tư 1'
                  columnDisplay='productGroupCode'
                  displayRelatedField='productGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                  searchColumns={productGroupSearchColumns}
                  dialogTitle='Nhóm vật tư 2'
                  columnDisplay='productGroupCode'
                  displayRelatedField='productGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                  searchColumns={productGroupSearchColumns}
                  dialogTitle='Nhóm vật tư 3'
                  columnDisplay='productGroupCode'
                  displayRelatedField='productGroupName'
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã kho:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Kho hàng'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              onValueChange={value => {
                setValue('ma_kho', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_kho', row.ma_kho);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Hạn giao hàng:</Label>
          <div className='w-32'>
            <FormField name='han_giao_hang' type='date' label='' className='w-full' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Trạng thái:</Label>
          <div className='w-64'>
            <FormField
              name='trang_thai'
              label=''
              type='select'
              options={[
                { value: 'all', label: 'Tất cả' },
                { value: 'draft', label: 'Lập chứng từ' },
                { value: 'waitingForApproval', label: 'Chờ duyệt' },
                { value: 'approving', label: 'Đang duyệt' },
                { value: 'approved', label: 'Đã duyệt' },
                { value: 'processing', label: 'Đang thực hiện' },
                { value: 'completed', label: 'Hoàn thành' },
                { value: 'closed', label: 'Đóng' }
              ]}
              defaultValue='all'
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-1/2'>
            <FormField
              name='mau_bao_cao'
              label=''
              type='select'
              options={[
                { value: 'SL', label: 'Mẫu số lượng' },
                { value: 'SLGT', label: 'Mẫu số lượng và giá trị' },
                { value: 'SLGTNT', label: 'Mẫu số lượng và giá trị ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
