export interface SearchFormValues {
  // Date fields
  fromDate?: string;
  toDate?: string;
  reportDate?: string;
  payDate?: string;

  // Number fields
  numberOfTtDays?: string | number;
  numberOfWarningDays?: string | number;

  // Select fields
  balance?: string;
  detail?: string;
  reportTemplate?: string;
  reportFilterTemplate?: string;

  // Customer fields
  customerCode?: string;
  customerGroup1?: string;
  customerGroup2?: string;
  customerGroup3?: string;

  // Other fields
  accountId?: string;
  region?: string;
  fromDocumentNumber?: string;
  toDocumentNumber?: string;

  // Legacy fields for backward compatibility
  tu_ngay?: string;
  loai_thoi_gian?: 'day' | 'week' | 'month' | 'quarter' | 'halfYear' | 'year';
  so_ky?: number;
  chon_bao_cao?: '0' | '1';
  mau_bao_cao?: 'mtc' | 'mtnt';
}

export interface BusinessReportItem {
  id: string;
  ma_so: string;
  chi_tieu: string;
  thang_1?: number;
  thang_2?: number;
  thang_3?: number;
  thang_4?: number;
  thang_5?: number;
  thang_6?: number;
  thang_7?: number;
  thang_8?: number;
  thang_9?: number;
  thang_10?: number;
  thang_11?: number;
  thang_12?: number;
  tong_cong?: number;
}

export interface BusinessReportResponse {
  results: BusinessReportItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UseBusinessReportReturn {
  data: BusinessReportItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
