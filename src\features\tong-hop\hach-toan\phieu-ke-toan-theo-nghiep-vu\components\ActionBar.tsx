import { Pencil, Trash, Copy, FilePlus2, Search, Printer } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onMultiPrintClick?: () => void;
  onExportClick?: () => void;
  onExportExcelClick?: () => void;
  onImportExcelClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onFixedColumnsClick,
  onRefreshClick,
  onMultiPrintClick,
  onExportClick,
  onExportExcelClick,
  onImportExcelClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu kế toán theo nghiệp vụ</h1>}>
      {onAddClick && <AritoActionButton title='Thêm' icon={FilePlus2} onClick={onAddClick} />}
      {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
      {onDeleteClick && (
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />
      )}
      {onCopyClick && (
        <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />
      )}
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Chứng từ hạch toán',
            icon: <AritoIcon icon={20} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Chứng từ hạch toán (ngoại tệ)',
            icon: <AritoIcon icon={20} />,
            onClick: onSearchClick,
            group: 0
          },
          {
            title: 'Chứng từ hạch toán (song ngữ)',
            icon: <AritoIcon icon={20} />,
            onClick: onRefreshClick,
            group: 1
          },
          {
            title: 'Chứng từ hạch toán (ngoại tệ, song ngữ)',
            icon: <AritoIcon icon={20} />,
            onClick: onFixedColumnsClick,
            group: 1
          }
        ]}
      />

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Tìm kiếm',
            icon: <AritoIcon icon={12} />,
            onClick: onSearchClick,
            group: 0
          },
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 1
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: onFixedColumnsClick,
            group: 1
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={883} />,
            onClick: onMultiPrintClick,
            group: 2
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportClick,
            group: 2
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: onExportExcelClick,
            group: 3
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: onImportExcelClick,
            group: 3
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
