// Types for the debt balance report

export interface BangCanDoiPhatSinhCongNoItem {
  uuid?: string;
  tai_khoan: string;
  ma_khach_hang: string;
  ten_khach_hang: string;
  du_no_dau: number;
  du_co_dau: number;
  ps_no: number;
  ps_co: number;
  du_no_cuoi: number;
  du_co_cuoi: number;
}

export interface BangCanDoiPhatSinhCongNoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangCanDoiPhatSinhCongNoItem[];
}

export interface BangCanDoiPhatSinhCongNoSearchFormValues {
  date?: string;
  accountId?: string;
  dataType?: 'begin' | 'end';
  customerCode?: string;
  customerGroup1?: string;
  customerGroup2?: string;
  customerGroup3?: string;
  region?: string;
  groupBy?: string[];
  reportTemplate?: 'quantity' | 'quantityAndValue' | 'quantityAndForeignValue';
  [key: string]: any;
}

export interface UseBangCanDoiPhatSinhCongNoReturn {
  data: BangCanDoiPhatSinhCongNoItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangCanDoiPhatSinhCongNoSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
