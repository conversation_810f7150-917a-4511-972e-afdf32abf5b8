import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

// Helper function to render cells with bold formatting for summary rows
const renderCellWithBold = (params: GridRenderCellParams) => {
  const isSummaryRow = params.row.systotal === 'TOTAL';
  return <span className={isSummaryRow ? 'font-bold' : ''}>{params.value}</span>;
};

export const orderStatusReportColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  {
    field: 'unit_id',
    headerName: 'Đơn vị',
    width: 90
  },
  {
    field: 'ma_ct',
    headerName: 'Loại chứng từ',
    width: 120
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 100
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 140
  },
  {
    field: 'ma_kh',
    headerName: 'Mã nhà cung cấp',
    width: 120
  },
  {
    field: 'ten_kh',
    headerName: 'Tên nhà cung cấp',
    width: 250,
    renderCell: renderCellWithBold
  },
  {
    field: 'ngay_giao',
    headerName: 'Ngày giao hàng',
    width: 120
  },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 200
  },
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 200
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80
  },
  {
    field: 'he_so',
    headerName: 'Hệ số',
    width: 80
  },
  {
    field: 'gia',
    headerName: 'Đơn giá',
    width: 100
  },
  {
    field: 'tien',
    headerName: 'Thành tiền',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'so_luong',
    headerName: 'Sl đặt hàng',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_dh',
    headerName: 'Sl đặt đơn hàng',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_nhap',
    headerName: 'Sl đã nhập',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_hd',
    headerName: 'Sl hóa đơn',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_tl',
    headerName: 'Sl trả lại',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_cl',
    headerName: 'Sl còn lại',
    width: 100,
    renderCell: renderCellWithBold
  }
];

export const itemSearchColumns = [
  { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 120 },
  { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'nhom_1', headerName: 'Nhóm 1', width: 120 },
  { field: 'theo_doi_lo', headerName: 'Theo dõi lô', width: 100 },
  { field: 'quy_cach', headerName: 'Quy cách', width: 150 },
  { field: 'hinh_anh', headerName: 'Hình ảnh', width: 150 }
];

export const itemTypeSearchColumns = [
  { field: 'ma_loai', headerName: 'Loại vật tư', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại vật tư', width: 280 }
];

export const itemGroupSearchColumns = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
  { field: 'ten_nhom', headerName: 'Tên nhóm', width: 280 }
];

export const warehouseSearchColumns = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'don_vi', headerName: 'Đơn vị', width: 120 },
  { field: 'theo_doi_vi_tri', headerName: 'Theo dõi vị trí', width: 150 }
];

export const customerSearchColumns: GridColDef[] = [
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', width: 150 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 200 },
  { field: 'cong_no_p_thu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'cong_no_p_tra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', width: 150 }
];

export const customerGroupSearchColumn: GridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_nhom', headerName: 'Tên Nhóm', width: 200 }
];

export const customerAreaSearchColumn: GridColDef[] = [
  { field: 'ma_khu_vuc', headerName: 'Mã khu vực', width: 150 },
  { field: 'ten_khu_vuc', headerName: 'Tên khu vực', width: 200 }
];

export const transactionSearchColumns = [
  { field: 'checkbox', headerName: '', width: 60 },
  { field: 'ma_giao_dich', headerName: 'Mã giao dịch', width: 120 },
  { field: 'ten_giao_dich', headerName: 'Tên giao dịch', width: 280 },
  { field: 'ma_chung_tu', headerName: 'Mã chứng từ', width: 120 }
];

export const batchSearchColumns = [
  { field: 'ma_lo', headerName: 'Mã lô', width: 120 },
  { field: 'ten_lo', headerName: 'Tên lô', width: 280 }
];

export const locationSearchColumns = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 120 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', width: 280 }
];
