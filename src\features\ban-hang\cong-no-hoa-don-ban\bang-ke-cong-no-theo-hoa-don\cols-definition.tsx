import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  { field: 'Stt', headerName: 'Stt', width: 50 },
  { field: 'Ng<PERSON>y c/từ', headerName: '<PERSON><PERSON>y c/từ', width: 100 },
  { field: 'Số c/từ', headerName: 'Số c/từ', width: 100 },
  { field: 'Ngày hóa đơn', headerName: 'Ng<PERSON>y hóa đơn', width: 100 },
  { field: 'Số hóa đơn', headerName: 'Số hóa đơn', width: 120 },
  { field: '<PERSON>ã khách hàng', headerName: 'Mã khách hàng', width: 120 },
  { field: 'Tên khách hàng', headerName: 'Tên khách hàng', width: 200 },
  { field: 'Mã NVBH', headerName: 'Mã NVBH', width: 100 },
  { field: 'Tên <PERSON>', headerName: '<PERSON>ên <PERSON>', width: 150 },
  { field: 'Tổng tiền <PERSON>', headerName: 'Tổng tiền <PERSON>', width: 150 },
  { field: 'Đã thu', headerName: 'Đã thu', width: 120 },
  { field: 'Phải thu', headerName: 'Phải thu', width: 120 },
  { field: 'Trong hạn', headerName: 'Trong hạn', width: 120 },
  {
    field: 'Quá hạn 1 - 45 ngày',
    headerName: 'Quá hạn 1 - 45 ngày',
    width: 150
  },
  {
    field: 'Quá hạn 46 - 90 ngày',
    headerName: 'Quá hạn 46 - 90 ngày',
    width: 150
  },
  {
    field: 'Quá hạn 91 - 135 ngày',
    headerName: 'Quá hạn 91 - 135 ngày',
    width: 150
  },
  {
    field: 'Quá hạn 136 - 180 ngày',
    headerName: 'Quá hạn 136 - 180 ngày',
    width: 160
  },
  {
    field: 'Quá hạn trên 181 ngày',
    headerName: 'Quá hạn trên 181 ngày',
    width: 160
  },
  { field: 'Ngày đến hạn', headerName: 'Ngày đến hạn', width: 100 },
  { field: 'Hạn tt', headerName: 'Hạn tt', width: 100 },
  { field: 'Số ngày đến hạn', headerName: 'Số ngày đến hạn', width: 130 },
  { field: 'Diễn giải', headerName: 'Diễn giải', width: 250 },
  { field: 'Đơn vị', headerName: 'Đơn vị', width: 80 }
];
