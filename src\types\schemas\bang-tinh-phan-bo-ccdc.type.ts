/**
 * TypeScript interfaces for BangTinhPhanBoCCDC (CCDC Allocation Calculation Report)
 *
 * This interface represents the structure for tool and equipment allocation calculation data
 * used in the BangTinhPhanBoCCDC feature.
 */

import { ApiResponse } from '../api.type';

/**
 * Interface for individual CCDC allocation calculation item
 */
export interface BangTinhPhanBoCCDCItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Serial number
   */
  stt: number;

  /**
   * Tool code
   */
  ma_cc: string;

  /**
   * Tool name
   */
  ten_cc: string;

  /**
   * Depreciation start date
   */
  ngay_kh1: string;

  /**
   * Number of depreciation periods
   */
  so_ky_kh: number;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * Tool type code
   */
  ma_lcc: string;

  /**
   * Reason/Description
   */
  ly_do: string;

  /**
   * Original cost
   */
  nguyen_gia: number;

  /**
   * Accumulated depreciation
   */
  gt_da_kh: number;

  /**
   * Remaining value
   */
  gt_cl: number;

  /**
   * Depreciation amount for current period
   */
  gt_kh_ky?: number;

  /**
   * Tool account
   */
  tk_cc?: string;

  /**
   * Depreciation account
   */
  tk_kh?: string;

  /**
   * Expense account
   */
  tk_cp?: string;

  /**
   * Task code
   */
  ma_vv?: string;

  /**
   * Fee code
   */
  ma_phi?: string;

  /**
   * Unit code
   */
  ma_unit?: string;

  /**
   * Department name
   */
  ten_bp?: string;

  /**
   * Tool type name
   */
  ten_lcc?: string;

  /**
   * Quantity
   */
  so_luong?: number;

  /**
   * Unit of measurement
   */
  dvt?: string;

  /**
   * Depreciation end date
   */
  ngay_kh_kt?: string;

  /**
   * Beginning period remaining value
   */
  gt_cl_dk?: number;

  /**
   * Beginning period accumulated depreciation
   */
  gt_da_kh_dk?: number;

  /**
   * Cumulative depreciation
   */
  gt_da_kh_lk?: number;

  /**
   * End period remaining value
   */
  gt_cl_ck?: number;
}

/**
 * Interface for API response
 */
export interface BangTinhPhanBoCCDCResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangTinhPhanBoCCDCItem[];
}

/**
 * Interface for search form values
 */
export interface BangTinhPhanBoCCDCSearchFormValues {
  /**
   * From period
   */
  tu_ky?: string;

  /**
   * From year
   */
  tu_nam?: string;

  /**
   * To period
   */
  den_ky?: string;

  /**
   * To year
   */
  den_nam?: string;

  /**
   * Processing type
   */
  xu_ly?: string;

  /**
   * Detail level
   */
  detailBy?: string;

  /**
   * Report template
   */
  mau_bc?: string;

  /**
   * Data analysis structure
   */
  data_analysis_struct?: string;

  /**
   * Unit code
   */
  ma_unit?: string;

  /**
   * Creator
   */
  nguoi_lap?: string;

  /**
   * Tool code filter
   */
  ma_cc?: string;

  /**
   * Tool type filter
   */
  ma_lcc?: string;

  /**
   * Department filter
   */
  ma_bp?: string;

  /**
   * Tool group 1
   */
  nh_cc1?: string;

  /**
   * Tool group 2
   */
  nh_cc2?: string;

  /**
   * Tool group 3
   */
  nh_cc3?: string;

  [key: string]: any;
}

/**
 * Interface for hook return type
 */
export interface UseBangTinhPhanBoCCDCReturn {
  data: BangTinhPhanBoCCDCItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangTinhPhanBoCCDCSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * Type for API response
 */
export type BangTinhPhanBoCCDCApiResponse = ApiResponse<BangTinhPhanBoCCDCItem>;
