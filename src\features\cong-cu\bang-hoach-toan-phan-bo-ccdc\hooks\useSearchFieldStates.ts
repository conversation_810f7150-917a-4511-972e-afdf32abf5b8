import { useState } from 'react';
import type { LoaiTaiSanCongCu, BoPhanSuDungCCDC, Group } from '@/types/schemas';

/**
 * Hook for managing search field states in the bang-hoach-toan-phan-bo-ccdc feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // Tool type state
  const [toolType, setToolType] = useState<LoaiTaiSanCongCu | null>(null);

  // Department state
  const [department, setDepartment] = useState<BoPhanSuDungCCDC | null>(null);

  // Tool group states
  const [toolGroup1, setToolGroup1] = useState<Group | null>(null);
  const [toolGroup2, setToolGroup2] = useState<Group | null>(null);
  const [toolGroup3, setToolGroup3] = useState<Group | null>(null);

  return {
    // Tool type
    toolType,
    setToolType,

    // Department
    department,
    setDepartment,

    // Tool groups
    toolGroup1,
    setToolGroup1,
    toolGroup2,
    setToolGroup2,
    toolGroup3,
    setToolGroup3
  };
};
