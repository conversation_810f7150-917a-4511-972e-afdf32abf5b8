import { useFormContext } from 'react-hook-form';
import React from 'react';
import {
  boPhanSearchColumns,
  hopDongSearchColumns,
  invalidCostSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  sanPhamSearchColumns,
  vuViecSearchColumns
} from './cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { exportDotThanhToanColumns } from '../../cols-definition';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const FilterTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Bộ phận:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              searchColumns={boPhanSearchColumns}
              dialogTitle='Bộ phận'
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              onValueChange={value => {
                setValue('bo_phan', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('bo_phan', row.ma_bp);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Vụ việc:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
              searchColumns={vuViecSearchColumns}
              dialogTitle='Vụ việc'
              columnDisplay='ma_vv'
              displayRelatedField='ten_vv'
              onValueChange={value => {
                setValue('vu_viec', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('vu_viec', row.ma_vv);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Hợp đồng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
              searchColumns={hopDongSearchColumns}
              dialogTitle='Hợp đồng'
              columnDisplay='so_hop_dong'
              displayRelatedField='ten_hop_dong'
              onValueChange={value => {
                setValue('hop_dong', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('hop_dong', row.so_hop_dong);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Đợt thanh toán:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
              searchColumns={exportDotThanhToanColumns}
              dialogTitle='Đợt thanh toán'
              columnDisplay='ma_dot'
              displayRelatedField='ten_dot_thanh_toan'
              onValueChange={value => {
                setValue('dot_thanh_toan', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('dot_thanh_toan', row.ma_dot);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Khế ước:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
              searchColumns={kheUocSearchColumns}
              dialogTitle='Khe ước'
              columnDisplay='ma_ku'
              displayRelatedField='ten_ku'
              onValueChange={value => {
                setValue('khe_uoc', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('khe_uoc', row.ma_ku);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Phí:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.PHI}/`}
              searchColumns={phiSearchColumns}
              dialogTitle='Phí'
              columnDisplay='ma_phi'
              displayRelatedField='ten_phi'
              onValueChange={value => {
                setValue('phi', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('phi', row.ma_phi);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Sản phẩm:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={sanPhamSearchColumns}
              dialogTitle='Sản phẩm'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              onValueChange={value => {
                setValue('san_pham', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('san_pham', row.ma_vt);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        {/* Lệnh sản xuất field */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Lệnh sản xuất:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
              searchColumns={lenhSanXuatSearchColumns}
              dialogTitle='Lệnh sản xuất'
              columnDisplay='ma_lenh'
              displayRelatedField='ten_lenh_san_xuat'
              onValueChange={value => {
                setValue('lenh_san_xuat', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('lenh_san_xuat', row.ma_lenh);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Chi phí không hợp lệ field */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>C/p không h/lệ:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
              searchColumns={invalidCostSearchColumns}
              dialogTitle='Chi phí không hợp lệ'
              columnDisplay='ma_chi_phi'
              displayRelatedField='ten_chi_phi'
              onValueChange={value => {
                setValue('chi_phi_khong_hop_le', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('chi_phi_khong_hop_le', row.ma_chi_phi);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterTab;
