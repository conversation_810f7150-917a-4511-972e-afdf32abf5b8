import AritoConfirmModal from '../arito-confirm-modal';
import AritoFilterModal from '../arito-filter-modal';
// import { EntityConfig } from '../arito-crud-page';

// Temporary EntityConfig type definition
type EntityConfig = any;
type CrudMode = 'create' | 'read' | 'update';

interface AritoCrudModalsProps {
  config: EntityConfig;
  isPopupVisible: boolean;
  isConfirmModalVisible: boolean;
  showFilterModal: boolean;
  popupMode: CrudMode;
  selectedItem: any;
  onClosePopup: () => void;
  onSave: (values: any) => void;
  onDelete: (item: any) => void;
  onCloseConfirmModal: () => void;
  onApplyFilter: (criteria: any) => void;
  onCloseFilterModal: () => void;
  containerRef: React.RefObject<HTMLDivElement>;
}

export function AritoCrudModals({
  config,
  isPopupVisible,
  isConfirmModalVisible,
  showFilterModal,
  popupMode,
  selectedItem,
  onClosePopup,
  onSave,
  onDelete,
  onCloseConfirmModal,
  onApplyFilter,
  onCloseFilterModal,
  containerRef
}: AritoCrudModalsProps) {
  // Get the initial values based on mode
  const initialValues =
    popupMode === 'create' ? (config.popupConfig?.initialValues ?? config.modalConfig?.initialValues) : selectedItem;

  return (
    <>
      {/* Filter Modal */}
      {config.filterModalConfig && (
        <AritoFilterModal
          visible={showFilterModal}
          onClose={onCloseFilterModal}
          onApply={onApplyFilter}
          title={config.filterModalConfig.title}
          fields={config.filterModalConfig.fields}
          tabs={config.filterModalConfig.tabs}
          initialValues={config.filterModalConfig.initialValues}
          validationSchema={config.filterModalConfig.validationSchema}
          columns={config.filterModalConfig.columns}
        />
      )}

      {/* Confirm Delete Modal */}
      <AritoConfirmModal
        open={isConfirmModalVisible}
        onClose={onCloseConfirmModal}
        onConfirm={() => selectedItem && onDelete(selectedItem)}
        title='Xóa dữ liệu'
        message='Bạn có chắc chắn xóa không?'
      />
    </>
  );
}
