// TypeScript interfaces for Return Voucher Report

export interface ReturnVoucherItem {
  // System fields
  syspivot?: string;
  systotal?: string;
  sysprint?: string;
  sysorder?: number;
  stt?: number;
  id: string;
  unit_id?: string;

  // Document fields (mapped to existing columns)
  ma_ct?: string; // Maps to so_ct
  ngay_ct?: string; // Maps to ngay_ctu
  so_ct: string; // Existing field

  // Entity codes
  ma_kh: string; // Existing field
  ma_vt: string; // Existing field
  ma_kho: string; // Existing field
  ma_bp?: string; // Maps to bo_phan
  ma_vv?: string; // Maps to vu_viec
  ma_hd?: string; // Maps to hop_dong
  ma_ku?: string; // Maps to khe_uoc
  ma_phi?: string; // Maps to phi
  ma_sp?: string; // Maps to sanh_pham
  ma_lsx?: string; // Maps to lenh_san_xuat
  ma_dtt?: string; // Maps to dot_thanh_toan
  ma_cp0?: string; // Maps to c/p_khong_hop_le
  ma_unit?: string; // Maps to don_vi

  // Transaction fields
  sl_xuat?: number; // Maps to so_luong
  gia?: number; // Maps to don_gia
  tien_xuat?: number; // Maps to thanh_tien
  thue: number; // Existing field
  dien_giai: string; // Existing field
  tk_du?: string; // Maps to tk_no

  // Display names
  ten_kh: string; // Existing field
  ten_vt: string; // Existing field
  dvt: string; // Existing field

  // Existing mapped fields (keep for backward compatibility)
  don_vi?: string;
  ngay_ctu?: string;
  so_luong?: number;
  don_gia?: number;
  thanh_tien?: number;
  tk_no?: string;
  bo_phan?: string;
  vu_viec?: string;
  hop_dong?: string;
  dot_thanh_toan?: string;
  khe_uoc?: string;
  phi?: number;
  sanh_pham?: string;
  lenh_san_xuat?: string;
  'c/p_khong_hop_le'?: string;
  ma_ctu?: string;

  // Summary row indicator
  isSummary?: boolean;
}

export interface ReturnVoucherResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ReturnVoucherItem[];
}

export interface SearchFormValues {
  ngay_tu?: string;
  ngay_den?: string;
  so_chung_tu_tu?: string;
  so_chung_tu_den?: string;
  ma_nha_cung_cap?: string;
  nhom_nha_cung_cap?: string[];
  ma_vat_tu?: string;
  loai_vat_tu?: string;
  nhom_vat_tu?: string[];
  ma_kho?: string;
  tai_khoan_co?: string;
  tai_khoan_no?: string;
  ma_khach_hang?: string;
  ngoai_te?: string;
  dien_giai?: string;
  trang_thai?: string;
  mau_bao_cao?: string;
  ly_do_tra?: string;
  ma_giao_dich?: string;
  ma_lo?: string;
  ma_vi_tri?: string;
  bo_phan?: string;
  vu_viec?: string;
  hop_dong?: string;
  dot_thanh_toan?: string;
  khe_uoc?: string;
  phi?: string;
  san_pham?: string;
  mau_loc_bao_cao?: string;
  [key: string]: any;
}

export interface UseReturnVoucherReturn {
  data: ReturnVoucherItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
