import { <PERSON><PERSON><PERSON><PERSON>, Lock, Printer, RefreshCw, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/arito/arito-action-button';
import { AritoMenuButton } from '@/components/arito/arito-menu-button';
import AritoActionBar from '@/components/arito/arito-action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  date?: Date | null;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
export const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportClick,
  onEditPrintTemplateClick,
  date
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Danh sách CCDC</h1>
        <div className='text-[10px] text-gray-500'>
          <p>
            <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
            Đến ngày <span className='font-semibold'>{date?.toLocaleDateString()}</span>
          </p>
        </div>
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}
    {onPrintClick && (
      <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} disabled={isViewDisabled} />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Lock} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onExportClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
