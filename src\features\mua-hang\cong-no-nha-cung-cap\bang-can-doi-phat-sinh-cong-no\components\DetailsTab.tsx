import { customerSearchColumns, groupColumns, regionColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { KhachHang, Group, KhuVuc } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailsTabProps {
  searchFieldStates: {
    customer: KhachHang | null;
    setCustomer: (customer: KhachHang | null) => void;
    customerGroup1: Group | null;
    setCustomerGroup1: (group: Group | null) => void;
    customerGroup2: Group | null;
    setCustomerGroup2: (group: Group | null) => void;
    customerGroup3: Group | null;
    setCustomerGroup3: (group: Group | null) => void;
    region: KhuVuc | null;
    setRegion: (region: KhuVuc | null) => void;
  };
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  return (
    <div className='min-w-[900px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* 1. Mã nhà cung cấp */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhà cung cấp:</Label>
          <SearchField<KhachHang>
            dialogTitle='Danh mục đối tượng'
            columnDisplay={'customer_code'}
            displayRelatedField={'customer_name'}
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
            searchColumns={customerSearchColumns}
            value={searchFieldStates.customer?.customer_code || ''}
            relatedFieldValue={searchFieldStates.customer?.customer_name || ''}
            onRowSelection={searchFieldStates.setCustomer}
          />
        </div>

        {/* 2. Nhóm NCC */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm NCC:</Label>
          <SearchField<Group>
            dialogTitle='Danh mục nhóm khách hàng 1'
            columnDisplay={'ma_nhom'}
            displayRelatedField={'ten2'}
            searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}?loai_nhom=KH1`}
            searchColumns={groupColumns}
            value={searchFieldStates.customerGroup1?.ma_nhom || ''}
            relatedFieldValue={searchFieldStates.customerGroup1?.ten2 || ''}
            onRowSelection={searchFieldStates.setCustomerGroup1}
          />
          <SearchField<Group>
            dialogTitle='Danh mục nhóm khách hàng 2'
            columnDisplay={'ma_nhom'}
            displayRelatedField={'ten2'}
            searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}?loai_nhom=KH2`}
            searchColumns={groupColumns}
            value={searchFieldStates.customerGroup2?.ma_nhom || ''}
            relatedFieldValue={searchFieldStates.customerGroup2?.ten2 || ''}
            onRowSelection={searchFieldStates.setCustomerGroup2}
          />
          <SearchField<Group>
            dialogTitle='Danh mục nhóm khách hàng 3'
            columnDisplay={'ma_nhom'}
            displayRelatedField={'ten2'}
            searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}?loai_nhom=KH3`}
            searchColumns={groupColumns}
            value={searchFieldStates.customerGroup3?.ma_nhom || ''}
            relatedFieldValue={searchFieldStates.customerGroup3?.ten2 || ''}
            onRowSelection={searchFieldStates.setCustomerGroup3}
          />
        </div>

        {/* 3. Khu vực */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực:</Label>
          <SearchField<KhuVuc>
            type='text'
            columnDisplay={'rg_code'}
            dialogTitle='Danh mục khu vực'
            displayRelatedField={'rgname'}
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
            searchColumns={regionColumns}
            value={searchFieldStates.region?.rg_code || ''}
            relatedFieldValue={searchFieldStates.region?.rgname || ''}
            onRowSelection={searchFieldStates.setRegion}
          />
        </div>

        {/* Nhóm theo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm theo:</Label>
          <div className='w-[500px]'>
            <FormField
              name='nhom_theo'
              type='multiselect'
              placeholder='(Chọn nhiều, nhóm báo cáo theo thứ tự được chọn)'
              options={[
                { value: '1', label: 'Nhóm khách 1' },
                { value: '2', label: 'Nhóm khách 2' },
                { value: '3', label: 'Nhóm khách 3' }
              ]}
            />
          </div>
        </div>
        {/* 9. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <FormField
            name='mau_bao_cao'
            type='select'
            options={[
              { value: 'TC', label: 'Mẫu tiền chuẩn' },
              {
                value: 'NT',
                label: 'Mẫu ngoại tệ'
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
