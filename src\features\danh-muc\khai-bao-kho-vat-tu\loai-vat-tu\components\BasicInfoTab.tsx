import { FormField } from '@/components/custom/arito/form/form-field';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      <div className='col-span-5 space-y-2'>
        <FormField
          label='Mã loại vật tư'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ma_loai_vat_tu'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên loại vật tư'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ten_loai_vat_tu'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên khác'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ten_khac'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center'
          name='trang_thai'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 1, label: '1. Còn sử dụng' },
            { value: 0, label: '0. Không sử dụng' }
          ]}
        />
      </div>
    </div>
  </div>
);
