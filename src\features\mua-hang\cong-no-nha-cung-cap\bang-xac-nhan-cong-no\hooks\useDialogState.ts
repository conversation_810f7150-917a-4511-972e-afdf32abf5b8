import { useState } from 'react';

interface UseDialogStateReturn {
  showData: boolean;
  showSearchDialog: boolean;
  showEditPrintTemplateDialog: boolean;

  openSearchDialog: () => void;
  closeSearchDialog: () => void;
  openEditPrintTemplateDialog: () => void;
  closeEditPrintTemplateDialog: () => void;

  handleSearchButtonClick: () => void;
  handleEditPrintTemplateButtonClick: () => void;
}

export const useDialogState = (clearSelection?: () => void): UseDialogStateReturn => {
  const [showData, setShowData] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);

  const openSearchDialog = () => setShowSearchDialog(true);
  const closeSearchDialog = () => setShowSearchDialog(false);
  const openEditPrintTemplateDialog = () => setShowEditPrintTemplateDialog(true);
  const closeEditPrintTemplateDialog = () => setShowEditPrintTemplateDialog(false);

  const handleSearchButtonClick = () => {
    if (clearSelection) clearSelection();
    closeSearchDialog();
    setShowData(true);
    // TODO: Implement search
  };

  const handleEditPrintTemplateButtonClick = () => {
    openEditPrintTemplateDialog();
    // TODO: Implement edit print template
  };

  return {
    showData,
    showSearchDialog,
    showEditPrintTemplateDialog,

    openSearchDialog,
    closeSearchDialog,
    openEditPrintTemplateDialog,
    closeEditPrintTemplateDialog,

    handleSearchButtonClick,
    handleEditPrintTemplateButtonClick
  };
};
