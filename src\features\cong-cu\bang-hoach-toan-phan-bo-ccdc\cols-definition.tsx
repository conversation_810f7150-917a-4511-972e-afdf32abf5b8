import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'stt',
    headerName: 'Stt',
    width: 100
  },
  {
    field: 'ma_cc',
    headerName: 'Mã công cụ',
    width: 160
  },
  {
    field: 'ten_cc',
    headerName: 'Tên công cụ',
    width: 160
  },
  {
    field: 'tk_kh',
    headerName: 'Tài khoản phân bổ',
    width: 160
  },
  {
    field: 'tk_cp',
    headerName: 'Tài khoản chi phí',
    width: 160
  },
  {
    field: 'tien',
    headerName: 'Tiền',
    width: 160,
    type: 'number',
    renderCell: (params: any) => {
      return new Intl.NumberFormat('vi-VN').format(params.row.tien || 0);
    }
  },
  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 160
  },
  {
    field: 'ma_vv',
    headerName: '<PERSON><PERSON> vụ việc',
    width: 160
  },
  {
    field: 'ma_phi',
    headerName: 'Mã phí',
    width: 160
  }
];

// Total row that will be added to the data
export const getTotalRow = (_rows: any[]) => {
  return {
    id: 'total',
    stt: '',
    ma_cc: '',
    ten_cc: 'Tổng cộng',
    tk_kh: '',
    tk_cp: '',
    tien: 0, // Will be overridden with calculated total
    ma_bp: '',
    ma_vv: '',
    ma_phi: ''
  };
};

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'column',
    headerName: 'Cột',
    width: 180
  },
  {
    field: 'columnName',
    headerName: 'Tên cột',
    width: 200
  },
  {
    field: 'columnNameEnglish',
    headerName: 'Tên tiếng anh',
    width: 200
  },
  {
    field: 'width',
    headerName: 'Độ rộng',
    width: 200
  },
  {
    field: 'format',
    headerName: 'Định dạng',
    width: 200
  },
  {
    field: 'align',
    headerName: 'Căn chỉnh',
    width: 200
  },
  {
    field: 'bold',
    headerName: 'In đậm',
    width: 200
  },
  {
    field: 'italic',
    headerName: 'Nghiêng',
    width: 200
  },
  {
    field: 'total',
    headerName: 'Tổng',
    width: 200
  }
];
