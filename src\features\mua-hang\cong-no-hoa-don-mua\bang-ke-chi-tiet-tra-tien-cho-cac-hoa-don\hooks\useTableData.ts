import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BangKeChiTietTraTienChoHoaDonItem } from '@/types/schemas';
import { detailPaymentsColumns } from '../cols-definition';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BangKeChiTietTraTienChoHoaDonItem[], searchParams?: any): UseTableDataReturn {
  const tables = useMemo(() => {
    // Calculate totals for financial columns
    const calculateTotals = (items: BangKeChiTietTraTienChoHoaDonItem[]) => {
      return items.reduce(
        (totals, item) => ({
          t_tt: totals.t_tt + (item.t_tt || 0),
          tt: totals.tt + (item.tt || 0),
          cl: totals.cl + (item.cl || 0)
        }),
        {
          t_tt: 0,
          tt: 0,
          cl: 0
        }
      );
    };

    const totals = calculateTotals(data);

    const tableData: TableData[] = [
      {
        name: '',
        columns: detailPaymentsColumns,
        rows: [
          {
            id: 'total',
            stt: 0,
            ngay_ct: '',
            so_ct: '',
            ngay_ct0: '',
            so_ct0: '',
            ma_kh: '',
            ten_kh: 'Tổng cộng',
            dien_giai: '',
            t_tt: totals.t_tt,
            tt: totals.tt,
            cl: totals.cl,
            tt_yn: '',
            ma_ct: '',
            ma_unit: '',
            id_tt: '',
            id_ct: '',
            loai_tt: '',
            unit_id: '',
            ngay_tt: '',
            ma_tt: '',
            han_tt: '',
            ma_nt: '',
            ty_gia: 0,
            t_tt_nt0: 0,
            t_tt0: 0,
            isTotal: true
          },
          ...data.map((item, index) => ({
            ...item,
            stt: index + 1
          }))
        ]
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
