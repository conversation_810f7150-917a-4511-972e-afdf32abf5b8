import { Pin, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onSearch?: () => void;
  onRefresh?: () => void;
  onFixedColumns?: () => void;
  onExportData?: () => void;
  onEditPrintTemplate?: () => void;
  className?: string;
  tu_ky?: string;
  den_ky?: string;
  tu_nam?: string;
  den_nam?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearch,
  onRefresh,
  onFixedColumns,
  onExportData,
  onEditPrintTemplate,
  className,
  tu_ky,
  den_ky,
  tu_nam,
  den_nam
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>
            Bảng hoạch toán phân bổ CCDC{' '}
            <span className='flex items-center justify-center text-xs font-semibold text-gray-500'>
              <div className='mr-2 size-2 rounded-full bg-red-500' />
              Từ kỳ {tu_ky} năm {tu_nam} đến kỳ {den_ky} năm {den_nam}
            </span>
          </h1>
        </div>
      }
    >
      {onSearch && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearch} variant='primary' />}

      {onRefresh && <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} variant='destructive' />}
      {onFixedColumns && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumns} variant='secondary' />
      )}
      {onExportData && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportData} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplate,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
