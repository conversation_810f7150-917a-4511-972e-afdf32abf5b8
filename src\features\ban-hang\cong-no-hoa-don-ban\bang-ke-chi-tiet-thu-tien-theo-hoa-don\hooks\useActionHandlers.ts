export interface UseActionHandlersReturn {
  handleRefreshClick: () => void;
  handleFixedColumnsClick: () => void;
  handleExportDataClick: () => void;
}

export function useActionHandlers(refreshData: () => Promise<void>): UseActionHandlersReturn {
  const handleRefreshClick = () => {
    console.log('Refresh clicked');
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
  };

  const handleExportDataClick = () => {
    console.log('Export data clicked');
  };

  return {
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  };
}
