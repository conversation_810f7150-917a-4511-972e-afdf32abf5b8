'use client';

import { toolTypeSearchColumns, departmentSearchColumns, toolGroupSearchColumns } from './search-cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import type { LoaiTaiSanCongCu, BoPhanSuDungCCDC, Group } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { useSearchFieldStates } from '../../hooks';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
  searchFieldStates: ReturnType<typeof useSearchFieldStates>;
}

export const DetailTab = ({ formMode, searchFieldStates }: DetailTabProps) => {
  const {
    toolType,
    setToolType,
    department,
    setDepartment,
    toolGroup1,
    setToolGroup1,
    toolGroup2,
    setToolGroup2,
    toolGroup3,
    setToolGroup3
  } = searchFieldStates;

  return (
    <div className='w-full min-w-[40vw] p-3 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-4 md:space-y-6'>
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 font-medium sm:mb-0 sm:w-40 sm:min-w-40'>Loại công cụ</Label>
            <SearchField<LoaiTaiSanCongCu>
              type='text'
              columnDisplay='ma_lts'
              displayRelatedField='ten_lts'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}`}
              searchColumns={toolTypeSearchColumns}
              value={toolType?.ma_lts || ''}
              relatedFieldValue={toolType?.ten_lts || ''}
              onRowSelection={setToolType}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục loại công cụ'
            />
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 font-medium sm:mb-0 sm:w-40 sm:min-w-40'>Bộ phận sử dụng</Label>
            <SearchField<BoPhanSuDungCCDC>
              type='text'
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN_SU_DUNG_CCDC}`}
              searchColumns={departmentSearchColumns}
              value={department?.ma_bp || ''}
              relatedFieldValue={department?.ten_bp || ''}
              onRowSelection={setDepartment}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục bộ phận sử dụng'
            />
          </div>

          <div className='flex flex-col space-y-4'>
            <div className='flex flex-col sm:flex-row sm:items-start'>
              <Label className='mb-2 font-medium sm:mb-0 sm:mt-2 sm:w-40 sm:min-w-40'>Nhóm công cụ 1,2,3</Label>
              <div className='grid w-full grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3'>
                <SearchField<Group>
                  type='text'
                  columnDisplay='ma_nhom'
                  displayRelatedField='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC1`}
                  searchColumns={toolGroupSearchColumns}
                  value={toolGroup1?.ma_nhom || ''}
                  relatedFieldValue={toolGroup1?.ten_phan_nhom || ''}
                  onRowSelection={setToolGroup1}
                  disabled={formMode === 'view'}
                  dialogTitle='Danh mục nhóm công cụ 1'
                />
                <SearchField<Group>
                  type='text'
                  columnDisplay='ma_nhom'
                  displayRelatedField='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC2`}
                  searchColumns={toolGroupSearchColumns}
                  value={toolGroup2?.ma_nhom || ''}
                  relatedFieldValue={toolGroup2?.ten_phan_nhom || ''}
                  onRowSelection={setToolGroup2}
                  disabled={formMode === 'view'}
                  dialogTitle='Danh mục nhóm công cụ 2'
                />
                <SearchField<Group>
                  type='text'
                  columnDisplay='ma_nhom'
                  displayRelatedField='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC3`}
                  searchColumns={toolGroupSearchColumns}
                  value={toolGroup3?.ma_nhom || ''}
                  relatedFieldValue={toolGroup3?.ten_phan_nhom || ''}
                  onRowSelection={setToolGroup3}
                  disabled={formMode === 'view'}
                  dialogTitle='Danh mục nhóm công cụ 3'
                />
              </div>
            </div>
          </div>

          <div className='flex flex-col gap-2 sm:flex-row sm:items-center'>
            <div className='flex flex-col sm:flex-grow sm:flex-row sm:items-center'>
              <Label htmlFor='reportTemplate' className='mb-2 font-medium sm:mb-0 sm:w-40 sm:min-w-40'>
                Mẫu báo cáo
              </Label>
              <FormField
                id='reportTemplate'
                type='select'
                name='reportTemplate'
                disabled={formMode === 'view'}
                options={[
                  {
                    value: 0,
                    label: 'Mẫu tiền chuẩn'
                  },
                  {
                    value: 1,
                    label: 'Mẫu ngoại tệ'
                  }
                ]}
                defaultValue={0}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
