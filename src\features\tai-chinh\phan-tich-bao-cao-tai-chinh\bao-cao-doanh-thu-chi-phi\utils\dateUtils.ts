/**
 * Utility functions for date calculations in revenue expense reports
 */

export interface PeriodColumn {
  field: string;
  headerName: string;
  width: number;
  type?: 'number' | 'string' | 'date' | 'dateTime' | 'boolean' | 'singleSelect' | 'actions';
}

/**
 * Generate period columns based on start date, period type and number of periods
 * @param startDate - Start date in format YYYY-MM-DD
 * @param numberOfPeriods - Number of periods to generate
 * @param periodType - Type of period (0=day, 1=week, 2=month, 3=quarter, 4=halfYear, 5=year)
 * @returns Array of column definitions
 */
export function generatePeriodColumns(
  startDate: string,
  numberOfPeriods: number,
  periodType: number = 2
): PeriodColumn[] {
  if (!startDate || !numberOfPeriods) {
    // Default fallback columns
    return [
      { field: 'apr_amount', headerName: 'Tiền', width: 120, type: 'number' as const },
      { field: 'apr_percent', headerName: '%', width: 80, type: 'number' as const },
      { field: 'may_amount', headerName: 'Tiền', width: 120, type: 'number' as const },
      { field: 'may_percent', headerName: '%', width: 80, type: 'number' as const }
    ];
  }

  const columns: PeriodColumn[] = [];
  const date = new Date(startDate);

  for (let i = 0; i < numberOfPeriods; i++) {
    let currentDate: Date;
    let headerName: string;

    switch (periodType) {
      case 0: // day
        currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + i);
        headerName = `Ngày ${currentDate.getDate()}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
        break;

      case 1: // week
        currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + i * 7);
        const weekStart = currentDate.getDate();
        const weekEnd = weekStart + 6;
        headerName = `Tuần ${weekStart}-${weekEnd}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
        break;

      case 2: // month
        currentDate = new Date(date.getFullYear(), date.getMonth() + i, 1);
        headerName = `Tháng ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
        break;

      case 3: // quarter
        const quarterIndex = Math.floor(date.getMonth() / 3) + i;
        const quarterYear = date.getFullYear() + Math.floor(quarterIndex / 4);
        const quarter = (quarterIndex % 4) + 1;
        headerName = `Quý ${quarter}/${quarterYear}`;
        break;

      case 4: // halfYear
        const halfYearIndex = Math.floor(date.getMonth() / 6) + i;
        const halfYear = date.getFullYear() + Math.floor(halfYearIndex / 2);
        const half = (halfYearIndex % 2) + 1;
        headerName = `Nửa năm ${half}/${halfYear}`;
        break;

      case 5: // year
        const year = date.getFullYear() + i;
        headerName = `Năm ${year}`;
        break;

      default:
        currentDate = new Date(date.getFullYear(), date.getMonth() + i, 1);
        headerName = `Tháng ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
    }

    // Add amount column
    columns.push({
      field: `period_${i + 1}_amount`,
      headerName: 'Tiền',
      width: 120,
      type: 'number' as const
    });

    // Add percent column
    columns.push({
      field: `period_${i + 1}_percent`,
      headerName: '%',
      width: 80,
      type: 'number' as const
    });
  }

  return columns;
}

/**
 * Generate column grouping model for dynamic periods
 * @param startDate - Start date in format YYYY-MM-DD
 * @param numberOfPeriods - Number of periods to generate
 * @param periodType - Type of period (0=day, 1=week, 2=month, 3=quarter, 4=halfYear, 5=year)
 * @returns Array of column group definitions
 */
export function generateColumnGroupingModel(startDate: string, numberOfPeriods: number, periodType: number = 2) {
  const groups = [
    {
      groupId: 'indicatorGroup',
      children: [{ field: 'indicator' }],
      headerName: 'Chỉ tiêu',
      headerAlign: 'center' as const,
      headerClassName: 'font-semibold bg-[#f3f3f3]'
    }
  ];

  if (!startDate || !numberOfPeriods) {
    // Default fallback groups
    groups.push(
      {
        groupId: 'Tháng 4/2025',
        headerName: 'Tháng 4/2025',
        children: [{ field: 'apr_amount' }, { field: 'apr_percent' }],
        headerAlign: 'center' as const,
        headerClassName: 'font-semibold bg-[#f3f3f3]'
      },
      {
        groupId: 'Tháng 5/2025',
        headerName: 'Tháng 5/2025',
        children: [{ field: 'may_amount' }, { field: 'may_percent' }],
        headerAlign: 'center' as const,
        headerClassName: 'font-semibold bg-[#f3f3f3]'
      }
    );
  } else {
    const date = new Date(startDate);

    for (let i = 0; i < numberOfPeriods; i++) {
      let currentDate: Date;
      let headerName: string;

      switch (periodType) {
        case 0: // day
          currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + i);
          headerName = `Ngày ${currentDate.getDate()}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
          break;

        case 1: // week
          currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + i * 7);
          const weekStart = currentDate.getDate();
          const weekEnd = weekStart + 6;
          headerName = `Tuần ${weekStart}-${weekEnd}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
          break;

        case 2: // month
          currentDate = new Date(date.getFullYear(), date.getMonth() + i, 1);
          headerName = `Tháng ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
          break;

        case 3: // quarter
          const quarterIndex = Math.floor(date.getMonth() / 3) + i;
          const quarterYear = date.getFullYear() + Math.floor(quarterIndex / 4);
          const quarter = (quarterIndex % 4) + 1;
          headerName = `Quý ${quarter}/${quarterYear}`;
          break;

        case 4: // halfYear
          const halfYearIndex = Math.floor(date.getMonth() / 6) + i;
          const halfYear = date.getFullYear() + Math.floor(halfYearIndex / 2);
          const half = (halfYearIndex % 2) + 1;
          headerName = `Nửa năm ${half}/${halfYear}`;
          break;

        case 5: // year
          const year = date.getFullYear() + i;
          headerName = `Năm ${year}`;
          break;

        default:
          currentDate = new Date(date.getFullYear(), date.getMonth() + i, 1);
          headerName = `Tháng ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
      }

      groups.push({
        groupId: headerName,
        headerName: headerName,
        children: [{ field: `period_${i + 1}_amount` }, { field: `period_${i + 1}_percent` }],
        headerAlign: 'center' as const,
        headerClassName: 'font-semibold bg-[#f3f3f3]'
      });
    }
  }

  // Add total group
  groups.push({
    groupId: 'Tổng cộng',
    headerName: 'Tổng cộng',
    children: [{ field: 'total_amount' }, { field: 'total_percent' }],
    headerAlign: 'center' as const,
    headerClassName: 'font-semibold bg-[#f3f3f3]'
  });

  return groups;
}

/**
 * Generate mock data with dynamic period fields for revenue expense report
 * @param numberOfPeriods - Number of periods
 * @returns Mock data with dynamic period fields
 */
export function generateDynamicMockData(numberOfPeriods: number) {
  const baseData = [
    {
      id: 1,
      indicator: 'Tổng doanh thu',
      baseAmount: 15000000000,
      basePercent: 100
    },
    {
      id: 2,
      indicator: 'Doanh thu bán hàng và cung cấp dịch vụ',
      baseAmount: 12000000000,
      basePercent: 80
    },
    {
      id: 3,
      indicator: 'Doanh thu hoạt động tài chính',
      baseAmount: 2000000000,
      basePercent: 13.3
    },
    {
      id: 4,
      indicator: 'Thu nhập khác',
      baseAmount: 1000000000,
      basePercent: 6.7
    },
    {
      id: 5,
      indicator: 'Tổng chi phí',
      baseAmount: 12000000000,
      basePercent: 80,
      highlight: true
    },
    {
      id: 6,
      indicator: 'Chi phí giá vốn',
      baseAmount: 7000000000,
      basePercent: 46.7
    },
    {
      id: 7,
      indicator: 'Chi phí bán hàng',
      baseAmount: 2000000000,
      basePercent: 13.3
    },
    {
      id: 8,
      indicator: 'Chi phí quản lý doanh nghiệp',
      baseAmount: 1500000000,
      basePercent: 10
    },
    {
      id: 9,
      indicator: 'Chi phí tài chính',
      baseAmount: 1000000000,
      basePercent: 6.7
    },
    {
      id: 10,
      indicator: 'Chi phí khác',
      baseAmount: 500000000,
      basePercent: 3.3
    },
    {
      id: 11,
      indicator: 'Lợi nhuận',
      baseAmount: 3000000000,
      basePercent: 20
    }
  ];

  return baseData.map(item => {
    const result: any = {
      id: item.id,
      indicator: item.indicator,
      highlight: item.highlight
    };

    let totalAmount = 0;
    let totalPercent = 0;

    // Generate dynamic period data
    for (let i = 1; i <= numberOfPeriods; i++) {
      const amountVariation = item.baseAmount + (Math.random() * 0.2 - 0.1) * item.baseAmount; // ±10% variation
      const percentVariation = item.basePercent + (Math.random() * 0.2 - 0.1) * item.basePercent; // ±10% variation

      result[`period_${i}_amount`] = Math.round(amountVariation);
      result[`period_${i}_percent`] = Math.round(percentVariation * 10) / 10; // Round to 1 decimal place

      totalAmount += result[`period_${i}_amount`];
      totalPercent += result[`period_${i}_percent`];
    }

    result.total_amount = totalAmount;
    result.total_percent = Math.round((totalPercent / numberOfPeriods) * 10) / 10; // Average percent

    return result;
  });
}

/**
 * Format date for display in ActionBar
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Formatted date string
 */
export function formatDateForDisplay(dateString: string): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  } catch {
    return dateString;
  }
}
