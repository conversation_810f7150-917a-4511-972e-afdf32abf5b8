import React, { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useMultiVendorDebtData } from './useMultiVendorDebtData';
import { exportAccountColumns } from '../cols-definition';
import { SearchFormValues } from '../schema';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
  selectedIds: string[];
  handleCheckboxChange: (id: string) => void;
}

export function useTableData(searchParams: SearchFormValues): UseTableDataReturn {
  const { data, isLoading, error, refreshData } = useMultiVendorDebtData(searchParams);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const handleCheckboxChange = (id: string) => {
    setSelectedIds(prev => (prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]));
    console.log('Checkbox clicked:', id);
  };

  const handleOpenViewForm = (obj: any) => {
    console.log('View form:', obj);
  };

  const handleOpenEditForm = (obj: any) => {
    console.log('Edit form:', obj);
  };

  const [tables, setTables] = useState<TableData[]>([
    {
      name: 'Tất cả',
      columns: exportAccountColumns(handleOpenViewForm, handleOpenEditForm, selectedIds, handleCheckboxChange),
      rows: data.filter(row => row.disabled === '1'),
      tabProps: { className: 'whitespace-nowrap' }
    }
  ]);

  React.useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables];
      updatedTables[0] = {
        ...updatedTables[0],
        columns: exportAccountColumns(handleOpenViewForm, handleOpenEditForm, selectedIds, handleCheckboxChange),
        rows: data.filter(row => row.disabled === '1')
      };
      return updatedTables;
    });
  }, [data, selectedIds]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData,
    selectedIds,
    handleCheckboxChange
  };
}
