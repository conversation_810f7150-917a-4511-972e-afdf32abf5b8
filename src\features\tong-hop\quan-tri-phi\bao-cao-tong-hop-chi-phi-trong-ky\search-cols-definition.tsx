import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'account_name', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'parent_account', headerName: 'Tài khoản mẹ', flex: 2 },
  { field: 'ledger_account', headerName: 'Tài khoản sổ cái', flex: 2 },
  { field: 'is_group', headerName: 'Tài khoản chi tiết', checkboxSelection: true, flex: 2 },
  { field: 'account_level', headerName: 'Bậc tài khoản', flex: 2 }
];

export const currencySearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã ngoại tệ', flex: 1 },
  { field: 'currency_name', headerName: 'Tên ngoại tệ', flex: 2 }
];

export const customerSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã khách hàng', flex: 1 },
  { field: 'customer_name', headerName: 'Tên khách hàng', flex: 2 }
];

export const departmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'department_name', headerName: 'Tên bộ phận', flex: 2 }
];

export const documentCodeSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã chứng từ', flex: 1 },
  { field: 'document_code_name', headerName: 'Tên chứng từ', flex: 2 }
];
