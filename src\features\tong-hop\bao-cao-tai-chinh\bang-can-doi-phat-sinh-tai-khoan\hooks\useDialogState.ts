import { useState } from 'react';
import { SearchFormValues } from '../schema';

interface UseDialogStateReturn {
  showData: boolean;
  showSearchDialog: boolean;
  showEditPrintDialog: boolean;
  searchParams: SearchFormValues | null;

  openSearchDialog: () => void;
  closeSearchDialog: () => void;
  openEditPrintDialog: () => void;
  closeEditPrintDialog: () => void;

  handleSearchButtonClick: (params?: SearchFormValues) => void;
  handleViewBookButtonClick: () => void;
  handleRefreshButtonClick: () => void;
  handleFixedColumnsButtonClick: () => void;
  handleEditPrintButtonClick: () => void;
  handleExportButtonClick: () => void;
}

const useDialogState = (clearSelection?: () => void): UseDialogStateReturn => {
  const [showData, setShowData] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showEditPrintDialog, setShowEditPrintDialog] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues | null>(null);

  const openSearchDialog = () => setShowSearchDialog(true);
  const closeSearchDialog = () => setShowSearchDialog(false);
  const openEditPrintDialog = () => setShowEditPrintDialog(true);
  const closeEditPrintDialog = () => setShowEditPrintDialog(false);

  const handleSearchButtonClick = (params?: SearchFormValues) => {
    if (clearSelection) clearSelection();
    if (params) {
      setSearchParams(params);
      setShowData(true);
      closeSearchDialog();
    } else {
      openSearchDialog();
    }
  };

  const handleViewBookButtonClick = () => {
    // TODO: Implement view book
  };

  const handleRefreshButtonClick = () => {
    // TODO: Implement refresh
  };

  const handleFixedColumnsButtonClick = () => {
    // TODO: Implement fixed columns
  };

  const handleEditPrintButtonClick = () => {
    openEditPrintDialog();
    // TODO: Implement edit print
  };

  const handleExportButtonClick = () => {
    // TODO: Implement export
  };

  return {
    showData,
    showSearchDialog,
    showEditPrintDialog,
    searchParams,

    openSearchDialog,
    closeSearchDialog,
    openEditPrintDialog,
    closeEditPrintDialog,

    handleSearchButtonClick,
    handleViewBookButtonClick,
    handleRefreshButtonClick,
    handleFixedColumnsButtonClick,
    handleEditPrintButtonClick,
    handleExportButtonClick
  };
};

export default useDialogState;
