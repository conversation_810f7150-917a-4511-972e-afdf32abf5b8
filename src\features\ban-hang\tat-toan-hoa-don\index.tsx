'use client';

import { useState } from 'react';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import { InitialSearchDialog, ActionBar } from './components';
import { useTatToanHoaDon } from '@/hooks/queries';

export default function TatToanHoaDonPage() {
  const {
    initialSearchDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick
  } = useDialogState();
  const [dates, setDates] = useState<{ fromDate: string; toDate: string } | null>(null);
  const { handleRefreshClick, handleFixedColumnsClick } = useActionHandlers();

  const [subTitle, setSubTitle] = useState<string>('');
  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useTatToanHoaDon(searchParams);
  const { tables, handleRowClick } = useTableData(data);

  const handleSearchWithData = async (values: any) => {
    const fromDate = values.ngay_h_toan_tu || '';
    const toDate = values.ngay_h_toan_den || '';

    setSearchParams(values);
    setSubTitle(`Tất toán hóa đơn từ ${fromDate} đến ${toDate}`);
    handleInitialSearch(values, fetchData);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
        setDates={(fromDate, toDate) => setDates({ fromDate, toDate })}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={handleFixedColumnsClick}
            className='border-b border-gray-200'
            subTitle={subTitle}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
