import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import AritoNotifyModal from '@/components/arito/arito-notify-modal';
import { NhatKyHeThongState } from '../hooks/use-nhat-ky-he-thong';
import AritoIcon from '@/components/custom/arito/icon';
import { useModal } from '../contexts/modal-context';
import { MainTab } from './fillter-tabs/MainTab';

interface ModalsProps {
  state: NhatKyHeThongState;
  initialFormData: any;
  actions: {
    handleCloseFormPrint: () => void;
    handleCloseInitialForm: () => void;
    handleInitialFormSubmit: (data: any) => void;
    handleCloseTimeFilterForm: () => void;
  };
}

export const Modals = ({ state, initialFormData, actions }: ModalsProps) => {
  const { confirmModal, notifyModal, setConfirmModal, setNotifyModal } = useModal();

  return (
    <>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal({ open: false })}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoNotifyModal
        open={notifyModal.open}
        onClose={() => setNotifyModal({ open: false })}
        onConfirm={notifyModal.onConfirm}
        title={notifyModal.title}
        message={notifyModal.message}
      />

      {/* Initial Filter Form - Must be completed before seeing data */}
      <AritoPopupForm<any>
        key='initial-filter-form'
        open={state.showInitialForm}
        onClose={actions.handleCloseInitialForm}
        mode='add'
        title='Điều kiện lọc'
        titleIcon={<AritoIcon icon={12} />}
        initialData={initialFormData}
        onSubmit={actions.handleInitialFormSubmit}
        headerFields={
          <div className='p-4'>
            <MainTab />
          </div>
        }
        maxWidth='md'
        fullWidth={true}
      />
    </>
  );
};
