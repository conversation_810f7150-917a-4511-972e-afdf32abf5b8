import { useCallback, useContext, useEffect, useState } from 'react';
import { IconButton, Menu, MenuItem } from '@mui/material';
import dayjs from 'dayjs';
import { AritoPopupFormContext } from './arito-form/pop-up/arito-popup-form';
import AritoIcon from '@/components/custom/arito/icon';

// Preset type definition
export type DateRangePresets = Record<string, string>;

// Define a new interface for the field configuration
export interface FieldConfig {
  id: string; // Field ID/name to be used in the form
  type?: string; // Optional field type (for future extension)
}

interface AritoRangeSelectProps {
  // Replace specific field names with a list of fields
  fields: FieldConfig[];
  buttonClassName?: string;
  // Required props for customization
  presets: DateRangePresets;
  // Updated callback type to handle multiple fields
  onRangeSelect?: (fieldValues: Record<string, string>) => void;
  // Required preset handler
  onPresetSelect: (
    preset: string,
    formatDate: (date: Date) => string,
    updateFormValues: (fieldValues: Record<string, string>) => void,
    fields: FieldConfig[]
  ) => void;
}

// Define types for our window augmentation
declare global {
  interface Window {
    _LATEST_FORM_STATE?: {
      values: Record<string, any>;
    };
  }
}

export const AritoRangeSelect = ({
  fields,
  buttonClassName,
  presets,
  onRangeSelect,
  onPresetSelect
}: AritoRangeSelectProps) => {
  const [dateRangeMenuAnchor, setDateRangeMenuAnchor] = useState<null | HTMLElement>(null);
  const formContext = useContext(AritoPopupFormContext);

  // Ensure fields is always an array, even if nothing is passed
  const safeFields = Array.isArray(fields) ? fields : [];

  // Store references to the current field values
  const [currentFieldValues, setCurrentFieldValues] = useState<Record<string, string | null>>({});

  // Helper function to format a date
  const formatDate = useCallback((date: Date) => date.toISOString().split('T')[0], []);

  // Helper function to update form values
  const updateFormValues = useCallback(
    (fieldValues: Record<string, string>) => {
      // Update current state
      setCurrentFieldValues(fieldValues);

      // Call the custom handler if provided
      if (onRangeSelect) {
        onRangeSelect(fieldValues);
      }

      // Update React Hook Form if available
      if (formContext?.control) {
        try {
          // Update each field in the form context
          Object.entries(fieldValues).forEach(([fieldId, value]) => {
            if (typeof formContext.control.setValue === 'function') {
              formContext.control.setValue(fieldId, value, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
              });
            }

            if (formContext.control._formValues) {
              formContext.control._formValues[fieldId] = value;
            }

            if (formContext.control._fields && formContext.control._fields[fieldId]) {
              formContext.control._fields[fieldId]._f.value = value;
            }

            if (formContext.control.trigger) {
              formContext.control.trigger(fieldId);
            }
          });
        } catch (error) {
          console.error('Error updating form context:', error);
        }
      }

      // Update DOM elements - using ID for precision
      const updateDateField = (fieldId: string, value: string) => {
        // Helper to create valid CSS selectors
        const createValidSelector = (selector: string): string | null => {
          // If the selector is empty or contains only invalid characters, return null
          if (!selector || /^[0-9]/.test(selector)) {
            return null; // Don't create selectors that start with numbers
          }
          return selector;
        };

        // Define all possible ways to find the input element
        const selectors = [
          // Name attribute match (always valid)
          `input[name="${fieldId}"]`,
          // ID containing the field ID - ensure it's a valid selector
          `[id*="${fieldId}"]`,
          // Attribute selectors for numeric IDs
          `[id="${fieldId}"]`,
          // React-specific patterns
          `[id$="-${fieldId}"]`,
          `[id^="${fieldId}-"]`,
          // Data attributes that might be used
          `[data-field-id="${fieldId}"]`,
          `[data-name="${fieldId}"]`
        ];

        // Add a direct ID selector only if it's valid
        if (!/^[0-9]/.test(fieldId)) {
          selectors.unshift(`#${fieldId}`); // Add valid ID selector at the beginning
        }

        // Add a container selector only if valid
        if (!/^[0-9]/.test(fieldId)) {
          selectors.push(`#${fieldId} input`); // Add valid container selector
        }

        // Try each selector
        for (const selector of selectors) {
          try {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
              elements.forEach(element => {
                if (element.tagName === 'INPUT') {
                  (element as HTMLInputElement).value = value;
                  element.dispatchEvent(new Event('change', { bubbles: true }));
                  element.dispatchEvent(new Event('input', { bubbles: true }));
                }
              });
            }
          } catch (error) {
            // Silent error - invalid selector
          }
        }

        // Try to find element by ID first (direct match - for backward compatibility)
        try {
          const element = document.getElementById(fieldId);
          if (element) {
            if (element.tagName === 'INPUT') {
              // Direct input element
              (element as HTMLInputElement).value = value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              element.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
              // Container element, find visible and hidden inputs
              const textInput = element.querySelector('input[type="text"]');
              if (textInput) {
                (textInput as HTMLInputElement).value = dayjs(value).format('DD/MM/YYYY');
                textInput.dispatchEvent(new Event('change', { bubbles: true }));
              }

              const hiddenInput = element.querySelector('input[type="hidden"]');
              if (hiddenInput) {
                (hiddenInput as HTMLInputElement).value = value;
                hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
              }
            }
            return true;
          }
        } catch (error) {
          // Silent error - element not found
        }

        // Update specifically for React Hook Form inputs using RHF's naming pattern
        try {
          const rhfInputs = document.querySelectorAll(`input[id^="react-hook-form-"][name="${fieldId}"]`);
          if (rhfInputs.length > 0) {
            rhfInputs.forEach(input => {
              (input as HTMLInputElement).value = value;
              input.dispatchEvent(new Event('change', { bubbles: true }));
              input.dispatchEvent(new Event('input', { bubbles: true }));
            });
            return true;
          }
        } catch (error) {
          // Silent error - RHF inputs not found
        }

        // Fallback to finding by name
        try {
          const elementsByName = document.querySelectorAll(`input[name="${fieldId}"]`);
          if (elementsByName.length > 0) {
            elementsByName.forEach(input => {
              const inputEl = input as HTMLInputElement;
              inputEl.value = value;
              inputEl.dispatchEvent(new Event('change', { bubbles: true }));
              inputEl.dispatchEvent(new Event('input', { bubbles: true }));

              // Try to find parent date picker
              const dateComponent = input.closest('.MuiFormControl-root');
              if (dateComponent) {
                const visibleInput = dateComponent.querySelector('input[type="text"]');
                if (visibleInput) {
                  (visibleInput as HTMLInputElement).value = dayjs(value).format('DD/MM/YYYY');
                  visibleInput.dispatchEvent(new Event('change', { bubbles: true }));
                }

                const hiddenInput = dateComponent.querySelector('input[type="hidden"]');
                if (hiddenInput) {
                  (hiddenInput as HTMLInputElement).value = value;
                  hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
                }
              }
            });
            return true;
          }
        } catch (error) {
          // Silent error - elements by name not found
        }

        // Last resort - find field in number inputs specifically
        if (fieldId.includes('period')) {
          try {
            const numberInputs = document.querySelectorAll('input[type="number"]');
            let found = false;

            numberInputs.forEach(input => {
              const inputEl = input as HTMLInputElement;
              if (
                inputEl.id === fieldId ||
                inputEl.name === fieldId ||
                inputEl.id.includes(fieldId) ||
                inputEl.name.includes(fieldId)
              ) {
                inputEl.value = value;
                inputEl.dispatchEvent(new Event('change', { bubbles: true }));
                inputEl.dispatchEvent(new Event('input', { bubbles: true }));
                found = true;
              }
            });

            if (found) return true;
          } catch (error) {
            // Silent error - number inputs not found
          }
        }

        // Last resort - find in form groups
        try {
          const formGroups = document.querySelectorAll('.form-group');
          let found = false;

          formGroups.forEach(group => {
            const inputs = group.querySelectorAll('input');
            inputs.forEach(input => {
              const inputEl = input as HTMLInputElement;
              if (
                inputEl.name === fieldId ||
                inputEl.id === fieldId ||
                inputEl.id.includes(fieldId) ||
                inputEl.name.includes(fieldId)
              ) {
                found = true;
                // Found matching input, update all inputs in this group
                const visibleInput = group.querySelector('input[type="text"]');
                if (visibleInput) {
                  (visibleInput as HTMLInputElement).value = dayjs(value).format('DD/MM/YYYY');
                  visibleInput.dispatchEvent(new Event('change', { bubbles: true }));
                }

                const hiddenInput = group.querySelector('input[type="hidden"]');
                if (hiddenInput) {
                  (hiddenInput as HTMLInputElement).value = value;
                  hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
                }

                // Also directly update this input
                inputEl.value = value;
                inputEl.dispatchEvent(new Event('change', { bubbles: true }));
                inputEl.dispatchEvent(new Event('input', { bubbles: true }));
              }
            });
          });

          if (found) return true;
        } catch (error) {
          // Silent error - form groups not found
        }

        // One last attempt - look for React Hook Form fields with numeric IDs
        try {
          // Special selector for React Hook Form fields with numeric IDs
          const reactFormElements = document.querySelectorAll(`[name="${fieldId}"][id*="react-hook-form"]`);
          if (reactFormElements.length > 0) {
            reactFormElements.forEach(element => {
              if (element.tagName === 'INPUT') {
                (element as HTMLInputElement).value = value;
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.dispatchEvent(new Event('input', { bubbles: true }));
                return true;
              }
            });
          }
        } catch (error) {
          // Silent error - React form elements not found
        }

        return false;
      };

      // Update all fields
      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        updateDateField(fieldId, value);
      });

      // Update global form state if available
      if (window._LATEST_FORM_STATE?.values) {
        Object.entries(fieldValues).forEach(([fieldId, value]) => {
          if (window._LATEST_FORM_STATE && window._LATEST_FORM_STATE.values) {
            window._LATEST_FORM_STATE.values[fieldId] = value;
          }
        });
      }
    },
    [formContext, onRangeSelect]
  );

  // Calculate and set default date range (first and last day of current month)
  useEffect(() => {
    // Only run if we have fields available
    if (safeFields.length === 0) return;

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // Prepare initial field values (assuming first two fields are from/to date)
    const fieldValues: Record<string, string> = {};

    // Default behavior: if we have at least 2 fields, treat them as from/to date range
    if (safeFields.length >= 2) {
      fieldValues[safeFields[0].id] = formatDate(firstDayOfMonth);
      fieldValues[safeFields[1].id] = formatDate(lastDayOfMonth);
    } else if (safeFields.length === 1) {
      // If only one field, set it to today
      fieldValues[safeFields[0].id] = formatDate(today);
    }

    // Initialize additional fields with empty values if any
    safeFields.slice(2).forEach(field => {
      fieldValues[field.id] = '';
    });

    updateFormValues(fieldValues);
  }, [safeFields, formatDate, updateFormValues]);

  // Global form submission interceptor
  useEffect(() => {
    if (Object.keys(currentFieldValues).length === 0) return;

    // Handle global form submission
    const handleGlobalFormSubmit = (e: SubmitEvent) => {
      const form = e.target as HTMLFormElement;
      try {
        // Check if form contains any of our fields
        const fieldIds = safeFields.map(f => f.id);
        const hasNamedElements = fieldIds.some(id => form.elements.namedItem(id));

        if (hasNamedElements) {
          // Patch FormData to include our values
          const originalFormDataMethod = window.FormData;
          window.FormData = function (this: FormData, form?: HTMLFormElement) {
            const formData = new originalFormDataMethod(form);

            // Add all our field values
            Object.entries(currentFieldValues).forEach(([fieldId, value]) => {
              if (value) {
                formData.set(fieldId, value);
              }
            });

            return formData;
          } as any;
          window.FormData.prototype = originalFormDataMethod.prototype;
          setTimeout(() => {
            window.FormData = originalFormDataMethod;
          }, 100);

          // Ensure form inputs exist and have correct values
          const ensureFormInput = (name: string, value: string | null) => {
            if (!value) return;

            let input = form.querySelector(`input[name="${name}"]`) as HTMLInputElement;
            if (!input) {
              input = document.createElement('input');
              input.type = 'hidden';
              input.name = name;
              form.appendChild(input);
            }
            input.value = value;
          };

          // Ensure all our fields exist in the form
          Object.entries(currentFieldValues).forEach(([fieldId, value]) => {
            if (value) {
              ensureFormInput(fieldId, value);
            }
          });
        }
      } catch (error) {
        // Handle error silently
      }
    };

    // Force form state update before submission
    const handleBeforeSubmit = () => {
      if (formContext?.control) {
        try {
          // Update all fields in form context
          Object.entries(currentFieldValues).forEach(([fieldId, value]) => {
            if (value && typeof formContext.control.setValue === 'function') {
              formContext.control.setValue(fieldId, value, {
                shouldValidate: true,
                shouldDirty: true
              });

              if (formContext.control._formValues) {
                formContext.control._formValues[fieldId] = value;

                if (formContext.control._formState?.dirtyFields) {
                  formContext.control._formState.dirtyFields[fieldId] = true;
                }
              }
            }
          });
        } catch (error) {
          // Fallback to direct DOM manipulation
          Object.entries(currentFieldValues).forEach(([fieldId, value]) => {
            if (value) {
              document.querySelectorAll(`input[name="${fieldId}"]`).forEach(input => {
                (input as HTMLInputElement).value = value;
                input.dispatchEvent(new Event('change', { bubbles: true }));
              });
            }
          });
        }
      }
    };

    // Capture form state periodically
    const captureFormState = () => {
      try {
        if (formContext?.control) {
          const values = formContext.control._formValues || {};
          window['_LATEST_FORM_STATE'] = { values };
        }
      } catch (error) {
        // Handle silently
      }
    };

    // Set up event listeners
    document.addEventListener('submit', handleGlobalFormSubmit, true);

    const captureSubmissionEvent = () => {
      document.querySelectorAll('form').forEach(form => {
        form.removeEventListener('submit', handleBeforeSubmit);
        form.addEventListener('submit', handleBeforeSubmit, true);
      });
    };

    captureSubmissionEvent();

    // Set up intervals
    const formStateIntervalId = setInterval(captureFormState, 500);
    const submissionEventIntervalId = setInterval(captureSubmissionEvent, 1000);

    // Cleanup
    return () => {
      document.removeEventListener('submit', handleGlobalFormSubmit, true);
      document.querySelectorAll('form').forEach(form => {
        form.removeEventListener('submit', handleBeforeSubmit, true);
      });
      clearInterval(formStateIntervalId);
      clearInterval(submissionEventIntervalId);
    };
  }, [currentFieldValues, safeFields, formContext]);

  // Menu handlers
  const handleDateRangeMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setDateRangeMenuAnchor(event.currentTarget);
  };

  const handleDateRangeMenuClose = () => {
    setDateRangeMenuAnchor(null);
  };

  // Handle date range preset selection
  const handleDateRangePreset = (preset: string) => {
    onPresetSelect(preset, formatDate, updateFormValues, safeFields);
    setTimeout(() => {
      handleDateRangeMenuClose();
    }, 150);
  };

  return (
    <>
      {/* Hidden inputs to ensure field values are included in form submission */}
      {safeFields.map(field => (
        <input key={field.id} type='hidden' name={field.id} value={currentFieldValues[field.id] || ''} />
      ))}

      <IconButton
        onClick={handleDateRangeMenuOpen}
        size='small'
        className={buttonClassName}
        sx={{
          marginLeft: '8px',
          padding: '4px 8px',
          borderRadius: '0px',
          color: '#0b87c9',
          border: '1px solid transparent',
          cursor: 'pointer',
          '&:hover': {
            border: '1px solid rgb(65, 165, 218, 0.5)',
            backgroundColor: 'rgba(32, 139, 197, 0.08)'
          }
        }}
      >
        <AritoIcon icon={284} />
      </IconButton>
      <Menu
        anchorEl={dateRangeMenuAnchor}
        open={Boolean(dateRangeMenuAnchor)}
        onClose={handleDateRangeMenuClose}
        PaperProps={{
          style: {
            borderRadius: '0px',
            maxHeight: 300,
            width: 200
          }
        }}
      >
        {/* Dynamically generate menu items from presets */}
        {Object.entries(presets).map(([presetKey, presetLabel]) => (
          <MenuItem
            key={presetKey}
            sx={{ fontSize: '12px', fontWeight: 600, cursor: 'pointer' }}
            onClick={() => handleDateRangePreset(presetKey)}
          >
            {presetLabel}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
