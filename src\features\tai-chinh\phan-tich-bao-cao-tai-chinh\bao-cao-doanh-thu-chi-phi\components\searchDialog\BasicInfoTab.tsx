import { FormField } from '@/components/custom/arito/form/form-field';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
}

export const BasicInfoTab = ({ formMode }: BasicInfoProps) => {
  return (
    <div className='p-6'>
      <div className='grid grid-cols-1 gap-x-8 space-y-6'>
        <div className='space-y-6'>
          <FormField
            className='grid grid-cols-[150px,1fr] items-center'
            type='date'
            label='Từ ngày'
            name='from_date'
            disabled={formMode === 'view'}
            defaultValue='2025-05-01'
          />
          <FormField
            className='grid grid-cols-[150px,1fr] items-center'
            type='select'
            label='Loại thời gian'
            name='time_type'
            disabled={formMode === 'view'}
            defaultValue={2}
            options={[
              { label: 'Ngày', value: 0 },
              { label: 'Tuần', value: 1 },
              { label: 'Tháng', value: 2 },
              { label: 'Quý', value: 3 },
              { label: 'N<PERSON><PERSON> Năm', value: 4 },
              { label: 'Năm', value: 5 }
            ]}
          />
          <FormField
            className='grid grid-cols-[150px,1fr] items-center'
            type='number'
            label='Số kỳ'
            name='period'
            disabled={formMode === 'view'}
            defaultValue={4}
          />
        </div>
      </div>
    </div>
  );
};
