import React from 'react';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { FormField } from '@/components/custom/arito/form/form-field';

interface ObjectSearchFieldProps {
  name?: string;
  label?: string;
  className?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  withSearch?: boolean;
  searchEndpoint?: string;
  options?: { value: string; label: string }[];
  labelClassName?: string;
}

// Define the search columns for objects
const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', flex: 1 }
];

export const ObjectSearchField: React.FC<ObjectSearchFieldProps> = ({
  name = 'objectCode',
  label = 'Mã đối tượng',
  className = 'grid grid-cols-[150px,1fr] items-center',
  disabled = false,
  formMode = 'add',
  withSearch = true,
  searchEndpoint = 'accounting/objects',
  options,
  labelClassName
}) => {
  if (withSearch) {
    return (
      <FormField
        className={className}
        label={label}
        name={name}
        type='text'
        searchEndpoint={searchEndpoint}
        searchColumns={objectSearchColumns}
        defaultSearchColumn='ma_doi_tuong'
        disabled={disabled || formMode === 'view'}
        labelClassName={labelClassName}
      />
    );
  } else {
    return (
      <FormField
        className={className}
        label={label}
        name={name}
        type='select'
        options={options || []}
        disabled={disabled || formMode === 'view'}
        labelClassName={labelClassName}
      />
    );
  }
};

export default ObjectSearchField;
