import React from 'react';
import { z } from 'zod';
import { columnAnalysisItems } from '@/features/tai-san/tang-giam-tscd/bang-ke-tscd-het-khau-hao-con-su-dung/cols-definition';
import AritoCreateAnalysisSample from '@/components/custom/arito/create-analysis-sample';
import { FormField } from '@/components/custom/arito/form/form-field';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { Label } from '@/components/ui/label';

interface SaveTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: SaveTemplateFormData) => void;
  templateType: 'filter' | 'analysis';
}

export interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

const SaveTemplateDialog: React.FC<SaveTemplateDialogProps> = ({ open, onClose, onSave, templateType }) => {
  const title = templateType === 'filter' ? 'Lưu mẫu báo cáo' : 'Lưu mới mẫu phân tích';
  const formMode = 'add';

  const handleSubmit = (data: SaveTemplateFormData) => {
    onSave(data);
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
    >
      <div className='max-h-[80vh] w-[800px] min-w-[800px] overflow-y-auto bg-gray-50'>
        <AritoForm
          mode={formMode}
          initialData={{
            templateName: '',
            templateName2: '',
            analysisTemplate: ''
          }}
          onSubmit={handleSubmit}
          schema={saveTemplateSchema}
          hasAritoActionBar={false}
          tabs={
            <div className='w-full space-y-4'>
              <div className='grid w-full grid-cols-1'>
                <div className='rounded-md bg-white p-2'>
                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>
                      {templateType === 'filter' ? 'Nhập tên mẫu mới:' : 'Nhập tên mới mẫu:'}
                    </Label>
                    <div className='flex-1'>
                      <FormField name='templateName' label='' type='text' className='w-full' />
                    </div>
                  </div>

                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên 2:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName2' label='' type='text' className='w-full' />
                    </div>
                  </div>
                </div>

                {templateType === 'analysis' && (
                  <div className='mb-4 border-b border-gray-200'>
                    <AritoHeaderTabs
                      tabs={[
                        {
                          id: 'info',
                          label: 'Mẫu phân tích',
                          component: (
                            <div className='rounded-md bg-white p-2'>
                              <AritoCreateAnalysisSample columnAnalysisItems={columnAnalysisItems} />
                            </div>
                          )
                        }
                      ]}
                    />
                  </div>
                )}
              </div>
            </div>
          }
          bottomBar={<BottomBar mode={formMode} onSubmit={() => {}} onClose={handleClose} />}
          classNameBottomBar='relative'
        />
      </div>
    </AritoDialog>
  );
};

export default SaveTemplateDialog;
