// TypeScript interfaces for SoDoiChieuCongNo (Debt Reconciliation Book)

export interface SoDoiChieuCongNoItem {
  id: string;
  ma_unit: string;
  ngay_ct: string;
  so_ct: string;
  tk: string;
  tk_du: string;
  dien_giai: string;
  ps_no: number;
  ps_co: number;
  du_no: number;
  du_co: number;
  ma_ct: string;
}

export interface SoDoiChieuCongNoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SoDoiChieuCongNoItem[];
}

export interface SoDoiChieuCongNoSearchFormValues {
  ngay_ct1?: Date;
  ngay_ct2?: Date;
  tk?: string;
  ngay_hd1?: Date;
  ngay_hd2?: Date;
  ma_kh?: string;
  so_du?: string;
  mau_bc?: string;
  ngay_bc?: Date;
  report_filtering?: string;
  data_analysis_struct?: string;
  [key: string]: any;
}

export interface UseSoDoiChieuCongNoReturn {
  data: SoDoiChieuCongNoItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SoDoiChieuCongNoSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
