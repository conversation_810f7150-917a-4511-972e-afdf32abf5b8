import { GridColDef } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const xuLyChenhLechSoLieuKiemKeColumns: GridColDef[] = [
  { field: 'don_vi', headerName: 'Đơn vị', width: 100 },
  { field: 'ngay_ctu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'so_ctu', headerName: 'Số c/từ', width: 100 },
  { field: 'dien_giai', headerName: '<PERSON>ễn giải', width: 200 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 100 },
  { field: 'ten_vat_tu', headerName: 'Tê<PERSON> vật tư', width: 150 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'ma_lo', headerName: 'Mã lô', width: 100 },
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 100 },
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', width: 120 },
  { field: 'sl_nhap', headerName: 'Sl nhập', width: 100 },
  { field: 'tien_nhap', headerName: 'Tiền nhập', width: 120 },
  { field: 'sl_xuat', headerName: 'Sl xuất', width: 100 },
  { field: 'tien_xuat', headerName: 'Tiền xuất', width: 120 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 120 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 120 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 120 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 120 },
  { field: 'phi', headerName: 'Phí', width: 100 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 120 },
  { field: 'lenh_sx', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hl', headerName: 'C/p không h/lệ', width: 150 },
  { field: 'ma_ctu', headerName: 'Mã c/từ', width: 120 }
];
export const accountColumns: ExtendedGridColDef[] = [
  {
    field: 'accountCode',
    headerName: 'Mã tài khoản',
    width: 120
  },
  {
    field: 'accountName',
    headerName: 'Tên tài khoản',
    width: 200
  },
  {
    field: 'parentAccount',
    headerName: 'Tài khoản mẹ',
    width: 120
  },
  {
    field: 'isLedgerAccount',
    headerName: 'Tk sổ cái',
    renderCell: AritoCheckboxCellRenderer,
    width: 100
  },
  {
    field: 'isDetailAccount',
    headerName: 'Tk chi tiết',
    renderCell: AritoCheckboxCellRenderer,
    width: 100
  },
  { field: 'accountLevel', headerName: 'Bậc tk', width: 80 }
];

export const getPartnerColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã khách hàng', width: 120 },
  { field: 'customer_name', headerName: 'Tên khách hàng', width: 280 },
  { field: 'receivable', headerName: 'Công nợ p/thu', width: 120 },
  { field: 'payable', headerName: 'Công nợ p/trả', width: 120 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 120 },
  { field: 'email', headerName: 'Email', width: 160 },
  { field: 'phone', headerName: 'Số điện thoại', width: 160 }
];

export const KhoHangSearchColBasicInfo: ExtendedGridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', flex: 1 },
  { field: 'ten_kho', headerName: 'Tên kho', flex: 2 },
  { field: 'don_vi', headerName: 'Đơn vị', flex: 1 },
  { field: 'theo_doi_vi_tri', headerName: 'Theo dõi vị trí', flex: 1 }
];

export const volSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nk', headerName: 'Mã quyển', width: 100 },
  { field: 'ten_nk', headerName: 'Tên quyển', width: 200 },
  { field: 'so_ct_mau', headerName: 'Số khai báo', width: 200 }
];

export const yeuCauKiemKeColumns: ExtendedGridColDef[] = [
  { field: 'so_c_tu', headerName: 'Số c/từ', width: 100 },
  { field: 'ngay_c_tu', headerName: 'Ngày c/từ', width: 100 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 200 }
];
