import { z } from 'zod';

export const SearchFormSchema = z.object({
  // Basic tab
  date: z.string().min(1, '<PERSON><PERSON><PERSON> là bắt buộc'),

  // Detail tab
  selected_report_template: z.string().optional(),
  report_template: z.string().optional(),

  // Other tab
  report_analysis_template: z.string().optional()
});
export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  date: '',
  selected_report_template: 'balance_sheet_tt200',
  report_template: 'standard_currency',
  report_analysis_template: 'balance_sheet_unit'
};

// Balance Sheet Data Types
export interface BalanceSheetItem {
  id: string;
  target: string;
  code: string;
  description: string;
  company: number;
  total: number;
}

export interface BalanceSheetResponse {
  results: BalanceSheetItem[];
  total: number;
}

export interface UseBalanceSheetReturn {
  data: BalanceSheetItem[];
  isLoading: boolean;
  error: Error | null;
  searchParams: SearchFormValues;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
