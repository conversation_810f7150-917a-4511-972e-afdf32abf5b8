import React, { useState, useEffect } from 'react';
import { GridRowParams } from '@mui/x-data-grid';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useBusinessReportData } from './useBusinessReportData';
import { businessReportColumns } from '../cols-definition';
import { SearchFormValues } from '../schema';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData } = useBusinessReportData(searchParams);

  const handleRowClick = (params: GridRowParams) => {
    const reportItem = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
    console.log('Row clicked:', reportItem);
  };

  const tables = [
    {
      name: 'Báo cáo kết quả hoạt động sản xuất kinh doanh theo đối tượng',
      rows: data,
      columns: businessReportColumns
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
