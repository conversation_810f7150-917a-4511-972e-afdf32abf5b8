// TypeScript interfaces for BaoCaoSoSanhNvlThucTeSoVoiDinhMuc (Material Actual vs Standard Comparison Report)

export interface BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem {
  id: string;
  ma_don_vi: string;
  ten_don_vi: string;
  ma_bo_phan: string;
  ten_bo_phan: string;
  so_lenh_san_xuat: string;
  ten_lenh_san_xuat: string;
  ma_vat_tu: string;
  ten_vat_tu: string;
  don_vi_tinh: string;
  so_luong_dinh_muc: number;
  gia_dinh_muc: number;
  tien_dinh_muc: number;
  so_luong_thuc_te: number;
  gia_thuc_te: number;
  tien_thuc_te: number;
  so_luong_chenh_lech: number;
  gia_chenh_lech: number;
  tien_chenh_lech: number;
  ty_le_chenh_lech_sl: number;
  ty_le_chenh_lech_gia: number;
  ty_le_chenh_lech_tien: number;
  ngay_san_xuat?: string;
  ngay_hoan_thanh?: string;
  trang_thai: string;
  ghi_chu?: string;
}

export interface BaoCaoSoSanhNvlThucTeSoVoiDinhMucResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem[];
}

export interface BaoCaoSoSanhNvlThucTeSoVoiDinhMucSearchFormValues {
  fromMonth?: number;
  fromYear?: number;
  toMonth?: number;
  toYear?: number;
  ma_vt?: string;
  ma_bo_phan?: string;
  so_lenh_sx?: string;
  don_vi?: string;
  reportTemplate?: string;
  reportFilterTemplate?: string;
  dataAnalysisTemplate?: string;
  [key: string]: any;
}

export interface UseBaoCaoSoSanhNvlThucTeSoVoiDinhMucReturn {
  data: BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoSoSanhNvlThucTeSoVoiDinhMucSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
