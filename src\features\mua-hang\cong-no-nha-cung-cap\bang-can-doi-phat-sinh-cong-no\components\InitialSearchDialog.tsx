import { Button } from '@mui/material';
import { useState } from 'react';
import { AritoDialog, AritoForm, AritoHeaderTabs, AritoIcon } from '@/components/custom/arito';
import { searchSchema, initialValues } from '../schema';
import { useSearchFieldStates } from '../hooks';
import DetailsTab from './DetailsTab';
import BasicInfo from './BasicInfo';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any, accountData?: any) => void;
  setDates?: (fromDate: string, toDate: string) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch, setDates }) => {
  const searchFieldStates = useSearchFieldStates();
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = (data: any) => {
    const combinedData = {
      ...data,
      ma_tk: searchFieldStates.account?.uuid || '',
      ma_kh: searchFieldStates.customer?.uuid || '',
      nh_kh1: searchFieldStates.customerGroup1?.uuid || '',
      nh_kh2: searchFieldStates.customerGroup2?.uuid || '',
      nh_kh3: searchFieldStates.customerGroup3?.uuid || '',
      ma_khu_vuc: searchFieldStates.region?.uuid || ''
    };
    if (!combinedData.ma_tk) {
      setError('Vui lòng chọn tài khoản');
      return;
    }

    setDates?.(combinedData.fromDate, combinedData.toDate);
    onSearch(combinedData, searchFieldStates.account);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng cân đối phát sinh công nợ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[1000px] overflow-y-auto'>
            <BasicInfo
              searchFieldStates={{ account: searchFieldStates.account, setAccount: searchFieldStates.setAccount }}
            />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: (
                    <DetailsTab
                      searchFieldStates={{
                        customer: searchFieldStates.customer,
                        setCustomer: searchFieldStates.setCustomer,
                        customerGroup1: searchFieldStates.customerGroup1,
                        setCustomerGroup1: searchFieldStates.setCustomerGroup1,
                        customerGroup2: searchFieldStates.customerGroup2,
                        setCustomerGroup2: searchFieldStates.setCustomerGroup2,
                        customerGroup3: searchFieldStates.customerGroup3,
                        setCustomerGroup3: searchFieldStates.setCustomerGroup3,
                        region: searchFieldStates.region,
                        setRegion: searchFieldStates.setRegion
                      }}
                    />
                  )
                }
              ]}
            />
            {error && <div className='m-4 bg-red-50 py-2 pl-5 font-medium text-red-600'>{error}</div>}
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button variant='contained' className='!bg-[#26827C]' type='submit'>
              <AritoIcon icon={884} />
              <span className='ml-1'>Đồng ý</span>
            </Button>
            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} />
              <span className='ml-1'>Huỷ</span>
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
