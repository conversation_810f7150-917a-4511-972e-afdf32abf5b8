import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BaoCaoSoSanhNvlThucTeSoVoiDinhMucItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_don_vi',
            headerName: 'Đơn vị',
            width: 100,
            renderCell: (params: any) => {
              return params.row.ma_don_vi || '';
            }
          },
          { field: 'ma_bo_phan', headerName: 'Bộ phận', width: 100 },
          { field: 'so_lenh_san_xuat', headerName: 'Số LSX', width: 120 },
          { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 100 },
          { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 200 },
          { field: 'don_vi_tinh', headerName: 'ĐVT', width: 80 },
          {
            field: 'so_luong_dinh_muc',
            headerName: 'SL định mức',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              return new Intl.NumberFormat('vi-VN', { minimumFractionDigits: 1 }).format(
                params.row.so_luong_dinh_muc || 0
              );
            }
          },
          {
            field: 'gia_dinh_muc',
            headerName: 'Giá định mức',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              return new Intl.NumberFormat('vi-VN').format(params.row.gia_dinh_muc || 0);
            }
          },
          {
            field: 'tien_dinh_muc',
            headerName: 'Tiền định mức',
            width: 130,
            type: 'number',
            renderCell: (params: any) => {
              return new Intl.NumberFormat('vi-VN').format(params.row.tien_dinh_muc || 0);
            }
          },
          {
            field: 'so_luong_thuc_te',
            headerName: 'SL thực tế',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              return new Intl.NumberFormat('vi-VN', { minimumFractionDigits: 1 }).format(
                params.row.so_luong_thuc_te || 0
              );
            }
          },
          {
            field: 'gia_thuc_te',
            headerName: 'Giá thực tế',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              return new Intl.NumberFormat('vi-VN').format(params.row.gia_thuc_te || 0);
            }
          },
          {
            field: 'tien_thuc_te',
            headerName: 'Tiền thực tế',
            width: 130,
            type: 'number',
            renderCell: (params: any) => {
              return new Intl.NumberFormat('vi-VN').format(params.row.tien_thuc_te || 0);
            }
          },
          {
            field: 'so_luong_chenh_lech',
            headerName: 'SL chênh lệch',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.so_luong_chenh_lech || 0;
              return new Intl.NumberFormat('vi-VN', { minimumFractionDigits: 1, signDisplay: 'always' }).format(value);
            }
          },
          {
            field: 'tien_chenh_lech',
            headerName: 'Tiền chênh lệch',
            width: 130,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.tien_chenh_lech || 0;
              return new Intl.NumberFormat('vi-VN', { signDisplay: 'always' }).format(value);
            }
          },
          {
            field: 'ty_le_chenh_lech_tien',
            headerName: 'Tỷ lệ CL (%)',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.ty_le_chenh_lech_tien || 0;
              return (
                new Intl.NumberFormat('vi-VN', { minimumFractionDigits: 2, signDisplay: 'always' }).format(value) + '%'
              );
            }
          },
          { field: 'trang_thai', headerName: 'Trạng thái', width: 120 }
        ],
        rows: data.map(item => ({
          id: item.id,
          ma_don_vi: item.ma_don_vi,
          ma_bo_phan: item.ma_bo_phan,
          so_lenh_san_xuat: item.so_lenh_san_xuat,
          ma_vat_tu: item.ma_vat_tu,
          ten_vat_tu: item.ten_vat_tu,
          don_vi_tinh: item.don_vi_tinh,
          so_luong_dinh_muc: item.so_luong_dinh_muc,
          gia_dinh_muc: item.gia_dinh_muc,
          tien_dinh_muc: item.tien_dinh_muc,
          so_luong_thuc_te: item.so_luong_thuc_te,
          gia_thuc_te: item.gia_thuc_te,
          tien_thuc_te: item.tien_thuc_te,
          so_luong_chenh_lech: item.so_luong_chenh_lech,
          gia_chenh_lech: item.gia_chenh_lech,
          tien_chenh_lech: item.tien_chenh_lech,
          ty_le_chenh_lech_sl: item.ty_le_chenh_lech_sl,
          ty_le_chenh_lech_gia: item.ty_le_chenh_lech_gia,
          ty_le_chenh_lech_tien: item.ty_le_chenh_lech_tien,
          trang_thai: item.trang_thai,
          ghi_chu: item.ghi_chu
        }))
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
