import { I<PERSON><PERSON>utton } from '@mui/material';
import { PopupFormField } from '@/components/arito/arito-form/pop-up/arito-popup-form-field';
import AritoIcon from '@/components/custom/arito/icon';

export const BasicInformationTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        <div className='space-y-2'>
          <div className='grid grid-cols-[2fr,1fr,3fr] items-center gap-x-4'>
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='number'
              label='Từ kỳ/năm'
              name='tu_ky'
              disabled={formMode === 'view'}
            />
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='number'
              label=''
              name='tu_nam'
              disabled={formMode === 'view'}
            />
          </div>
          <div className='grid grid-cols-[2fr,1fr,3fr] items-center gap-x-4'>
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='number'
              label='Đến kỳ/năm'
              name='den_ky'
              disabled={formMode === 'view'}
            />
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='number'
              label=''
              name='den_nam'
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const mauPhanTichOption = [
  { value: 0, label: 'Tạo mẫu phân tích mới', icon: 7, onClick: () => {} },
  { value: 1, label: 'Sửa mẫu đang chọn', icon: 9, onClick: () => {} },
  { value: 2, label: 'Xoá mẫu đang chọn', icon: 8, onClick: () => {} }
];

export const DetailTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        {/* Thông tin vật tư */}
        <div className='space-y-2'>
          <PopupFormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Mã sản phẩm'
            name='ma_san_pham'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Mã bộ phận'
            name='ma_bo_phan'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Số lệnh sx'
            name='so_lenh_sx'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='select'
            label='Đơn vị'
            name='don_vi'
            options={[{ value: 0, label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }]}
            disabled={formMode === 'view'}
          />
          <div className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[1fr,1fr]'>
            <PopupFormField
              className='grid grid-cols-[160px,1fr] items-center'
              type='select'
              label='Mẫu phân tích DL'
              name='mau_phan_tich_dl'
              options={[
                { value: 0, label: 'Không phân tích' },
                { value: 1, label: 'Báo cáo giá thành và sản phẩm xoay theo nhóm yếu tố' },
                { value: 2, label: 'Báo cáo giá thành và sản phẩm xoay theo nhóm yếu tố (GT đơn vị)' }
              ]}
              disabled={formMode === 'view'}
            />
            <div className='relative'>
              <div className='group relative'>
                <IconButton
                  size='small'
                  color='primary'
                  disabled={formMode === 'view'}
                  className='square-full border border-gray-300'
                  style={{ width: '32px', height: '32px', borderRadius: '4px' }}
                >
                  <AritoIcon icon={790} />
                </IconButton>
                <div className='absolute left-0 z-50 hidden bg-white shadow-lg group-hover:block'>
                  {mauPhanTichOption.map(option => (
                    <button
                      key={option.value}
                      className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'
                      onClick={option.onClick}
                    >
                      {option.icon ? <AritoIcon icon={option.icon} /> : null}
                      <div className='ml-2'>{option.label}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <PopupFormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='select'
            label='Mẫu báo cáo'
            name='mau_bao_cao'
            options={[
              { value: 0, label: 'Mẫu số lượng và tiền' },
              { value: 1, label: 'Mẫu tiền chuẩn' },
              { value: 2, label: 'Mẫu ngoại tệ' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};

const mauLocOption = [
  { value: 0, label: 'Lưu mẫu mới', icon: 7, onClick: () => {} },
  { value: 1, label: 'Lưu đè vào mẫu đang chọn', icon: 75, onClick: () => {} },
  { value: 2, label: 'Xoá mẫu đang chọn', icon: 8, onClick: () => {} }
];

export const OtherTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        <div className='space-y-2'>
          <div className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[1fr,1fr]'>
            <PopupFormField
              className='grid grid-cols-[160px,1fr] items-center'
              type='select'
              label='Mẫu lọc báo cáo'
              name='mau_loc_bao_cao'
              options={[{ value: 0, label: 'Người dùng tự lọc' }]}
              disabled={formMode === 'view'}
            />
            <div className='relative'>
              <div className='group relative'>
                <IconButton
                  size='small'
                  color='primary'
                  disabled={formMode === 'view'}
                  className='square-full border border-gray-300'
                  style={{ width: '32px', height: '32px', borderRadius: '4px' }}
                >
                  <AritoIcon icon={624} />
                </IconButton>
                <div className='absolute left-0 z-50 hidden bg-white shadow-lg group-hover:block'>
                  {mauLocOption.map(option => (
                    <button
                      key={option.value}
                      className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'
                      onClick={option.onClick}
                    >
                      {option.icon ? <AritoIcon icon={option.icon} /> : null}
                      <div className='ml-2'>{option.label}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
