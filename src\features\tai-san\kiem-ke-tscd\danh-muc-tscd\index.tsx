'use client';

import React from 'react';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import { ActionBar, EditPrintTemplateDialog } from './components';

export default function FixedAssetCatalogPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const { tables, handleRowClick, isLoading, error, refreshData } = useTableData(searchParams);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers(refreshData);

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} />
          </div>
        </>
      )}
    </div>
  );
}
