import { useState, useEffect } from 'react';
import { useBudgetReportData } from './useBudgetReportData';
import { getTableColumns } from '../cols-definition';
import { SearchFormValues } from '../schema';

interface TableData {
  name: string;
  columns: any[];
  rows: any[];
}

export function useTableData(searchParams: SearchFormValues | null) {
  const [tables, setTables] = useState<TableData[]>([]);

  // Use the new budget report data hook
  const {
    data: budgetReportData,
    isLoading: loading,
    error
  } = useBudgetReportData(
    searchParams || {
      ky: '5',
      nam: '2025',
      ma_unit: '',
      id_maubc: 'BCNS04',
      mau_bc: '20'
    }
  );

  useEffect(() => {
    if (budgetReportData && budgetReportData.length > 0) {
      const tableData: TableData[] = [
        {
          name: 'Báo cáo ngân sách',
          columns: getTableColumns(),
          rows: budgetReportData
        }
      ];
      setTables(tableData);
    }
  }, [budgetReportData]);

  return { tables, loading, budgetReportData, error };
}
