import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Date range
  ngay_tu: dateLike.optional(),
  ngay_den: dateLike.optional(),

  // Account fields
  tai_khoan: z.string().optional(),
  tai_khoan_doi_ung: z.string().optional(),

  // Supplier fields
  ma_ncc: z.string().optional(),
  ten_ncc: z.string().optional(),

  // Group fields
  nhom_ncc_1: z.string().optional(),
  nhom_ncc_2: z.string().optional(),
  nhom_ncc_3: z.string().optional(),

  // Region
  khu_vuc: z.string().optional(),

  // Report options
  bao_gom_so_du_0: z.boolean().optional(),
  chi_tiet_theo_chung_tu: z.boolean().optional(),
  sap_xep_theo: z.enum(['ma_ncc', 'ten_ncc', 'so_du']).optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_tu: '',
  ngay_den: '',
  tai_khoan: '',
  tai_khoan_doi_ung: '',
  ma_ncc: '',
  ten_ncc: '',
  nhom_ncc_1: '',
  nhom_ncc_2: '',
  nhom_ncc_3: '',
  khu_vuc: '',
  bao_gom_so_du_0: false,
  chi_tiet_theo_chung_tu: false,
  sap_xep_theo: undefined
};
