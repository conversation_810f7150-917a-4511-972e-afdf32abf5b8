'use client';

import { ActionBar, EditPrintTemplateDialog, SearchDialog } from './components';
import { useRowSelection, useDialogState, useBalanceSheetData } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { balanceSheetUnitColumns } from './cols-definition';

export default function BangCanDoiKeToanTheoDonVi() {
  // Data hook for fetching balance sheet data
  const { data, isLoading, error, fetchData, refreshData } = useBalanceSheetData();

  const { selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    initialSearchDialogOpen,
    showTable,
    showEditPrintTemplateDialog,
    showSaveTemplateDialog,
    searchParams,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleCloseEditPrintTemplate,
    handleSaveTemplateClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportClick,
    openSaveTemplateDialog,
    closeSaveTemplateDialog
  } = useDialogState(clearSelection, fetchData, refreshData);

  const tables = [
    {
      name: '',
      rows: data,
      columns: balanceSheetUnitColumns
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      {showEditPrintTemplateDialog && (
        <EditPrintTemplateDialog
          open={showEditPrintTemplateDialog}
          onClose={handleCloseEditPrintTemplate}
          onSave={handleEditPrintTemplateClick}
        />
      )}

      <SearchDialog
        openSearchDialog={initialSearchDialogOpen}
        onCloseSearchDialog={handleInitialSearchClose}
        onSearch={handleInitialSearch}
        openSaveTemplateDialog={showSaveTemplateDialog}
        onOpenSaveTemplateDialog={openSaveTemplateDialog}
        onCloseSaveTemplateDialog={closeSaveTemplateDialog}
        onSaveTemplate={handleSaveTemplateClick}
      />

      {/* Only show data tables and action bar */}
      {showTable && (
        <>
          <ActionBar
            searchParams={searchParams}
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportClick={handleExportClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
          />
          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
