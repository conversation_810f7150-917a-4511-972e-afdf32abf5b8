import { useActionHandlers } from './useActionHandlers';
import { useDialogState } from './useDialogState';
import { useTableData } from './useTableData';

export function useReportData() {
  const dialogState = useDialogState();
  const tableData = useTableData(dialogState.searchParams);
  const actionHandlers = useActionHandlers();

  return {
    initialSearchDialogOpen: dialogState.initialSearchDialogOpen,
    editPrintTemplateDialogOpen: dialogState.editPrintTemplateDialogOpen,
    showTable: dialogState.showTable,
    searchParams: dialogState.searchParams,

    handleInitialSearchClose: dialogState.handleInitialSearchClose,
    handleInitialSearch: dialogState.handleInitialSearch,
    handleSearchClick: dialogState.handleSearchClick,
    handleEditPrintTemplateClick: dialogState.handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog: dialogState.handleClosePrintTemplateDialog,
    handleSavePrintTemplate: dialogState.handleSavePrintTemplate,

    tables: tableData.tables,
    handleRowClick: tableData.handleRowClick,
    isLoading: tableData.isLoading,
    error: tableData.error,
    refreshData: tableData.refreshData,
    selectedRowId: tableData.selectedRowId,

    handleRefreshClick: actionHandlers.handleRefreshClick,
    handleFixedColumnsClick: actionHandlers.handleFixedColumnsClick,
    handleExportDataClick: actionHandlers.handleExportDataClick
  };
}
