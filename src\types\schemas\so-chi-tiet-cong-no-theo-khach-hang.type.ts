// TypeScript interfaces for SoChiTietCongNoTheoKhachHang (Customer Debt Detail Report)

export interface SoChiTietCongNoTheoKhachHangItem {
  id: string;
  ma_unit: string;
  ngay_ct: string;
  so_ct: string;
  ngay_ct0: string;
  so_ct0: string;
  tk: string;
  tk_du: string;
  dien_giai: string;
  ps_no: number;
  ps_co: number;
  ma_ct: string;
  xorder?: number;
  line?: number;
  unit_id?: string;
  ma_bp?: string;
  ma_vv?: string;
  xid?: string;
}

export interface SoChiTietCongNoTheoKhachHangResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SoChiTietCongNoTheoKhachHangItem[];
}

export interface SoChiTietCongNoTheoKhachHangSearchFormValues {
  ngay_ct1?: string;
  ngay_ct2?: string;
  ct_vt?: boolean;
  so_du?: boolean;
  ma_unit?: string;
  groupBy?: string[];
  mau_bc?: number;
  data_analysis_struct?: string;
  nguoi_lap?: string;
  [key: string]: any;
}

export interface UseSoChiTietCongNoTheoKhachHangReturn {
  data: SoChiTietCongNoTheoKhachHangItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SoChiTietCongNoTheoKhachHangSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
