'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { AritoPopupForm } from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { getAllocationJournalEntryColumns } from '../../cols-definition';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import { AritoDataTables } from '@/components/arito/arito-data-tables';
import AritoNotifyModal from '@/components/arito/arito-notify-modal';
import { MoreInfomationTab } from '../MoreInfomationTab';
import AritoIcon from '@/components/custom/arito/icon';
import { accountSchema } from '../../schemas';
import { ActionBar } from '../ActionBar';
import { AddingTab } from '../AddingTab';
import { DetailTab } from '../DetailTab';
export default function AllocationJournalEntryPage({ initialRows }: { initialRows: any[] }) {
  // Process initial data
  const processedRows = initialRows.map(row => ({
    ...row,
    id: row.name // Use the name field as the id
  }));

  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(processedRows.length > 0 ? processedRows[0] : null);
  // Track the selected row index for the DataGrid
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(processedRows.length > 0 ? '0' : null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  const [notifyModal, setNotifyModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });
  //Initial
  const [rows, setRows] = useState<any[]>(processedRows);

  // Debug selected row and row IDs
  useEffect(() => {
    console.log('Selected object:', selectedObj);
    console.log('Selected row index:', selectedRowIndex);
  }, [selectedObj, selectedRowIndex]);

  //Add and Edit

  const handleCloseForm = () => {
    setShowForm(false);
    // Reset currentObj when form is closed
    if (formMode === 'add') {
      setCurrentObj(null);
    }
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');

    // Create empty object based on schema with proper typing
    const emptyObj: Record<string, string> = {
      name: '',
      currency_name: '',
      account_arise_debt: '',
      account_arise_credit: '',
      enabled: '1' // Default to enabled
    };

    setCurrentObj(emptyObj);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const row = params.row as any;
    setSelectedObj(row);
    // The id in params.row is the index as a string
    setSelectedRowIndex(params.row.id.toString());
  };

  const showSuccessModal = (message: string, onOk?: () => void) => {
    setConfirmModal({
      open: true,
      title: 'Thành công',
      message: message,
      onConfirm: () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const showErrorModal = (message: string, onOk?: () => void) => {
    setConfirmModal({
      open: true,
      title: 'Lỗi',
      message: message,
      onConfirm: () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const showNotifyModal = (message: string, onOk?: () => void) => {
    setNotifyModal({
      open: true,
      message: message,
      title: 'Thông báo',
      onConfirm: () => {
        setNotifyModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const handleFormSubmit = async (data: any) => {
    console.log(data);

    // Placeholder implementation - ERPNext functionality removed
    showNotifyModal(
      formMode === 'add' ? 'Tạo bút toán phân bổ thành công' : 'Cập nhật bút toán phân bổ thành công',
      () => {
        window.location.reload();
      }
    );

    setShowForm(false);
  };

  const handleDelete = (obj: any) => {
    setConfirmModal({
      open: true,
      title: 'Cảnh báo',
      message: 'Bạn có chắc chắn muốn xoá bút toán phân bổ này không?',
      onConfirm: async () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        console.log('xoá');
        // Placeholder implementation - ERPNext functionality removed
        showNotifyModal('Xoá bút toán phân bổ thành công', () => {
          window.location.reload();
        });
      }
    });
  };

  const tables = [
    // {
    //   name: "Tất cả",
    //   rows: orderRows,
    //   columns: getAccountColumns(handleOpenViewForm, handleOpenEditForm),
    // },
    {
      name: 'Bút toán kế toán',
      rows: rows.filter(row => row.accountType === 'short_term_assets'),
      columns: getAllocationJournalEntryColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Bút toán vụ việc, công trình',
      rows: rows.filter(row => row.accountType === 'long_term_assets'),
      columns: getAllocationJournalEntryColumns(handleOpenViewForm, handleOpenEditForm)
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoNotifyModal
        open={notifyModal.open}
        onClose={() => setNotifyModal(prev => ({ ...prev, open: false }))}
        onConfirm={notifyModal.onConfirm}
        title={notifyModal.title}
        message={notifyModal.message}
      />

      <AritoPopupForm<any>
        key={`account-form-${formMode}-${showForm}`}
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title={formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa bút toán phân bổ' : 'Xem bút toán phân bổ'}
        titleIcon={<AritoIcon icon={699} />}
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={accountSchema}
        headerFields={<AddingTab formMode={formMode} />}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} />
          },
          {
            id: 'more',
            label: 'Khác',
            component: <MoreInfomationTab formMode={formMode} />
          }
        ]}
        maxWidth='md'
        fullWidth={true}
      />

      <ActionBar
        onAddClick={handleOpenAddForm}
        onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
        onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
        onDeleteClick={() => selectedObj && handleDelete(selectedObj)}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
      />
      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} onRowClick={handleRowClick} selectedRowId={selectedRowIndex || undefined} />
      </div>
    </div>
  );
}
