import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Basic fields
  tu_thang: z.string().min(1, 'Từ tháng không được để trống'),
  den_thang: z.string().min(1, '<PERSON>ến tháng không được để trống'),
  nam: z.string().min(1, 'Năm không được để trống'),
  thue_suat: z.string().optional(),
  tai_khoan_thue: z.string().optional(),
  tai_khoan_doi_ung: z.string().optional(),
  ma_doi_tuong: z.string().optional(),
  cuc_thue: z.string().optional(),
  loai_bao_cao: z.string().optional(),
  tinh_chat_thue: z.string().optional(),
  sap_xep: z.string().optional(),
  phan_loai: z.string().optional(),
  mau_bao_cao: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

// Tax report data types
export interface TaxReportItem {
  id: string;
  stt: number | string;
  ngay_hoa_don: Date | string | null;
  so_hoa_don: string;
  so_chung_tu: string;
  ngay_chung_tu: Date | string | null;
  ky_hieu: string;
  ma_nha_cung_cap: string;
  ten_nha_cung_cap: string;
  ma_so_thue: string;
  dia_chi: string;
  mat_hang: string;
  tien_hang: number | string;
  thue_suat: string;
  tien_thue: number | string;
  tong_tien: number | string;
  ghi_chu: string;
  tai_khoan_no: string;
  tai_khoan_co: string;
  bo_phan: string;
  vu_viec: string;
  hop_dong: string;
  dot_thanh_toan: string;
  khe_uoc: string;
  phi: string;
  san_pham: string;
  lenh_san_xuat: string;
  ma_chung_tu: string;
  isSummary?: boolean;
}

export interface TaxReportResponse {
  results: TaxReportItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UseTaxReportReturn {
  data: TaxReportItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export const initialValues: SearchFormValues = {
  tu_thang: '1',
  den_thang: '12',
  nam: new Date().getFullYear().toString(),
  thue_suat: '',
  tai_khoan_thue: '',
  tai_khoan_doi_ung: '',
  ma_doi_tuong: '',
  cuc_thue: '',
  loai_bao_cao: '',
  tinh_chat_thue: '',
  sap_xep: 'Ngày',
  phan_loai: 'Nhóm theo ký hiệu, số hóa đơn, thuế suất',
  mau_bao_cao: 'Mẫu tiêu chuẩn'
};
