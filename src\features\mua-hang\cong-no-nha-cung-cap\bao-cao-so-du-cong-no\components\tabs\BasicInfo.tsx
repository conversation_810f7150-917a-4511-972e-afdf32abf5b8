import { Controller, useFormContext } from 'react-hook-form';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants/search-columns';
import { Label } from '@/components/ui/label';
import { TaiKhoan } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  searchFieldStates: {
    account: Tai<PERSON>hoan | null;
    setAccount: (account: <PERSON><PERSON><PERSON>an | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  const { control } = useFormContext();

  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày:</Label>
          <FormField name='ngay' type='date' className='w-40' />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản:</Label>
          <SearchField<TaiKhoan>
            columnDisplay={'code'}
            displayRelatedField={'name'}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            value={searchFieldStates.account?.code || ''}
            relatedFieldValue={searchFieldStates.account?.name || ''}
            onRowSelection={searchFieldStates.setAccount}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại số dư:</Label>
          <Controller
            name='loai_so_du'
            control={control}
            defaultValue='begin'
            render={({ field: { onChange, value } }) => (
              <RadioButton
                name='loai_so_du'
                options={[
                  { value: 'begin', label: 'Đầu kỳ' },
                  { value: 'end', label: 'Cuối kỳ' }
                ]}
                defaultValue={value || 'begin'}
                onChange={onChange}
                orientation='horizontal'
              />
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
