import { But<PERSON> } from '@mui/material';
import React from 'react';
import { SearchFormValues, SearchFormSchema, initialSearchValues } from '../../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { BasicInfoTab } from './BasicInfoTab';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: SearchFormValues) => void;

  openSaveTemplateDialog: boolean;
  onOpenSaveTemplateDialog: () => void;
  onCloseSaveTemplateDialog: () => void;
  onSaveTemplate: (data: any) => void;
}

const SearchDialog = ({
  openSearchDialog,
  onCloseSearchDialog,
  onSearch,
  openSaveTemplateDialog,
  onOpenSaveTemplateDialog,
  onCloseSaveTemplateDialog,
  onSaveTemplate
}: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Báo cáo kết quả hoạt động kinh doanh'
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={SearchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <DetailTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: (
                    <OtherTab
                      formMode='add'
                      open={openSaveTemplateDialog}
                      onClose={onCloseSaveTemplateDialog}
                      onOpen={onOpenSaveTemplateDialog}
                      onSave={onSaveTemplate}
                    />
                  )
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
            <Button onClick={onCloseSearchDialog} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
