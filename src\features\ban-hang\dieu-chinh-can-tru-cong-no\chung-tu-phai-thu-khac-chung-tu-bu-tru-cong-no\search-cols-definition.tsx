import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const documentNumberSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_quyen', headerName: 'Tên quyển', flex: 2 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', flex: 1 }
];

export const objectSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', flex: 1 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'tk_so_cai', headerName: 'Tk sổ cái', flex: 1 },
  { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', flex: 1, checkboxSelection: true },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const unitSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'unit_name', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'id', headerName: 'ID', flex: 1 }
];
