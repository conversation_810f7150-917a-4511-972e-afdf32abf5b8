import { useState, useCallback } from 'react';
import {
  BaoCaoSoSanhBanHangHaiKyItem,
  BaoCaoSoSanhBanHangHaiKyResponse,
  BaoCaoSoSanhBanHangHaiKySearchFormValues,
  UseBaoCaoSoSanhBanHangHaiKyReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BaoCaoSoSanhBanHangHaiKyItem[] => {
  const customers = [
    { ma: 'KH001', ten: 'Công ty TNHH ABC Technology', nhom1: 'Doanh nghiệp lớn', nhom2: 'Công nghệ thông tin' },
    { ma: 'KH002', ten: 'Cửa hàng XYZ Trading', nhom1: 'Doanh nghiệp vừa', nhom2: 'Thương mại' },
    { ma: 'KH003', ten: 'Tập đoàn DEF Corporation', nhom1: 'Tập đoàn', nhom2: '<PERSON><PERSON><PERSON> mật' },
    { ma: 'KH004', ten: 'Công ty TNHH GHI Solutions', nhom1: '<PERSON><PERSON><PERSON> nghiệp vừa', nhom2: 'Công nghệ' },
    { ma: 'KH005', ten: 'Siêu thị JKL Mart', nhom1: 'Bán lẻ', nhom2: 'Siêu thị' },
    { ma: 'KH006', ten: 'Nhà máy MNO Manufacturing', nhom1: 'Sản xuất', nhom2: 'Công nghiệp' },
    { ma: 'KH007', ten: 'Trường học PQR Education', nhom1: 'Giáo dục', nhom2: 'Trường học' },
    { ma: 'KH008', ten: 'Bệnh viện STU Medical', nhom1: 'Y tế', nhom2: 'Bệnh viện' }
  ];

  const products = [
    { ma: 'SP001', ten: 'Phần mềm quản lý ERP', nhom1: 'Phần mềm', nhom2: 'ERP', gia: 10000000, gia_von: 8000000 },
    { ma: 'SP002', ten: 'Thiết bị văn phòng', nhom1: 'Thiết bị', nhom2: 'Văn phòng', gia: 2500000, gia_von: 2000000 },
    { ma: 'SP003', ten: 'Hệ thống bảo mật', nhom1: 'Phần mềm', nhom2: 'Bảo mật', gia: 15000000, gia_von: 12000000 },
    { ma: 'SP004', ten: 'Máy tính xách tay', nhom1: 'Thiết bị', nhom2: 'Máy tính', gia: 18000000, gia_von: 15000000 },
    { ma: 'SP005', ten: 'Hệ thống POS', nhom1: 'Phần mềm', nhom2: 'POS', gia: 5000000, gia_von: 4000000 },
    { ma: 'SP006', ten: 'Máy in laser', nhom1: 'Thiết bị', nhom2: 'In ấn', gia: 8000000, gia_von: 6500000 }
  ];

  const employees = [
    { ma: 'NV001', ten: 'Nguyễn Văn A' },
    { ma: 'NV002', ten: 'Trần Thị B' },
    { ma: 'NV003', ten: 'Lê Văn C' },
    { ma: 'NV004', ten: 'Phạm Thị D' },
    { ma: 'NV005', ten: 'Hoàng Văn E' }
  ];

  const warehouses = [
    { ma: 'KHO01', ten: 'Kho chính' },
    { ma: 'KHO02', ten: 'Kho phụ' },
    { ma: 'KHO03', ten: 'Kho thiết bị' }
  ];

  const units = [
    { ma: 'CN', ten: 'Chi nhánh Hà Nội' },
    { ma: 'BN', ten: 'Chi nhánh TP.HCM' },
    { ma: 'DN', ten: 'Chi nhánh Đà Nẵng' }
  ];

  const regions = ['Miền Bắc', 'Miền Trung', 'Miền Nam'];

  const mockData: BaoCaoSoSanhBanHangHaiKyItem[] = [];
  let id = 1;

  // Generate comparison data for customers and products
  customers.forEach(customer => {
    products.forEach(product => {
      // Not every customer buys every product - add some randomness
      if (Math.random() > 0.7) return;

      const employee = employees[Math.floor(Math.random() * employees.length)];
      const warehouse = warehouses[Math.floor(Math.random() * warehouses.length)];
      const unit = units[Math.floor(Math.random() * units.length)];
      const region = regions[Math.floor(Math.random() * regions.length)];

      // Generate current period data
      const so_luong_ky_nay = Math.floor(Math.random() * 20) + 1;
      const gia_ban_ky_nay = product.gia + (Math.random() - 0.5) * product.gia * 0.2;
      const doanh_so_ky_nay = so_luong_ky_nay * gia_ban_ky_nay;
      const thue_ky_nay = doanh_so_ky_nay * 0.1;
      const chiet_khau_ky_nay = doanh_so_ky_nay * (Math.random() * 0.1);
      const doanh_thu_ky_nay = doanh_so_ky_nay - chiet_khau_ky_nay;
      const gia_von_ky_nay = product.gia_von + (Math.random() - 0.5) * product.gia_von * 0.1;
      const tien_von_ky_nay = so_luong_ky_nay * gia_von_ky_nay;
      const lai_ky_nay = doanh_thu_ky_nay - tien_von_ky_nay;

      // Generate previous period data with some variation
      const growthFactor = 0.8 + Math.random() * 0.4; // 80% to 120% of current period
      const so_luong_ky_truoc = Math.floor(so_luong_ky_nay * growthFactor);
      const gia_ban_ky_truoc = gia_ban_ky_nay * (0.9 + Math.random() * 0.2); // ±10% price variation
      const doanh_so_ky_truoc = so_luong_ky_truoc * gia_ban_ky_truoc;
      const thue_ky_truoc = doanh_so_ky_truoc * 0.1;
      const chiet_khau_ky_truoc = doanh_so_ky_truoc * (Math.random() * 0.1);
      const doanh_thu_ky_truoc = doanh_so_ky_truoc - chiet_khau_ky_truoc;
      const gia_von_ky_truoc = gia_von_ky_nay * (0.95 + Math.random() * 0.1);
      const tien_von_ky_truoc = so_luong_ky_truoc * gia_von_ky_truoc;
      const lai_ky_truoc = doanh_thu_ky_truoc - tien_von_ky_truoc;

      // Calculate comparison metrics
      const chenh_lech_so_luong = so_luong_ky_nay - so_luong_ky_truoc;
      const ty_le_so_luong = so_luong_ky_truoc > 0 ? (chenh_lech_so_luong / so_luong_ky_truoc) * 100 : 0;

      const chenh_lech_doanh_so = doanh_so_ky_nay - doanh_so_ky_truoc;
      const ty_le_doanh_so = doanh_so_ky_truoc > 0 ? (chenh_lech_doanh_so / doanh_so_ky_truoc) * 100 : 0;

      const chenh_lech_doanh_thu = doanh_thu_ky_nay - doanh_thu_ky_truoc;
      const ty_le_doanh_thu = doanh_thu_ky_truoc > 0 ? (chenh_lech_doanh_thu / doanh_thu_ky_truoc) * 100 : 0;

      const chenh_lech_lai = lai_ky_nay - lai_ky_truoc;
      const ty_le_lai = lai_ky_truoc > 0 ? (chenh_lech_lai / lai_ky_truoc) * 100 : 0;

      mockData.push({
        id: id.toString(),
        ma_khach_hang: customer.ma,
        ten_khach_hang: customer.ten,
        ma_san_pham: product.ma,
        ten_san_pham: product.ten,
        nhom_khach_hang_1: customer.nhom1,
        nhom_khach_hang_2: customer.nhom2,
        nhom_san_pham_1: product.nhom1,
        nhom_san_pham_2: product.nhom2,

        // Current period
        so_luong_ky_nay: Math.round(so_luong_ky_nay),
        gia_ban_ky_nay: Math.round(gia_ban_ky_nay),
        doanh_so_ky_nay: Math.round(doanh_so_ky_nay),
        thue_ky_nay: Math.round(thue_ky_nay),
        chiet_khau_ky_nay: Math.round(chiet_khau_ky_nay),
        doanh_thu_ky_nay: Math.round(doanh_thu_ky_nay),
        gia_von_ky_nay: Math.round(gia_von_ky_nay),
        tien_von_ky_nay: Math.round(tien_von_ky_nay),
        lai_ky_nay: Math.round(lai_ky_nay),

        // Previous period
        so_luong_ky_truoc: Math.round(so_luong_ky_truoc),
        gia_ban_ky_truoc: Math.round(gia_ban_ky_truoc),
        doanh_so_ky_truoc: Math.round(doanh_so_ky_truoc),
        thue_ky_truoc: Math.round(thue_ky_truoc),
        chiet_khau_ky_truoc: Math.round(chiet_khau_ky_truoc),
        doanh_thu_ky_truoc: Math.round(doanh_thu_ky_truoc),
        gia_von_ky_truoc: Math.round(gia_von_ky_truoc),
        tien_von_ky_truoc: Math.round(tien_von_ky_truoc),
        lai_ky_truoc: Math.round(lai_ky_truoc),

        // Comparison
        chenh_lech_so_luong: Math.round(chenh_lech_so_luong),
        ty_le_so_luong: Math.round(ty_le_so_luong * 100) / 100,
        chenh_lech_doanh_so: Math.round(chenh_lech_doanh_so),
        ty_le_doanh_so: Math.round(ty_le_doanh_so * 100) / 100,
        chenh_lech_doanh_thu: Math.round(chenh_lech_doanh_thu),
        ty_le_doanh_thu: Math.round(ty_le_doanh_thu * 100) / 100,
        chenh_lech_lai: Math.round(chenh_lech_lai),
        ty_le_lai: Math.round(ty_le_lai * 100) / 100,

        // Additional fields
        ma_nhan_vien: employee.ma,
        ten_nhan_vien: employee.ten,
        ma_kho: warehouse.ma,
        ten_kho: warehouse.ten,
        ma_don_vi: unit.ma,
        ten_don_vi: unit.ten,
        khu_vuc: region
      });

      id++;
    });
  });

  return mockData.sort((a, b) => b.ty_le_doanh_thu - a.ty_le_doanh_thu);
};

export function useBaoCaoSoSanhBanHangHaiKy(): UseBaoCaoSoSanhBanHangHaiKyReturn {
  const [data, setData] = useState<BaoCaoSoSanhBanHangHaiKyItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchParams, setSearchParams] = useState<BaoCaoSoSanhBanHangHaiKySearchFormValues>({
    fromDate: '',
    toDate: '',
    previousFromDate: '',
    previousToDate: '',
    reportBy: 'customer',
    groupBy: 'product'
  });

  const fetchData = useCallback(async (searchParams: BaoCaoSoSanhBanHangHaiKySearchFormValues) => {
    setIsLoading(true);
    setError(null);
    setSearchParams(searchParams);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoSoSanhBanHangHaiKyResponse>('/ban-hang/bao-cao-so-sanh-ban-hang-hai-ky/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
