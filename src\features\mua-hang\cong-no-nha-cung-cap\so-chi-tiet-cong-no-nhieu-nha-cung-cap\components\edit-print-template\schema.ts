import { z } from 'zod';

// Schema for the edit print template form
export const editPrintTemplateSchema = z.object({
  paperType: z.string().optional(),
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  otherName: z.string().optional(),
  template: z.string().optional(),
  paperOrientation: z.string().optional(),
  printTitle: z.string().optional(),
  printTitle2: z.string().optional(),
  subTitle: z.string().optional(),
  subTitle2: z.string().optional(),
  bilingualTemplate: z.string().optional(),
  printEmptyLines: z.string().optional(),
  templateType: z.string().optional(),
  fileName: z.string().optional(),
  column1: z.string().optional(),
  column2: z.string().optional(),
  column3: z.string().optional(),
  column4: z.string().optional(),
  column5: z.string().optional(),
  column6: z.string().optional(),
  column7: z.string().optional(),
  column8: z.string().optional()
});

// Initial values for the form
export const initialValues = {
  paperType: '',
  templateName: '',
  otherName: '',
  template: '',
  paperOrientation: 'landscape',
  printTitle: '',
  printTitle2: '',
  subTitle: '',
  subTitle2: '',
  bilingualTemplate: 'false',
  printEmptyLines: 'false',
  templateType: '',
  fileName: '',
  column1: '',
  column2: '',
  column3: '',
  column4: '',
  column5: '',
  column6: '',
  column7: '',
  column8: ''
};
