import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const TaxInformationColumns: GridColDef[] = [
  { field: 'don_vi', headerName: 'Đơn vị', width: 100 },
  { field: 'ten_cong_ty', headerName: 'Tên công ty', width: 300 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 150 },
  { field: 'dia_chi', headerName: 'Địa chỉ', width: 300 }
];

export const taxSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cqt', headerName: 'Mã cơ quan thuế', flex: 1 },
  { field: 'ten_cqt', headerName: 'Tên cơ quan thuế', flex: 1 }
];
