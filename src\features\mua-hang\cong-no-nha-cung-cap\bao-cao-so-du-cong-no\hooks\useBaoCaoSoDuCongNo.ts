import { useState, useCallback } from 'react';
import type {
  BaoCaoSoDuCongNoItem,
  BaoCaoSoDuCongNoResponse,
  BaoCaoSoDuCongNoSearchFormValues,
  UseBaoCaoSoDuCongNoReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BaoCaoSoDuCongNoItem[] => {
  return [
    {
      customer_code: 'KH001',
      customer_name: 'Công ty TNHH ABC',
      du_no: 15000000,
      du_co: 0
    },
    {
      customer_code: 'KH002',
      customer_name: '<PERSON><PERSON><PERSON> hàng Hoa <PERSON>',
      du_no: 0,
      du_co: 5000000
    },
    {
      customer_code: 'KH003',
      customer_name: 'Ông Nguyễn Văn A',
      du_no: 2500000,
      du_co: 0
    },
    {
      customer_code: 'KH004',
      customer_name: '<PERSON><PERSON> Trần Thị B',
      du_no: 0,
      du_co: 1200000
    },
    {
      customer_code: 'KH005',
      customer_name: '<PERSON>ông ty <PERSON> phần XYZ',
      du_no: 8000000,
      du_co: 0
    },
    {
      customer_code: 'KH006',
      customer_name: 'Nhà hàng Hải Sản',
      du_no: 0,
      du_co: 3000000
    },
    {
      customer_code: 'KH007',
      customer_name: 'Doanh nghiệp Tư nhân D',
      du_no: 10000000,
      du_co: 0
    },
    {
      customer_code: 'KH008',
      customer_name: 'Tạp hóa Minh Tâm',
      du_no: 0,
      du_co: 750000
    }
  ];
};
/**
 * Custom hook for managing BaoCaoSoDuCongNo (Customer Debt Balance Report) data
 *
 * This hook provides functionality to fetch customer debt balance report data
 * with mock support for testing and development purposes.
 */
export function useBaoCaoSoDuCongNo(searchParams: BaoCaoSoDuCongNoSearchFormValues): UseBaoCaoSoDuCongNoReturn {
  const [data, setData] = useState<BaoCaoSoDuCongNoItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BaoCaoSoDuCongNoSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoSoDuCongNoResponse>('/ban-hang/cong-no-khach-hang/bao-cao-so-du-cong-no/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
